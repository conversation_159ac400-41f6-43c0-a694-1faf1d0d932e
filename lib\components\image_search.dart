import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/image_compressor.dart';
import 'package:dio/dio.dart';
import 'dart:io';

class ImageSearchWidget extends StatefulWidget {
  final IconData? icon;
  final double? iconSize;
  final Function(String, String) onImageSearch;

  const ImageSearchWidget(
      {super.key,
      required this.onImageSearch,
      this.icon = Icons.camera_alt_sharp,
      this.iconSize = 24});

  @override
  State<ImageSearchWidget> createState() => _ImageSearchWidgetState();
}

class _ImageSearchWidgetState extends State<ImageSearchWidget> {
  final ImagePicker _picker = ImagePicker();

  // 以图搜图功能
  Future<void> onImageUpload() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        // 限制图片质量和大小
        maxWidth: 800,
        imageQuality: 90,
      );

      if (pickedFile != null) {
        // 使用ImageCompressor压缩图片
        final originalFile = File(pickedFile.path);
        final originalSizeKB = originalFile.lengthSync() / 1024;
        print(
            '【图片压缩】原图大小: ${originalSizeKB.toStringAsFixed(2)}KB - ${originalFile.path}');

        // 显示上传进度对话框，移到压缩之前
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              backgroundColor: Colors.white,
              child: Container(
                padding: EdgeInsets.all(20),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在处理图片...',
                      style: TextStyle(color: Colors.black),
                    ),
                  ],
                ),
              ),
            );
          },
        );

        try {
          final compressedFile = await ImageCompressor.compress(
            originalFile,
            options: CompressOptions(),
          );

          final compressedSizeKB = compressedFile.lengthSync() / 1024;
          final isCompressed = originalFile.path != compressedFile.path;
          print(
              '【图片压缩】压缩后大小: ${compressedSizeKB.toStringAsFixed(2)}KB - ${isCompressed ? "已压缩" : "未压缩"}');

          await _uploadImageForSearch(compressedFile);
        } catch (e) {
          // 关闭加载对话框
          try {
            Navigator.of(Get.context!).pop();
          } catch (_) {}

          print('图片压缩失败: $e');
          // 显示错误提示
          ScaffoldMessenger.of(Get.context!).showSnackBar(
            SnackBar(content: Text('图片压缩失败: ${e.toString()}')),
          );
        }
      }
    } catch (e) {
      // 错误处理，确保对话框被关闭
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      print('图片选择失败: $e');
      // 显示错误提示
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('图片选择失败: ${e.toString()}')),
      );
    }
  }

  // 上传图片进行搜索
  Future<void> _uploadImageForSearch(File file) async {
    try {
      final fileStream = file.openRead();

      // 确保文件名以.jpg结尾
      String filename = 'search_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final multipartFile = MultipartFile.fromStream(
        () => fileStream,
        await file.length(),
        filename: filename,
      );

      dynamic res = await ProductAPI.useUploadImage1688(
          FormData.fromMap({'file': multipartFile}));

      // 关闭加载对话框
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      if (res != null && res?['result']?['code'] == 200) {
        final imageId = res['data']?['imageId'];
        final imageUrl = res['data']?['imageUrl'];

        widget.onImageSearch(imageId, imageUrl);
      } else {
        // 显示错误提示
        ScaffoldMessenger.of(Get.context!).showSnackBar(
          SnackBar(content: Text(res?['result']?['message'] ?? '图片上传失败')),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      print('图片上传失败: $e');
      // 显示错误提示
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('图片上传失败: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onImageUpload();
      },
      child: Container(
        padding: EdgeInsets.all(2.sp),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              widget.icon,
              size: widget.iconSize ?? 24.sp,
            )
          ],
        ),
      ),
    );
  }
}

class SearchResult {
  final String title;
  final String imageUrl;
  final double similarity;

  SearchResult({
    required this.title,
    required this.imageUrl,
    required this.similarity,
  });
}
