import 'dart:io';
import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter_image_compress/flutter_image_compress.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;

class CompressOptions {
  final int maxSizeKB;
  final int quality;
  final int maxWidth;
  final int maxHeight;

  CompressOptions({
    this.maxSizeKB = 200,
    this.quality = 80,
    this.maxWidth = 500,
    this.maxHeight = 500,
  });
}

class ImageCompressor {
  static const int DEFAULT_MAX_SIZE = 200;
  static const int DEFAULT_QUALITY = 80;
  static const int DEFAULT_MAX_WIDTH = 500;
  static const int DEFAULT_MAX_HEIGHT = 500;

  /// 压缩图片文件
  /// [file] 输入的图片文件
  /// [options] 压缩选项
  /// 返回压缩后的图片文件
  static Future<File> compress(
    File file, {
    CompressOptions? options,
  }) async {
    final opts = options ?? CompressOptions();
    final fileSizeKB = file.lengthSync() / 1024;

    // 如果文件小于最大尺寸，直接返回
    if (fileSizeKB <= opts.maxSizeKB) {
      return file;
    }

    // 获取图片信息
    final decodedImage = await decodeImageFromList(await file.readAsBytes());
    final width = decodedImage.width;
    final height = decodedImage.height;

    // 计算压缩后的尺寸
    final dimensions =
        _calculateDimensions(width, height, opts.maxWidth, opts.maxHeight);

    // 创建临时文件用于保存压缩后的图片
    final tempDir = await getTemporaryDirectory();
    final targetPath = path.join(tempDir.path,
        'compressed_${DateTime.now().millisecondsSinceEpoch}.jpg');

    // 尝试压缩图片
    final compressedXFile = await FlutterImageCompress.compressAndGetFile(
      file.path,
      targetPath,
      minWidth: dimensions.width.toInt(),
      minHeight: dimensions.height.toInt(),
      quality: opts.quality,
    );

    // 如果压缩失败，返回原始文件
    if (compressedXFile == null) {
      return file;
    }

    // 将XFile转换为File
    final compressedFile = File(compressedXFile.path);
    final compressedSizeKB = compressedFile.lengthSync() / 1024;

    // 如果压缩后仍然超过最大尺寸，继续降低质量压缩
    if (compressedSizeKB > opts.maxSizeKB) {
      int low = 30; // 最低质量
      int high = opts.quality;
      File resultFile = compressedFile;

      int iterations = 0;
      while (high - low > 5 && iterations < 10) {
        iterations++;
        final mid = ((low + high) / 2).floor();

        final tempQualityPath = path.join(tempDir.path,
            'compressed_quality_${DateTime.now().millisecondsSinceEpoch}.jpg');

        final tempXFile = await FlutterImageCompress.compressAndGetFile(
          file.path,
          tempQualityPath,
          minWidth: dimensions.width.toInt(),
          minHeight: dimensions.height.toInt(),
          quality: mid,
        );

        if (tempXFile == null) {
          return resultFile; // 如果进一步压缩失败，返回已压缩的文件
        }

        // 将XFile转换为File
        final tempFile = File(tempXFile.path);
        final tempSizeKB = tempFile.lengthSync() / 1024;

        if (tempSizeKB > opts.maxSizeKB) {
          high = mid;
        } else {
          low = mid;
          resultFile = tempFile;
          break; // 如果已经达到了要求，就跳出循环
        }
      }

      return resultFile;
    }

    return compressedFile;
  }

  /// 计算压缩后的尺寸，保持宽高比
  static Size _calculateDimensions(
    int width,
    int height,
    int maxWidth,
    int maxHeight,
  ) {
    if (width <= 0 || height <= 0) {
      return Size.zero; // 返回默认值
    }

    double newWidth = width.toDouble();
    double newHeight = height.toDouble();

    if (width > maxWidth) {
      newWidth = maxWidth.toDouble();
      newHeight = (height * maxWidth) / width;
    }

    if (newHeight > maxHeight) {
      newHeight = maxHeight.toDouble();
      newWidth = (width * maxHeight) / height;
    }

    return Size(newWidth.floorToDouble(), newHeight.floorToDouble());
  }
}
