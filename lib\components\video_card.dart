import 'package:flutter/material.dart';
import 'package:video_player/video_player.dart';

class VideoCard extends StatefulWidget {
  final double top;
  final String videoUrl;

  const VideoCard({super.key, required this.videoUrl, required this.top});

  @override
  State<VideoCard> createState() => _VideoCardState();
}

class _VideoCardState extends State<VideoCard> {
  late VideoPlayerController _controller;
  bool _isPlaying = false;

  @override
  void initState() {
    super.initState();
    _controller = VideoPlayerController.networkUrl(Uri.parse(widget.videoUrl))
      ..initialize().then((_) {
        // Ensure the first frame is shown after the video is initialized, even before the play button has been pressed.
        setState(() {});
      });

    _controller.addListener(() {
      if (_controller.value.position == _controller.value.duration) {
        setState(() {
          _isPlaying = false;
          _controller.seekTo(Duration.zero);
        });
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _togglePlayPause() {
    setState(() {
      _isPlaying = !_isPlaying;
      _isPlaying ? _controller.play() : _controller.pause();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.black,
      alignment: Alignment.center,
      child: Stack(
        children: [
          GestureDetector(
            onTap: _togglePlayPause,
            child: _controller.value.isInitialized
                ? AspectRatio(
                    aspectRatio: _controller.value.aspectRatio,
                    child: VideoPlayer(_controller),
                  )
                : Container(),
          ),
          Positioned(
              top: widget.top,
              left: 20,
              right: 20,
              child: IconButton(
                onPressed: _togglePlayPause,
                icon: Icon(_isPlaying ? Icons.pause : Icons.play_arrow,
                    size: 32.0, color: Colors.white),
              )),
          Positioned(
            bottom: 10,
            left: 15,
            right: 15,
            child: VideoProgressIndicator(
              _controller,
              padding: EdgeInsets.symmetric(vertical: 10.0),
              allowScrubbing: true,
              colors: VideoProgressColors(
                playedColor: const Color.fromARGB(255, 255, 255, 255),
                backgroundColor: const Color.fromARGB(255, 129, 128, 128),
                bufferedColor: const Color.fromARGB(255, 144, 145, 143),
              ),
            ),
          ),
        ],
      ),
    );
  }
}
