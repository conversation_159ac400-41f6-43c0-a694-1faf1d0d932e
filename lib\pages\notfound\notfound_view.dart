import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';

class NotfoundPage extends StatefulWidget {
  const NotfoundPage({super.key});

  @override
  State<NotfoundPage> createState() => _NotfoundPageState();
}

class _NotfoundPageState extends State<NotfoundPage> {
  @override
  void initState() {
    super.initState();
    Future.delayed(Duration(seconds: 2), () {
      NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
    });
  }

  Future<void> onGotoHome() async {
    await NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
  }

  @override
  Widget build(BuildContext context) {
    return Center(
        child: Transform.scale(
      scale: 3.5,
      child: CircularProgressIndicator(
        backgroundColor: Colors.grey.shade200, // 背景色
        valueColor: AlwaysStoppedAnimation<Color>(
          Colors.primaries[
              (DateTime.now().second ~/ 3) % Colors.primaries.length],
        ),
        strokeWidth: 2.0, // 进度条宽度
      ),
    ));
  }
}
