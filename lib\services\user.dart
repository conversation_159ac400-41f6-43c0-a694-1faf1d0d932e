import 'package:chilat2_mall_app/utils/request.dart';

class UserAPI {
  // 注册
  static Future<dynamic> useRegister(dynamic data) async {
    var response =
        await RequestUtil().post('passport/Auth/register', params: data);

    return response;
  }

  // 登录
  static Future<dynamic> useLogin(dynamic data) async {
    var response =
        await RequestUtil().post('passport/Auth/login', params: data);

    return response;
  }

  // 退出登录
  static Future<dynamic> useLogout(dynamic data) async {
    var response =
        await RequestUtil().post('passport/Auth/logout', params: data);

    return response;
  }

  // 修改密码
  static Future<dynamic> useModifyPassword(dynamic data) async {
    var response =
        await RequestUtil().post('passport/Auth/modifyPassword', params: data);

    return response;
  }

  // 修改密码
  static Future<dynamic> useUpdatePassword(dynamic data) async {
    var response = await RequestUtil().post('passport/User/updatePassword',
        params: data, handleAuthErrors: true);

    return response;
  }

  // 发送验证码
  static Future<dynamic> useSendCaptcha(dynamic data) async {
    var response =
        await RequestUtil().post('passport/Auth/sendCaptcha', params: data);

    return response;
  }

  // 个人中心-获取用户信息
  static Future<dynamic> useUserDetail(dynamic data,
      {bool handleAuthErrors = false}) async {
    var response = await RequestUtil().post('/passport/User/detail',
        params: data, handleAuthErrors: handleAuthErrors);

    return response;
  }

  // 个人中心-询盘列表
  static Future<dynamic> useGoodsLookingList(dynamic data) async {
    var response = await RequestUtil().post(
        '/inquiry/GoodsLookingList/pageList',
        params: data,
        handleAuthErrors: true);

    return response;
  }

  // 个人中心 - 查询所有地址
  static Future<dynamic> useListUserAddress(dynamic data) async {
    var response = await RequestUtil().post('/passport/User/listUserAddress',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 个人中心 - 保存地址
  static Future<dynamic> useSaveUserAddress(dynamic data) async {
    var response = await RequestUtil().post('/passport/User/saveUserAddress',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 个人中心 - 删除地址
  static Future<dynamic> useDeleteUserAddress(dynamic data) async {
    var response = await RequestUtil().post('/passport/User/deleteUserAddress',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 个人中心 - 设为默认地址
  static Future<dynamic> useAddressToDefault(dynamic data) async {
    var response = await RequestUtil().post('/passport/User/addressToDefault',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 个人中心 - 根据国家查询地区
  static Future<dynamic> useListRegionByCountry(dynamic data) async {
    var response = await RequestUtil()
        .post('/basis/Country/listRegionByCountry', params: data);
    return response;
  }

  // 个人中心 - 根据邮编查询地区
  static Future<dynamic> useListRegionByPostcode(dynamic data) async {
    var response = await RequestUtil()
        .post('/basis/Country/listRegionByPostcode', params: data);
    return response;
  }

  // 查询邮箱是否已验证
  static Future<Map<String, dynamic>> useQueryVerifyMailResult(
      Map<String, dynamic> data) async {
    return await RequestUtil().post('/passport/User/queryVerifyMailResult',
        params: data, handleAuthErrors: true);
  }

  // 优惠券-我的优惠券列表查询
  static Future<Map<String, dynamic>> useGetMyCouponDetailList(
      Map<String, dynamic> data) async {
    return await RequestUtil().post(
        '/marketing/CouponDetail/getMyCouponDetailList',
        params: data,
        handleAuthErrors: true);
  }

  // 发送验证邮件
  static Future<Map<String, dynamic>> useSendVerifyMail(
      Map<String, dynamic> data) async {
    return await RequestUtil().post('/passport/User/sendVerifyMail',
        params: data, handleAuthErrors: true);
  }

  // 查询被邀请好友用户的邮箱验证情况
  static Future<Map<String, dynamic>> useInvitedUserMailStatus(
      Map<String, dynamic> data) async {
    return await RequestUtil().post('/passport/User/invitedUserMailStatus',
        params: data, handleAuthErrors: true);
  }
}
