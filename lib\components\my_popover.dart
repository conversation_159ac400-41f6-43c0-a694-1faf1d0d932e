import 'package:flutter/material.dart';

// 三态复选框
class TriStateCheckbox extends StatelessWidget {
  final bool? value;
  final double? width;
  final double? height;
  final ValueChanged<bool?>? onChanged;
  final Color? activeColor;

  const TriStateCheckbox({
    super.key,
    required this.value,
    this.onChanged,
    this.activeColor,
    this.width = 20,
    this.height = 20,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => onChanged?.call(_getNextState(value)),
      child: Container(
        width: width,
        height: height,
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(4),
          border: Border.all(color: Colors.grey.shade300),
          color: _getBackgroundColor(context),
        ),
        child: _getIcon(),
      ),
    );
  }

  bool? _getNextState(bool? current) {
    return current == null
        ? false
        : current
            ? null
            : true;
  }

  Color? _getBackgroundColor(BuildContext context) {
    if (value == null) return activeColor?.withOpacity(0.5);
    return value! ? activeColor : null;
  }

  Widget? _getIcon() {
    if (value == null) {
      return const Icon(Icons.remove, size: 14, color: Colors.white);
    }
    return value!
        ? const Icon(Icons.check, size: 14, color: Colors.white)
        : null;
  }
}
