import 'package:chilat2_mall_app/utils/utils.dart';

class ArticleAPI {
  // 获取BLOG列表
  static Future<dynamic> useGetBlogList(dynamic data) async {
    var response =
        await RequestUtil().post('pages/ArticlePage/getBlogList', params: data);

    return response;
  }

  // 查询文章详情
  static Future<dynamic> useArticleDetail(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/ArticlePage/articleDetail', params: data);

    return response;
  }

  // 查询文章详情
  static Future<dynamic> useListArticleCategory(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/ArticlePage/listArticleCategory', params: data);

    return response;
  }

  // 查询文章详情
  static Future<dynamic> useListArticleByCategoryId(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/ArticlePage/listArticleByCategoryId', params: data);

    return response;
  }
}
