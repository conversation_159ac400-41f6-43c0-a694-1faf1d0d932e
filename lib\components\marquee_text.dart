import 'package:flutter/material.dart';
import 'dart:async';

class MarqueeText extends StatefulWidget {
  final String text;

  const MarqueeText({super.key, required this.text});

  @override
  State<MarqueeText> createState() => _MarqueeTextState();
}

class _MarqueeTextState extends State<MarqueeText> {
  final ScrollController _scrollController = ScrollController();
  Timer? _timer;

  @override
  void initState() {
    super.initState();
    // 延迟一段时间后开始滚动
    Future.delayed(const Duration(milliseconds: 100), () {
      _startMarquee();
    });
  }

  @override
  void dispose() {
    _timer?.cancel();
    _scrollController.dispose();
    super.dispose();
  }

  void _startMarquee() {
    final double maxScroll = _scrollController.position.maxScrollExtent;
    _timer = Timer.periodic(const Duration(milliseconds: 20), (timer) {
      if (_scrollController.offset < maxScroll) {
        _scrollController.animateTo(
          _scrollController.offset + 1,
          duration: const Duration(milliseconds: 20),
          curve: Curves.linear,
        );
      } else {
        // 滚动到末尾后重置位置
        _scrollController.jumpTo(0);
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      controller: _scrollController,
      scrollDirection: Axis.horizontal,
      physics: const NeverScrollableScrollPhysics(),
      child: Text(
        widget.text,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }
}
