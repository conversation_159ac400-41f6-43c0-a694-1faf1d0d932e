import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/services/user.dart';

class EmailValidation extends StatelessWidget {
  final List<dynamic> couponList;

  const EmailValidation({
    Key? key,
    required this.couponList,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final RxBool loading = false.obs;

    // 发送验证邮件
    Future<void> resendVerification() async {
      if (loading.value) return;
      loading.value = true;

      try {
        final res = await UserAPI.useSendVerifyMail({
          'verifyMailScene': 'MY_COUPON_LIST',
        });

        if (res['result']['code'] == 200) {
          Get.snackbar(
            'Success',
            I18n.of(context)?.translate('cm_common.emailSent') ??
                'Verification email sent',
            backgroundColor: Colors.green,
            colorText: Colors.white,
            duration: Duration(seconds: 3),
          );

          if (res['data']['isMailVerified'] == true) {
            Get.forceAppUpdate();
          } else {
            await navigateToEmail();
          }
        } else {
          Get.snackbar(
            'Error',
            res['result']['message'] ??
                I18n.of(context)?.translate('cm_find.errorMessage'),
            backgroundColor: Colors.red[400],
            colorText: Colors.white,
            duration: Duration(seconds: 4),
          );
        }
      } catch (e) {
        Get.snackbar(
          'Error',
          I18n.of(context)?.translate('cm_find.errorMessage') ?? '',
          backgroundColor: Colors.red[400],
          colorText: Colors.white,
          duration: Duration(seconds: 4),
        );
      } finally {
        loading.value = false;
      }
    }

    return Container(
      child: Container(
        padding: EdgeInsets.fromLTRB(7.sp, 36.sp, 7.sp, 36.sp),
        child: Column(
          children: [
            Text(
              I18n.of(context)?.translate('cm_common_verifyEmail') ?? '',
              style: TextStyle(
                color: Color(0xFF333333),
                fontSize: 20.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 16.sp),
              margin: EdgeInsets.symmetric(vertical: 16.sp),
              child: Text(
                I18n.of(context)?.translate('cm_common_unlockBundle') ?? '',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: AppColors.primaryColor,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w500,
                  height: 1.25,
                ),
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 20.sp),
              child: Text(
                I18n.of(context)?.translate('cm_common_verifyEmailPrompt') ??
                    '',
                textAlign: TextAlign.center,
                style: TextStyle(
                  color: Color(0xFF4D4D4D),
                  fontSize: 14.sp,
                  height: 1.4,
                ),
              ),
            ),
            Container(
              margin: EdgeInsets.only(top: 20.sp, bottom: 34.sp),
              padding: EdgeInsets.symmetric(horizontal: 4.sp),
              child: Wrap(
                alignment: WrapAlignment.center,
                spacing: 4.sp,
                runSpacing: 10.sp,
                children: List.generate(couponList.length, (index) {
                  final coupon = couponList[index];
                  return Container(
                    width: 90.sp + 20.sp, // 优惠券宽度 + x计数器宽度
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Container(
                          width: 90.sp,
                          height: 44.sp,
                          decoration: BoxDecoration(
                            image: DecorationImage(
                              image: AssetImage(
                                coupon['couponType'] == 'COUPON_TYPE_COMMISSION'
                                    ? 'assets/images/marketing/commissionCoupon.png'
                                    : 'assets/images/marketing/productCoupon.png',
                              ),
                              fit: BoxFit.cover,
                            ),
                          ),
                          padding:
                              EdgeInsets.fromLTRB(24.sp, 10.sp, 4.sp, 4.sp),
                          child: Column(
                            children: [
                              Container(
                                height: 15.sp,
                                alignment: Alignment.center,
                                margin: EdgeInsets.only(bottom: 4.sp),
                                child:
                                    coupon['couponWay'] == 'COUPON_WAY_DISCOUNT'
                                        ? Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Text(
                                                (coupon['discount'] * 100)
                                                        .toStringAsFixed(0) +
                                                    '%',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w500,
                                                  height: 1.0,
                                                ),
                                              ),
                                              Transform.scale(
                                                scale: 0.84,
                                                alignment: Alignment.topRight,
                                                child: Text(
                                                  I18n.of(context)?.translate(
                                                          'cm_coupon.discount') ??
                                                      '',
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 10.sp,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                            ],
                                          )
                                        : Row(
                                            mainAxisAlignment:
                                                MainAxisAlignment.center,
                                            children: [
                                              Transform.rotate(
                                                angle: -90 * 3.1415927 / 180,
                                                child: Text(
                                                  currencyUnit,
                                                  style: TextStyle(
                                                    color: Colors.white,
                                                    fontSize: 10.sp,
                                                    fontWeight: FontWeight.w500,
                                                  ),
                                                ),
                                              ),
                                              Text(
                                                '\$${setNewUnit(coupon['preferentialAmount'], true)}',
                                                style: TextStyle(
                                                  color: Colors.white,
                                                  fontSize: 14.sp,
                                                  fontWeight: FontWeight.w500,
                                                  height: 1.0,
                                                ),
                                              ),
                                            ],
                                          ),
                              ),
                              Transform.scale(
                                scale: 0.7,
                                alignment: Alignment.topLeft,
                                child: Text(
                                  (coupon['couponType'] == 'COUPON_TYPE_PRODUCT'
                                          ? I18n.of(context)?.translate(
                                                  'cm_coupon.productCoupon') ??
                                              ''
                                          : I18n.of(context)?.translate(
                                                  'cm_coupon.commissionCoupon') ??
                                              '')
                                      .toUpperCase(),
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 10.sp,
                                    height: 1.0,
                                    letterSpacing: -0.5,
                                  ),
                                  softWrap: false,
                                  overflow: TextOverflow.visible,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(width: 2.sp),
                        Container(
                          child: Row(
                            children: [
                              Text(
                                'x',
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                  letterSpacing: -1.5,
                                  fontSize: 15.sp,
                                  height: 1.0,
                                ),
                              ),
                              SizedBox(width: 2.sp),
                              Text(
                                '${coupon['count']}',
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                  letterSpacing: -1.5,
                                  fontSize: 15.sp,
                                  height: 1.0,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  );
                }),
              ),
            ),
            Obx(() => ElevatedButton(
                  onPressed: loading.value ? null : resendVerification,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    padding: EdgeInsets.symmetric(horizontal: 20.sp),
                    minimumSize: Size(200.sp, 38.sp),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(100),
                    ),
                  ),
                  child: loading.value
                      ? SizedBox(
                          width: 10.sp,
                          height: 10.sp,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                          ),
                        )
                      : Text(
                          I18n.of(context)
                                  ?.translate('cm_common_emailActivateNow') ??
                              '',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 18.sp,
                          ),
                        ),
                )),
          ],
        ),
      ),
    );
  }
}
