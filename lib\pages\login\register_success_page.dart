import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter/gestures.dart';
import 'package:get/get.dart';

import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/config/config.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/utils/local_storage.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:chilat2_mall_app/styles/colors.dart';

class RegisterSuccessPage extends StatefulWidget {
  const RegisterSuccessPage({super.key});

  @override
  State<RegisterSuccessPage> createState() => _RegisterSuccessPageState();
}

class _RegisterSuccessPageState extends State<RegisterSuccessPage> {
  final Map<String, dynamic> pageData = {
    'expireHour': 0,
    'couponList': <Map<String, dynamic>>[],
  };

  Map<String, dynamic>? userInfo;

  @override
  void initState() {
    super.initState();
    _loadUserInfo();
    _queryVerifyMailResult();
  }

  // 加载用户信息
  void _loadUserInfo() {
    userInfo = LocalStorage().getJSON(USER_INFO);
  }

  // 查询邮箱是否已验证及验证后可以得到的优惠券
  Future<void> _queryVerifyMailResult({String? type}) async {
    try {
      final res = await UserAPI.useQueryVerifyMailResult({
        'email': userInfo?['email'],
        'isNeedCoupon': true,
        'verifyMailScene': 'REGISTER',
      });

      if (res['result']['code'] == 200) {
        setState(() {
          pageData['expireHour'] = res['data']['expireHour'] ?? 0;
          pageData['couponList'] = res['data']['couponList'] ?? [];
        });

        if (res['data']['isMailVerified'] == true) {
          if (type == 'verifyMail') {
            Get.offAllNamed(AppRoutes.MineCouponPage);
          } else {
            Get.toNamed(AppRoutes.MineCouponPage);
          }
        } else if (type == 'verifyMail') {
          await navigateToEmail();
        }
      } else {
        Get.snackbar(
            '',
            res['result']['message'] ??
                I18n.of(context)?.translate('cm_find.errorMessage') ??
                '');
      }
    } catch (e) {
      print('Error querying verify mail result: $e');
      Get.snackbar(
          '', I18n.of(context)?.translate('cm_find.errorMessage') ?? '');
    }
  }

  // 发送验证邮箱的邮件
  Future<void> _resendVerification() async {
    try {
      final res = await UserAPI.useSendVerifyMail({
        'verifyMailScene': 'REGISTER',
      });

      if (res['result']['code'] == 200) {
        if (res['data']['isMailVerified'] == true) {
          Get.offAllNamed(AppRoutes.MineCouponPage);
        } else {
          Get.snackbar(
            '',
            I18n.of(context)?.translate('cm_common.resendSuccess') ?? '',
            snackPosition: SnackPosition.TOP,
            titleText: const SizedBox.shrink(),
          );
        }
      } else {
        Get.snackbar(
          '',
          res['result']['message'] ??
              I18n.of(context)?.translate('cm_find.errorMessage') ??
              '',
          snackPosition: SnackPosition.TOP,
          titleText: const SizedBox.shrink(),
        );
      }
    } catch (e) {
      Get.snackbar(
          '', I18n.of(context)?.translate('cm_find.errorMessage') ?? '');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      body: SafeArea(
        child: Column(
          children: [
            const SearchHeader(
              showHomeIcon: true,
            ),
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding:
                      EdgeInsets.symmetric(horizontal: 10.sp, vertical: 24.sp),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 头部信息
                      Row(
                        children: [
                          Icon(
                            Icons.check_circle,
                            color: Color(0xFF4CAF50), // 绿色
                            size: 42.sp,
                          ),
                          SizedBox(width: 8.sp),
                          Text(
                            I18n.of(context)
                                    ?.translate('cm_common.thankYouRegister') ??
                                '感谢您的注册',
                            style: TextStyle(
                              fontSize: 23.sp,
                              fontWeight: FontWeight.w600,
                              color: Color(0xFF333333),
                            ),
                            softWrap: true,
                            overflow: TextOverflow.visible,
                            maxLines: null,
                          ),
                        ],
                      ),
                      SizedBox(height: 16.sp),

                      // 注册成功信息
                      Text(
                        I18n.of(context)
                                ?.translate('cm_common.regSuccessStart') ??
                            '您已成功注册',
                        style: TextStyle(
                          fontSize: 16.sp,
                          color: Color(0xFF333333),
                        ),
                        softWrap: true,
                        overflow: TextOverflow.visible,
                        maxLines: null,
                      ),
                      SizedBox(height: 10.sp),

                      Text.rich(
                        TextSpan(
                          children: [
                            TextSpan(
                              text: (I18n.of(context)?.translate(
                                              'cm_common.checkQuickGuide') ??
                                          '') +
                                      ' ' ??
                                  '',
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: I18n.of(context)
                                      ?.translate('cm_news.quickGuide') ??
                                  '',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Color(0xFFE50113),
                              ),
                              recognizer: TapGestureRecognizer()
                                ..onTap =
                                    () => Get.toNamed(AppRoutes.QuickGuide),
                            ),
                            TextSpan(
                              text: '.',
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xFF333333)),
                            ),
                          ],
                        ),
                        softWrap: true,
                        overflow: TextOverflow.visible,
                        maxLines: null,
                      ),
                      SizedBox(height: 10.sp),
                      Text.rich(
                        TextSpan(
                          children: [
                            if ((pageData['couponList'] as List)
                                .isNotEmpty) ...[
                              TextSpan(
                                text: (I18n.of(context)?.translate(
                                            'cm_common.activationReward') ??
                                        '') +
                                    ': ',
                                style: TextStyle(
                                    fontSize: 16.sp, color: Color(0xFF333333)),
                              ),
                              ..._buildCouponInlineSpansWithDot(),
                            ] else ...[
                              TextSpan(
                                text: I18n.of(context)
                                        ?.translate('cm_nota.emailActivate') ??
                                    '',
                                style: TextStyle(
                                    fontSize: 16.sp, color: Color(0xFF333333)),
                              ),
                            ],
                            TextSpan(
                              text: I18n.of(context)?.translate(
                                      'cm_common.activationEmail') ??
                                  '',
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: userInfo?['username'] ?? '',
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: ', ' +
                                  (I18n.of(context)?.translate(
                                          'cm_common.activationValidTime') ??
                                      ''),
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: ' ${pageData['expireHour']} ',
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: pageData['expireHour'] > 1
                                  ? (I18n.of(context)?.translate(
                                          'cm_common.activationTimeUnits') ??
                                      'horas')
                                  : (I18n.of(context)?.translate(
                                          'cm_common.activationTimeUnit') ??
                                      'hora.'),
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  fontWeight: FontWeight.w600,
                                  color: Color(0xFF333333)),
                            ),
                            TextSpan(
                              text: '.',
                              style: TextStyle(
                                  fontSize: 16.sp, color: Color(0xFF333333)),
                            ),
                          ],
                        ),
                        softWrap: true,
                        overflow: TextOverflow.visible,
                      ),
                      SizedBox(height: 20.sp),

                      // 按钮区域
                      Row(
                        children: [
                          ElevatedButton(
                            onPressed: () =>
                                _queryVerifyMailResult(type: 'verifyMail'),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryColor,
                              foregroundColor: Colors.white,
                              padding: EdgeInsets.symmetric(
                                  horizontal: 24.sp, vertical: 0.sp),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4.sp),
                              ),
                              minimumSize: Size(0, 38.sp),
                            ),
                            child: Text(
                              I18n.of(context)
                                      ?.translate('cm_common_emailActivate') ??
                                  'Activar',
                              style: TextStyle(
                                  fontSize: 15.sp, fontWeight: FontWeight.w500),
                            ),
                          ),
                          SizedBox(width: 12.sp),
                          Text(
                            I18n.of(context)?.translate('cm_common.or') ?? 'o',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: Color(0xFF333333),
                            ),
                          ),
                          TextButton(
                            onPressed: () => NavigatorUtil.pushNamed(
                                context, AppRoutes.HomePage),
                            child: Text(
                              I18n.of(context)?.translate(
                                      'cm_common.skipToMyChilatshop') ??
                                  'ignorar e ir a Mi Chilatshop',
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ],
                      ),

                      // 分隔线
                      SizedBox(height: 20.sp),
                      Divider(height: 1.sp, color: Color(0xFFEEEEEE)),
                      SizedBox(height: 16.sp),

                      // 验证邮箱说明
                      Text(
                        I18n.of(context)
                                ?.translate('cm_common.verificationEmail') ??
                            '¿No recibiste el correo electrónico de verificación?',
                        style: TextStyle(
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                          color: Color(0xFF333333),
                        ),
                      ),
                      SizedBox(height: 12.sp),

                      // 验证邮箱提示列表
                      _buildListItem(I18n.of(context)
                              ?.translate('cm_common.spamCheck') ??
                          'El correo de verificación de Chilatshop podría ser marcado como spam. Por favor, revisa tu bandeja de correo no deseado.'),
                      SizedBox(height: 8.sp),
                      _buildListItem(I18n.of(context)
                              ?.translate('cm_common.deliveryDelay') ??
                          'La entrega podría retrasarse unos minutos. Por favor, espera pacientemente.'),
                      SizedBox(height: 8.sp),
                      _buildListItemWithAction(
                        I18n.of(context)
                                ?.translate('cm_common.verificationDelay') ??
                            'El correo de verificación podría tardar unos minutos en llegar. Si no lo recibes después de un tiempo, verifica si ha sido marcado como spam ',
                        I18n.of(context)
                                ?.translate('cm_common.resendVerification') ??
                            'o vuelve a enviar el correo de verificación.',
                        _resendVerification,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 构建优惠券样式的 InlineSpan 列表，最后一个拼接点号
  List<InlineSpan> _buildCouponInlineSpansWithDot() {
    final couponList = pageData['couponList'] as List;
    List<InlineSpan> result = [];
    for (int i = 0; i < couponList.length; i++) {
      final coupon = couponList[i];
      result.add(
        TextSpan(
          text: coupon['couponName'] ?? '',
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
      );
      result.add(
        TextSpan(
          text: 'x${coupon['count'] ?? 0}',
          style: TextStyle(
            fontSize: 15.sp,
            fontWeight: FontWeight.w500,
            color: Color(0xFF333333),
          ),
        ),
      );
      if (i < couponList.length - 1) {
        result.add(
          TextSpan(
            text: ', ',
            style: TextStyle(
              fontSize: 15.sp,
              color: Color(0xFF555555),
            ),
          ),
        );
      } else {
        result.add(
          TextSpan(
            text: '. ',
            style: TextStyle(
              fontSize: 15.sp,
              color: Color(0xFF333333),
            ),
          ),
        );
      }
    }
    return result;
  }

  // 构建无点击事件的列表项
  Widget _buildListItem(String text) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('• ', style: TextStyle(fontSize: 14.sp, color: Color(0xFF333333))),
        Expanded(
          child: Text(
            text,
            style: TextStyle(
              fontSize: 14.sp,
              color: Color(0xFF333333),
            ),
          ),
        ),
      ],
    );
  }

  // 构建带点击事件的列表项
  Widget _buildListItemWithAction(
      String text, String actionText, VoidCallback onTap) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text('• ', style: TextStyle(fontSize: 14.sp, color: Color(0xFF333333))),
        Expanded(
          child: RichText(
            text: TextSpan(
              style: TextStyle(fontSize: 14.sp, color: Color(0xFF333333)),
              children: [
                TextSpan(text: text + ' '),
                TextSpan(
                  text: actionText,
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Color(0xFF636DED),
                    decoration: TextDecoration.none,
                  ),
                  recognizer: TapGestureRecognizer()..onTap = onTap,
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}
