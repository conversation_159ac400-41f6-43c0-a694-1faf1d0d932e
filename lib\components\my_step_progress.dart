import 'package:chilat2_mall_app/models/common.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';

class MyStepProgress extends StatefulWidget {
  final int currentStep;
  final Color activateColor; // 激活颜色
  final Color inactiveColor; // 未激活颜色
  final double lineThickness; // 线段粗细
  final double circleSize; // 圆形直径
  final TextStyle? textStyle; // 文字样式
  final List<StepItem>? steps;
  const MyStepProgress({
    super.key,
    required this.currentStep,
    this.activateColor = AppColors.primaryColor,
    this.inactiveColor = Colors.grey,
    this.lineThickness = 1.0,
    this.circleSize = 16.0,
    this.textStyle,
    this.steps,
  });

  @override
  State<MyStepProgress> createState() => _MyStepProgressState();
}

class _MyStepProgressState extends State<MyStepProgress> {
  double screenWidth = 0.0;
  late List<StepItem> _steps;

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    _steps = widget.steps ??
        [
          StepItem(
              title: '1',
              description: I18n.of(context)!.translate("cm_common.step1")),
          StepItem(
              title: '2',
              description: I18n.of(context)!.translate("cm_common.step2")),
          StepItem(
              title: '3',
              description: I18n.of(context)!.translate("cm_common.step3")),
        ];

    return Container(
      height: 58,
      width: screenWidth,
      margin: EdgeInsets.only(top: 12, bottom: 2, left: 2, right: 2),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: _steps.length,
        itemBuilder: (context, index) {
          dynamic step = _steps[index];
          return _stepProgressWidget(step, index == widget.currentStep);
        },
      ),
    );
  }

  Widget _stepProgressWidget(StepItem step, bool selected) {
    return Container(
      width: screenWidth / _steps.length,
      padding: EdgeInsets.symmetric(vertical: 2),
      child: Column(
        children: [
          Row(mainAxisAlignment: MainAxisAlignment.center, children: [
            Expanded(
              child: Container(
                height: 2.5,
                color: selected ? widget.activateColor : widget.inactiveColor,
              ),
            ),
            Container(
              width: widget.circleSize,
              height: widget.circleSize,
              decoration: BoxDecoration(
                color: selected ? widget.activateColor : widget.inactiveColor,
                shape: BoxShape.circle,
              ),
              child: Center(
                child: Text(
                  step.title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 12,
                  ),
                ),
              ),
            ),
            Expanded(
              child: Container(
                height: 2.5,
                color: selected ? widget.activateColor : widget.inactiveColor,
              ),
            ),
          ]),
          Container(
            width: screenWidth / _steps.length,
            padding: EdgeInsets.symmetric(vertical: 2, horizontal: 2),
            child: Center(
              child: Text(
                step.description,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
                style: TextStyle(fontSize: 12),
              ),
            ),
          )
        ],
      ),
    );
  }
}

class OrderProgressBar extends StatefulWidget {
  final int currentStep;
  const OrderProgressBar({super.key, required this.currentStep});

  @override
  State<OrderProgressBar> createState() => _OrderProgressBarState();
}

class _OrderProgressBarState extends State<OrderProgressBar> {
  final ScrollController _scrollController = ScrollController();
  bool _showScrollArrow = false; // 控制右侧滚动箭头显示

  @override
  void initState() {
    super.initState();
    // 监听滚动事件，更新箭头显示状态
    _scrollController.addListener(_updateScrollArrow);

    // 初始渲染后检查一次滚动位置
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _updateScrollArrow();
    });
  }

  @override
  void dispose() {
    _scrollController.removeListener(_updateScrollArrow);
    _scrollController.dispose();
    super.dispose();
  }

  // 更新滚动箭头显示状态
  void _updateScrollArrow() {
    if (!_scrollController.hasClients) return;

    final maxScroll = _scrollController.position.maxScrollExtent;
    final currentScroll = _scrollController.offset;

    // 当未滚动到最右侧（留5px误差）时显示箭头
    setState(() {
      _showScrollArrow = currentScroll < maxScroll - 5;
    });
  }

  // 点击箭头滚动到最右侧
  void _scrollToEnd() {
    if (!_scrollController.hasClients) return;

    _scrollController.animateTo(
      _scrollController.position.maxScrollExtent,
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    // 计算右侧区域总宽度（最右侧图标位置 + 图标宽度）

    return Container(
      height: 128.sp,
      padding: EdgeInsets.symmetric(vertical: 4.sp, horizontal: 8.sp),
      child: Stack(
        children: [
          // 水平滚动内容
          SingleChildScrollView(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                // 左侧三个图标（带红线连接）
                _buildLeftSection(),
                // 右侧背景图上的不等距图标
                _buildRightSection(),

                // 右侧留白
                SizedBox(width: 18.sp),
              ],
            ),
          ),

          // 右侧滚动箭头（半透明背景）
          Visibility(
              visible: _showScrollArrow,
              child: Positioned(
                right: -10,
                top: -40,
                bottom: 0,
                width: 48,
                child: GestureDetector(
                  onTap: _scrollToEnd,
                  child: Container(
                    decoration: const BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          Color.fromRGBO(255, 255, 255, 0.9),
                          Color.fromRGBO(255, 255, 255, 0.5),
                          Color.fromRGBO(255, 255, 255, 0),
                        ],
                        stops: [0, 0.5, 1],
                        begin: Alignment.centerRight,
                        end: Alignment.centerLeft,
                      ),
                    ),
                    child: Align(
                      alignment: Alignment.centerRight,
                      child: Padding(
                        padding: EdgeInsets.only(right: 4.sp),
                        child: Icon(
                          Icons.arrow_forward_ios_rounded,
                          color: Colors.black38,
                          size: 28,
                        ),
                      ),
                    ),
                  ),
                ),
              ))
        ],
      ),
    );
  }

  // 构建左侧带红线连接的图标组
  Widget _buildLeftSection() {
    return Container(
      padding: EdgeInsets.only(top: 9.sp),
      child: Stack(
        children: [
          Row(
            children: [
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 100.sp,
                    child: Row(
                      children: [
                        Center(
                          child: Container(
                            padding: EdgeInsets.symmetric(vertical: 4.sp),
                            child: SvgPicture.asset(
                              "assets/icons/find/check-circle.svg",
                              // SVG大小为圆形的60%，可根据需要调整
                              width: 28.sp,
                              height: 28.sp,
                              // 确保SVG在圆形内完整显示
                              fit: BoxFit.contain,
                            ),
                          ),
                        ),
                        Expanded(
                            child: Center(
                          child: Container(
                            height: 2.5,
                            color: AppColors.primaryColor,
                          ),
                        ))
                      ],
                    ),
                  ),
                  Container(
                    width: 88.sp,
                    padding: EdgeInsets.only(top: 8.sp),
                    child: Text(
                      I18n.of(context)!.translate("cm_common.step1"),
                      maxLines: 3,
                      style: TextStyle(color: AppColors.primaryColor),
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 100.sp,
                    child: Row(
                      children: [
                        Center(
                          child: widget.currentStep == 1
                              ? Container(
                                  // 设置圆形背景
                                  width: 40.sp,
                                  height: 40.sp,
                                  decoration: const BoxDecoration(
                                    color: AppColors.primaryColor, // 红色背景
                                    shape: BoxShape.circle, // 圆形
                                  ),
                                  // 居中显示SVG图片
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/find/unpaid.svg",
                                      // SVG大小为圆形的60%，可根据需要调整
                                      width: 28.sp,
                                      height: 28.sp,
                                      // 确保SVG在圆形内完整显示
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                )
                              : Container(
                                  padding: EdgeInsets.symmetric(vertical: 4.sp),
                                  child: SvgPicture.asset(
                                    "assets/icons/find/check-circle.svg",
                                    // SVG大小为圆形的60%，可根据需要调整
                                    width: 28.sp,
                                    height: 28.sp,
                                    // 确保SVG在圆形内完整显示
                                    fit: BoxFit.contain,
                                  ),
                                ),
                        ),
                        Expanded(
                            child: Center(
                          child: Container(
                            height: 2.5,
                            color: widget.currentStep == 2
                                ? AppColors.primaryColor
                                : Colors.grey.shade300,
                          ),
                        ))
                      ],
                    ),
                  ),
                  Container(
                    width: 88.sp,
                    padding: EdgeInsets.only(top: 8.sp),
                    child: Text(
                      I18n.of(context)!.translate("cm_common.step2"),
                      maxLines: 3,
                      style: TextStyle(
                          color: widget.currentStep >= 1
                              ? AppColors.primaryColor
                              : Colors.black54),
                    ),
                  )
                ],
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 88.sp,
                    child: Row(
                      children: [
                        Center(
                          child: widget.currentStep == 2
                              ? Container(
                                  // 设置圆形背景
                                  width: 40.sp,
                                  height: 40.sp,
                                  decoration: const BoxDecoration(
                                    color: AppColors.primaryColor, // 红色背景
                                    shape: BoxShape.circle, // 圆形
                                  ),
                                  // 居中显示SVG图片
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/find/quote-active.svg",
                                      // SVG大小为圆形的60%，可根据需要调整
                                      width: 28.sp,
                                      height: 28.sp,
                                      // 确保SVG在圆形内完整显示
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                )
                              : Container(
                                  // 设置圆形背景
                                  width: 32.sp,
                                  height: 32.sp,
                                  margin: EdgeInsets.symmetric(vertical: 2.sp),
                                  padding: EdgeInsets.all(4.sp),
                                  decoration: BoxDecoration(
                                    color: Colors.grey.withOpacity(
                                        0.05), // 透明度通过 withOpacity 控制
                                    shape: BoxShape.circle, // 设置为圆形
                                    border: Border.all(
                                      color: Colors.black38, // 边框颜色
                                      width: 0.5, // 边框宽度
                                      // style: BorderStyle.dotted, // 虚线样式
                                    ),
                                  ),
                                  // 居中显示SVG图片
                                  child: Center(
                                    child: SvgPicture.asset(
                                      "assets/icons/find/quote.svg",
                                      // SVG大小为圆形的60%，可根据需要调整
                                      width: 28.sp,
                                      height: 28.sp,
                                      // 确保SVG在圆形内完整显示
                                      fit: BoxFit.contain,
                                    ),
                                  ),
                                ),
                        ),
                        Expanded(
                            child: Center(
                          child: Container(
                            height: 2.5,
                            color: Colors.grey.shade300,
                          ),
                        )),
                        Container(
                          width: 8.sp, // 宽度
                          height: 12.sp, // 高度
                          color: AppColors.primaryColor, // 红色背景
                          margin: EdgeInsets.symmetric(horizontal: 2.sp),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    width: 92.sp,
                    padding: EdgeInsets.only(top: 8.sp),
                    child: Text(
                      I18n.of(context)!.translate("cm_common.step3"),
                      maxLines: 3,
                      style: TextStyle(
                          color: widget.currentStep >= 2
                              ? AppColors.primaryColor
                              : Colors.black54),
                    ),
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  // 构建右侧背景图上的图标组
  Widget _buildRightSection() {
    return SizedBox(
      width: 300,
      height: 150.sp,
      child: Stack(
        children: [
          // 右侧背景图
          Container(
            padding: EdgeInsets.symmetric(vertical: 24.sp),
            child: Image(
              image: AssetImage("assets/icons/find/step-line.png"),
              width: 300.sp,
              fit: BoxFit.fill,
            ),
          ),
          Positioned(
            left: 10,
            top: 0,
            child: Column(
              children: [
                Container(
                  // 设置圆形背景
                  width: 32.sp,
                  height: 32.sp,
                  margin: EdgeInsets.symmetric(vertical: 13.sp),
                  padding: EdgeInsets.all(4.sp),
                  decoration: BoxDecoration(
                    color:
                        Colors.white.withOpacity(0.8), // 透明度通过 withOpacity 控制
                    shape: BoxShape.circle, // 设置为圆形
                    border: Border.all(
                      color: Colors.black38, // 边框颜色
                      width: 0.5, // 边框宽度
                      // style: BorderStyle.dotted, // 虚线样式
                    ),
                  ),
                  // 居中显示SVG图片
                  child: Center(
                    child: SvgPicture.asset(
                      "assets/icons/find/paid.svg",
                      // SVG大小为圆形的60%，可根据需要调整
                      width: 24.sp,
                      height: 24.sp,
                      // 确保SVG在圆形内完整显示
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                SizedBox(
                  width: 80, // 固定宽度防止文字换行
                  child: Text(
                    I18n.of(context)!.translate("cm_common.step4"),
                    style: const TextStyle(
                      color: Colors.black45,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 100,
            top: 0,
            child: Column(
              children: [
                Container(
                  // 设置圆形背景
                  width: 32.sp,
                  height: 32.sp,
                  margin: EdgeInsets.symmetric(vertical: 13.sp),
                  padding: EdgeInsets.all(4.sp),
                  decoration: BoxDecoration(
                    color:
                        Colors.white.withOpacity(0.8), // 透明度通过 withOpacity 控制
                    shape: BoxShape.circle, // 设置为圆形
                    border: Border.all(
                      color: Colors.black38, // 边框颜色
                      width: 0.5, // 边框宽度
                      // style: BorderStyle.dotted, // 虚线样式
                    ),
                  ),
                  // 居中显示SVG图片
                  child: Center(
                    child: SvgPicture.asset(
                      "assets/icons/order/purchasing.svg",
                      // SVG大小为圆形的60%，可根据需要调整
                      width: 24.sp,
                      height: 24.sp,
                      // 确保SVG在圆形内完整显示
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                // 文字描述
                SizedBox(
                  width: 80, // 固定宽度防止文字换行
                  child: Text(
                    I18n.of(context)!.translate("cm_common.step5"),
                    style: const TextStyle(
                      color: Colors.black45,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 160,
            top: 0,
            child: Column(
              children: [
                Container(
                  // 设置圆形背景
                  width: 32.sp,
                  height: 32.sp,
                  margin: EdgeInsets.symmetric(vertical: 13.sp),
                  padding: EdgeInsets.all(4.sp),
                  decoration: BoxDecoration(
                    color:
                        Colors.white.withOpacity(0.8), // 透明度通过 withOpacity 控制
                    shape: BoxShape.circle, // 设置为圆形
                    border: Border.all(
                      color: Colors.black38, // 边框颜色
                      width: 0.5, // 边框宽度
                      // style: BorderStyle.dotted, // 虚线样式
                    ),
                  ),
                  // 居中显示SVG图片
                  child: Center(
                    child: SvgPicture.asset(
                      "assets/icons/find/check.svg",
                      width: 24.sp,
                      height: 24.sp,
                      // 确保SVG在圆形内完整显示
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 210,
            top: 0,
            child: Column(
              children: [
                Container(
                  // 设置圆形背景
                  width: 32.sp,
                  height: 32.sp,
                  margin: EdgeInsets.symmetric(vertical: 13.sp),
                  padding: EdgeInsets.all(4.sp),
                  decoration: const BoxDecoration(
                    color: AppColors.primaryColor, // 红色背景
                    shape: BoxShape.circle, // 圆形
                  ),
                  // 居中显示SVG图片
                  child: Center(
                    child: SvgPicture.asset(
                      "assets/icons/order/deliveringAc.svg",
                      // SVG大小为圆形的60%，可根据需要调整
                      width: 24.sp,
                      height: 24.sp,
                      // 确保SVG在圆形内完整显示
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
                SizedBox(
                  width: 80, // 固定宽度防止文字换行
                  child: Text(
                    I18n.of(context)!.translate("cm_common.step6"),
                    style: const TextStyle(
                      color: Colors.black45,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 280,
            top: 0,
            child: Column(
              children: [
                Container(
                  // 设置圆形背景
                  width: 24.sp,
                  height: 24.sp,
                  margin: EdgeInsets.symmetric(vertical: 16.sp),
                  decoration: BoxDecoration(
                    color:
                        Colors.white.withOpacity(0.8), // 透明度通过 withOpacity 控制
                    shape: BoxShape.circle, // 设置为圆形
                    border: Border.all(
                      color: Colors.black38, // 边框颜色
                      width: 0.5, // 边框宽度
                      // style: BorderStyle.dotted, // 虚线样式
                    ),
                  ),
                  // 居中显示SVG图片
                  child: Center(
                    child: SvgPicture.asset(
                      "assets/icons/find/uncheck-circle.svg",
                      width: 24.sp,
                      height: 24.sp,
                      // 确保SVG在圆形内完整显示
                      fit: BoxFit.contain,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 右侧单个图标（带位置偏移）
  Widget _buildRightStep(PositionedStep step) {
    print("==>>TODO 4432: ${step.leftOffset}");
    return Positioned(
      left: step.leftOffset,
      top: 0,
      child: Column(
        children: [
          Container(
            // 设置圆形背景
            width: 32.sp,
            height: 32.sp,
            margin: EdgeInsets.symmetric(vertical: 10.sp),
            padding: EdgeInsets.all(4.sp),
            decoration: BoxDecoration(
              color: Colors.white.withOpacity(0.8), // 透明度通过 withOpacity 控制
              shape: BoxShape.circle, // 设置为圆形
              border: Border.all(
                color: Colors.black38, // 边框颜色
                width: 0.5, // 边框宽度
                // style: BorderStyle.dotted, // 虚线样式
              ),
            ),
            // 居中显示SVG图片
            child: Center(
              child: SvgPicture.asset(
                step.icon,
                // SVG大小为圆形的60%，可根据需要调整
                width: 28.sp,
                height: 28.sp,
                // 确保SVG在圆形内完整显示
                fit: BoxFit.contain,
              ),
            ),
          ),

          // 文字描述
          const SizedBox(height: 8),
          SizedBox(
            width: 80, // 固定宽度防止文字换行
            child: Text(
              step.label,
              style: const TextStyle(
                fontSize: 12,
                color: Colors.black45,
                fontWeight: FontWeight.w500,
              ),
              textAlign: TextAlign.center,
            ),
          ),
        ],
      ),
    );
  }
}

// 左侧步骤数据模型
class ProgressStep {
  final String icon;
  final String label;
  final bool isCompleted;
  final bool isActive;

  ProgressStep({
    required this.icon,
    required this.label,
    this.isCompleted = false,
    this.isActive = false,
  });
}

// 右侧步骤数据模型（含位置偏移）
class PositionedStep {
  final String icon;
  final String label;
  final double leftOffset; // 距离左侧的偏移量（控制间距）

  PositionedStep({
    required this.icon,
    required this.label,
    required this.leftOffset,
  });
}
