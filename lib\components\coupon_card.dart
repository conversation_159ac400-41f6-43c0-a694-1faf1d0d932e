import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/styles/colors.dart';

class CouponCard extends StatefulWidget {
  final Map<String, dynamic> coupon;
  final String pageSource;
  final double? width;

  const CouponCard({
    super.key,
    required this.coupon,
    required this.pageSource,
    this.width,
  });

  @override
  State<CouponCard> createState() => _CouponCardState();
}

class _CouponCardState extends State<CouponCard> {
  bool showMore = false;

  String formatCouponRules(List<dynamic>? rules) {
    if (rules == null || rules.isEmpty) return '';

    final ruleDescriptions = rules
        .map((rule) {
          final useRuleCount = rule['useRuleCount']?.toString();
          final useRuleType = rule['useRuleType'];

          switch (useRuleType) {
            case 'COUPON_MAXIMUM_AVAILABLE':
              if (useRuleCount != '-1') {
                return '${I18n.of(context)?.translate('cm_coupon.maxUsage')} $useRuleCount ${I18n.of(context)?.translate('cm_coupon.maxUsageUnit')}';
              }
              return '';
            case 'COUPON_SAME_TYPE_OVERLAY':
              return I18n.of(context)?.translate('cm_coupon.sameTypeOverlay') ??
                  '';
            case 'COUPON_FIRST_USE':
              return I18n.of(context)?.translate('cm_coupon.firstOrderOnly') ??
                  '';
            default:
              return '';
          }
        })
        .where((desc) => desc.isNotEmpty)
        .toList();

    return ruleDescriptions.join(' & ');
  }

  Color _getBackgroundColor() {
    if (widget.coupon['ticketStatus'] != 'TICKET_NOT_USE') {
      return const Color(0xFFF2F2F2);
    } else if (widget.coupon['couponType'] == 'COUPON_TYPE_COMMISSION') {
      return const Color(0xFFFEFAF7);
    } else {
      return const Color(0xFFFEF5F6);
    }
  }

  Color _getBorderColor() {
    if (widget.coupon['ticketStatus'] != 'TICKET_NOT_USE') {
      return const Color(0xFF7F7F7F);
    } else {
      if (widget.coupon['couponType'] == 'COUPON_TYPE_COMMISSION') {
        return const Color(0xFF9C4A2C);
      } else {
        return const Color(0xFFFF4056);
      }
    }
  }

  Color _getCouponColor() {
    if (widget.coupon['ticketStatus'] != 'TICKET_NOT_USE') {
      return const Color(0xFF7F7F7F);
    } else {
      if (widget.coupon['couponType'] == 'COUPON_TYPE_COMMISSION') {
        return const Color(0xFF9C4A2C);
      } else {
        return const Color(0xFFFF4056);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    final isCommissionCoupon =
        widget.coupon['couponType'] == 'COUPON_TYPE_COMMISSION';
    final isNotUsable = widget.coupon['ticketStatus'] != 'TICKET_NOT_USE';

    return Container(
      width: widget.width ?? double.infinity,
      margin: EdgeInsets.only(bottom: 12.sp),
      decoration: BoxDecoration(
        color: _getBackgroundColor(),
        borderRadius: BorderRadius.circular(4.sp),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            offset: const Offset(0, 2),
            blurRadius: 4,
          ),
        ],
      ),
      child: Stack(
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: EdgeInsets.only(
                  left: 12.sp,
                  right: 12.sp,
                  top: 20.sp,
                  bottom: 2.sp,
                ),
                decoration: BoxDecoration(
                  color: _getBackgroundColor(),
                  border: Border(
                    left: BorderSide(color: _getBorderColor().withAlpha(128)),
                    right: BorderSide(color: _getBorderColor().withAlpha(128)),
                    top: BorderSide(color: _getBorderColor().withAlpha(128)),
                  ),
                  // borderRadius: BorderRadius.only(
                  //   topLeft: Radius.circular(4.sp),
                  //   topRight: Radius.circular(4.sp),
                  // ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SizedBox(height: 8.sp),
                    Text(
                      _getCouponTitle(),
                      style: TextStyle(
                        fontSize: 16.sp,
                        color: _getBorderColor(),
                        fontWeight: FontWeight.w500,
                        height: 1.2,
                      ),
                    ),
                    SizedBox(height: 2.sp),
                    _buildCouponAmount(),
                    SizedBox(height: 2.sp),
                    Text(
                      _getCouponCondition(),
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: _getBorderColor().withAlpha(204),
                      ),
                    ),
                  ],
                ),
              ),
              _buildDashedDivider(),
              Container(
                padding: EdgeInsets.only(
                  left: 12.sp,
                  right: 12.sp,
                  top: 2.sp,
                  bottom: 8.sp,
                ),
                decoration: BoxDecoration(
                  color: _getBackgroundColor(),
                  border: Border(
                    left: BorderSide(color: _getBorderColor().withAlpha(128)),
                    right: BorderSide(color: _getBorderColor().withAlpha(128)),
                    bottom: BorderSide(color: _getBorderColor().withAlpha(128)),
                  ),
                  // borderRadius: BorderRadius.only(
                  //   bottomLeft: Radius.circular(4.sp),
                  //   bottomRight: Radius.circular(4.sp),
                  // ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Text(
                          '${timeFormatByZone(widget.coupon['ticketStartExpirationDate'] ?? 0, showSeconds: false)} - ${timeFormatByZone(widget.coupon['ticketEndExpirationDate'] ?? 0, showSeconds: false)}',
                          style: TextStyle(
                            fontSize: 12.sp,
                            color: Color(0xFF737373),
                          ),
                        ),
                        if (widget.coupon['userInstructions']?.isNotEmpty ==
                                true ||
                            formatCouponRules(
                                    widget.coupon['couponInfoUseRuleModelList'])
                                .isNotEmpty)
                          Padding(
                            padding: EdgeInsets.only(left: 16.0), // 左边增加16.0的间距
                            child: GestureDetector(
                              onTap: () => setState(() => showMore = !showMore),
                              child: Container(
                                decoration: BoxDecoration(
                                  border: Border.all(
                                      color: const Color(0xFFCCCCCC)),
                                  borderRadius: BorderRadius.circular(2.sp),
                                ),
                                child: Icon(
                                  showMore
                                      ? Icons.keyboard_arrow_up
                                      : Icons.keyboard_arrow_down,
                                  size: 16.sp,
                                  color: const Color(0xFF737373),
                                ),
                              ),
                            ),
                          ),
                      ],
                    ),
                    if (showMore) ...[
                      if (widget.coupon['userInstructions']?.isNotEmpty == true)
                        Padding(
                          padding: const EdgeInsets.only(top: 5),
                          child: Text(
                            '${I18n.of(context)?.translate('cm_coupon_instructions')}: ${widget.coupon['userInstructions']}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF737373),
                            ),
                          ),
                        ),
                      if (formatCouponRules(
                              widget.coupon['couponInfoUseRuleModelList'])
                          .isNotEmpty)
                        Padding(
                          padding: const EdgeInsets.only(top: 2),
                          child: Text(
                            '${I18n.of(context)?.translate('cm_coupon_rules')}: ${formatCouponRules(widget.coupon['couponInfoUseRuleModelList'])}',
                            style: const TextStyle(
                              fontSize: 12,
                              color: Color(0xFF737373),
                            ),
                          ),
                        ),
                    ],
                  ],
                ),
              ),
            ],
          ),
          Positioned(
            top: 0,
            left: 0,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 3),
              decoration: BoxDecoration(
                color: isNotUsable
                    ? const Color(0xFF7F7F7F)
                    : isCommissionCoupon
                        ? const Color(0xFFD1A27A)
                        : const Color(0xFFDB6B73),
                borderRadius: BorderRadius.circular(4.sp),
              ),
              child: Text(
                (I18n.of(context)?.translate(
                          widget.coupon['couponType'] == 'COUPON_TYPE_PRODUCT'
                              ? 'cm_coupon.productCoupon'
                              : 'cm_coupon.commissionCoupon',
                        ) ??
                        '')
                    .toUpperCase(),
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 13.sp,
                  fontWeight: FontWeight.w900,
                  height: 1.2,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ),
          ),
          if (isNotUsable)
            Positioned(
              right: 10.sp,
              top: 84.sp,
              child: Transform.rotate(
                angle: -30 * 3.14159 / 180,
                child: SvgPicture.asset(
                  widget.coupon['ticketStatus'] == 'TICKET_USE'
                      ? 'assets/images/marketing/expiredCoupon.svg'
                      : 'assets/images/marketing/invalidCoupon.svg',
                  width: 60.sp,
                  height: 60.sp,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getCouponTitle() {
    switch (widget.coupon['couponWay']) {
      case 'COUPON_WAY_FULL_REDUCTION':
        return I18n.of(context)?.translate('cm_coupon.fullReductionCoupon') ??
            '';
      case 'COUPON_WAY_DIRECT_REDUCTION':
        return I18n.of(context)?.translate('cm_coupon.directReductionCoupon') ??
            '';
      case 'COUPON_WAY_DISCOUNT':
        return I18n.of(context)?.translate('cm_coupon.discountCoupon') ?? '';
      default:
        return '';
    }
  }

  Widget _buildCouponAmount() {
    switch (widget.coupon['couponWay']) {
      case 'COUPON_WAY_FULL_REDUCTION':
      case 'COUPON_WAY_DIRECT_REDUCTION':
        return Row(
          crossAxisAlignment: CrossAxisAlignment.baseline,
          textBaseline: TextBaseline.alphabetic,
          children: [
            Text(
              currencyUnit,
              style: TextStyle(
                fontSize: 16.sp,
                color: _getCouponColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 4.sp),
            Text(
              '\$',
              style: TextStyle(
                  fontSize: 22.sp,
                  color: _getCouponColor(),
                  fontWeight: FontWeight.w500),
            ),
            Text(
              setNewUnit(widget.coupon['preferentialAmount']?.toString(), true),
              style: TextStyle(
                  fontSize: 22.sp,
                  color: _getCouponColor(),
                  fontWeight: FontWeight.w500),
            ),
          ],
        );
      case 'COUPON_WAY_DISCOUNT':
        final discount = (widget.coupon['discount'] is num)
            ? widget.coupon['discount'] as num
            : 0.0;
        return Row(
          children: [
            Text(
              '${(discount * 100).toStringAsFixed(0)}%',
              style: TextStyle(
                  fontSize: 22,
                  color: _getCouponColor(),
                  fontWeight: FontWeight.w500),
            ),
            Text(
              I18n.of(context)?.translate('cm_coupon.discount') ?? '',
              style: TextStyle(
                fontSize: 12,
                color: _getCouponColor(),
                fontWeight: FontWeight.w500,
              ),
            ),
            if (widget.coupon['preferentialAmount'] != null)
              Text(
                ' ${I18n.of(context)?.translate('cm_coupon.upToMoney')} \$${setNewUnit(widget.coupon['preferentialAmount']?.toString(), false)}',
                style: TextStyle(
                  fontSize: 14,
                  color: _getCouponColor().withAlpha(204),
                ),
              ),
          ],
        );
      default:
        return const SizedBox();
    }
  }

  String _getCouponCondition() {
    if (widget.coupon['couponWay'] == 'COUPON_WAY_DIRECT_REDUCTION') {
      return I18n.of(context)?.translate('cm_coupon.noLimit') ?? '';
    }

    switch (widget.coupon['couponUseConditionsType']) {
      case 'FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumRequired')} ${setNewUnit(widget.coupon['useConditionsAmount']?.toString(), false)}';
      case 'EVERY_FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumUnmet')} ${setNewUnit(widget.coupon['useConditionsAmount']?.toString(), false)} ${I18n.of(context)?.translate('cm_coupon.minimumUnmetCost')}';
      case 'UNLIMITED':
        return I18n.of(context)?.translate('cm_coupon.noLimit') ?? '';
      default:
        return '';
    }
  }

  Widget _buildDashedDivider() {
    return Container(
      height: 12.sp,
      margin: EdgeInsets.zero,
      child: Stack(
        children: [
          Positioned.fill(
            child: CustomPaint(
              painter: DashedLinePainter(
                color: _getBorderColor().withAlpha(128),
              ),
            ),
          ),
          Positioned(
            left: -6.sp,
            child: Container(
              width: 12.sp,
              height: 12.sp,
              decoration: BoxDecoration(
                color: AppColors.primaryBackground,
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getBorderColor().withAlpha(128),
                ),
              ),
            ),
          ),
          Positioned(
            right: -6.sp,
            child: Container(
              width: 12.sp,
              height: 12.sp,
              decoration: BoxDecoration(
                color: AppColors.primaryBackground,
                shape: BoxShape.circle,
                border: Border.all(
                  color: _getBorderColor().withAlpha(128),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class DashedLinePainter extends CustomPainter {
  final Color color;

  DashedLinePainter({required this.color});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = color
      ..strokeWidth = 1
      ..style = PaintingStyle.stroke;

    const dashWidth = 4.0;
    const dashSpace = 4.0;
    double startX = 0;
    final y = size.height / 2;

    while (startX < size.width) {
      canvas.drawLine(
        Offset(startX, y),
        Offset(startX + dashWidth, y),
        paint,
      );
      startX += dashWidth + dashSpace;
    }
  }

  @override
  bool shouldRepaint(DashedLinePainter oldDelegate) => false;
}
