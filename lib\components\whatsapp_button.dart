import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/components/common_webview_page.dart';

class WhatsAppButton extends StatefulWidget {
  final String? phoneNumber;
  final double? distanceBottom; // 距离底部的距离

  const WhatsAppButton({super.key, this.phoneNumber, this.distanceBottom});

  @override
  State<WhatsAppButton> createState() => _WhatsAppButtonState();
}

class _WhatsAppButtonState extends State<WhatsAppButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  final String defaultPhoneNumber = "8615906790093";

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _onGoWhatsAppClick() async {
    // Play the animation
    await _animationController.forward();
    await _animationController.reverse();

    final String phoneNumber = widget.phoneNumber ?? defaultPhoneNumber;

    // Build WhatsApp URL
    final String url =
        "https://api.whatsapp.com/send/?phone=${phoneNumber}&text=quiero+saber+sobre+importar+desde+China%2C+%C2%BFpuedes+ayudarme%3F&type=phone_number&app_absent=0";

    // You can add tracking logic here
    print("点击了 WhatsApp 按钮，链接：$url");

    // Open link with embedded WebView
    Get.to(() => CommonWebViewPage(
          url: url,
          title: 'WhatsApp',
          showAppBar: true,
        ));
  }

  @override
  Widget build(BuildContext context) {
    final buttonSize = 40.sp;
    // final screenHeight = MediaQuery.of(context).size.height;

    return Positioned(
      // top: (screenHeight - buttonSize) / 2,
      bottom: widget.distanceBottom ?? 120.sp,
      right: 12.sp,
      child: GestureDetector(
        onTap: _onGoWhatsAppClick,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: SvgPicture.asset(
            'assets/images/common/whatsapp-icon-default.svg',
            width: buttonSize,
            height: buttonSize,
          ),
        ),
      ),
    );
  }
}
