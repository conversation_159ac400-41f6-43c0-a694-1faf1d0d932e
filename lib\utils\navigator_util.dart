import 'package:flutter/material.dart';

class NavigatorUtil {
  static final List<String> _routeHistory = [];

  // 普通跳转
  static Future<T?> push<T>(BuildContext context, Widget page) {
    _routeHistory.add(page.toString());

    return Navigator.push(
        context, MaterialPageRoute(builder: (context) => page));
  }

  // 带命名的跳转（用于记录）
  static Future<T?> pushNamed<T>(BuildContext context, String routeName,
      {Object? arguments}) {
    _routeHistory.add(routeName);
    return Navigator.pushNamed(context, routeName, arguments: arguments);
  }

  // 替换当前页面
  static Future<T?> pushReplacement<T>(BuildContext context, Widget page) {
    return Navigator.pushReplacement(
        context, MaterialPageRoute(builder: (context) => page));
  }

  // 返回上一页
  static void pop<T extends Object?>(BuildContext context, [T? result]) {
    if (Navigator.canPop(context)) {
      if (_routeHistory.isNotEmpty) {
        _routeHistory.removeLast();
      }
      Navigator.pop(context, result);
    }
  }

  // 返回根页面
  static void popToRoot(BuildContext context) {
    Navigator.popUntil(context, (route) => route.isFirst);
  }
}
