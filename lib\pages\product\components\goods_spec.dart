import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/cart_sku_item.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class GoodsSpecDrawer extends StatefulWidget {
  final BuildContext context; // 添加 required 参数
  final String goodsId;
  final String? sourceGoodsId;
  final Function(int) selectEvent;
  final Function(String) onCartUpdate;
  final Function() onGoodsSpecClose;

  const GoodsSpecDrawer({
    super.key,
    required this.context, // 强制调用者传入 context
    required this.goodsId,
    required this.selectEvent,
    required this.onCartUpdate,
    required this.onGoodsSpecClose,
    this.sourceGoodsId,
  });

  @override
  State<GoodsSpecDrawer> createState() => _GoodsSpecDrawerState();
}

class _GoodsSpecDrawerState extends State<GoodsSpecDrawer> {
  String? _goodsId;
  bool _isLoading = true;
  double skuNameWidth = 0.0;
  double skuPriceWidth = 0.0;
  double cartQtyWidth = 0.0;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  int totalSpecItem = 0;
  int totalMoneyAmount = 0;
  bool showVideoIcon = false;
  int currentStepPriceEnd = 0;
  // SiteListModel? siteData;
  List<String> selectedSpecList = [];
  List<GoodsSkuInfoModel> skuList = [];
  GoodsInfoModel? pageData;
  Map<String, List<String>> specItemMap = {};
  Map<String, String> currentSpecMap = {};
  Map<String, List<String>> selectedSpecsMap = {};
  List<GoodsSkuInfoModel> selectedSkuList = [];
  EditForm editForm = EditForm(
      totalCost: 0, skuList: [], routeList: [], showExpandedRoutes: false);
  MidCartModel? cartData;
  // final MainController mainController = Get.put(MainController());
  // TODO 删除一个SKU后，最后一个SKU的加减按钮无效
  @override
  void initState() {
    super.initState();
    _goodsId = widget.goodsId;

    onPageData();
    // 站点信息
    onSiteData();
  }

  @override
  void dispose() {
    super.dispose();
  }

  // 获取商品详情
  Future<void> onPageData({String? type}) async {
    try {
      if (widget.goodsId.isEmpty) {
        _goodsId = await onGetGoodsId(widget.sourceGoodsId ?? "");
        if (_goodsId?.isEmpty ?? true) {
          return;
        }
      }

      dynamic res = await ProductAPI.useGoodsInfo({
        "id": _goodsId,
        "deviceType": 1,
      });
      if (res['result']['code'] == 200) {
        setState(() {
          if (type == "updateShippingCost") {
            pageData?.pcsEstimateFreight = res['data']?['pcsEstimateFreight'];
            return;
          }
          editForm.selectedRouteId = res['data']?['routeId'] ?? '';
          pageData = GoodsInfoModel.fromJson(res['data']);
          pageData?.pcsEstimateFreight =
              res['data']?['pcsEstimateFreight'] ?? 0;
          editForm.selectedRouteId = pageData?.routeId;
          pageData?.skuList = pageData?.skuList?.map((item) {
            item.skuName =
                item.specItems?.map((specItem) => specItem.itemName).join(', ');
            ;
            item.minIncreaseQuantity ??= pageData?.minIncreaseQuantity;
            return item;
          }).toList();

          onInitData();
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 以图搜图-根据1688id获取商品信息
  Future<String?> onGetGoodsId(String sourceGoodsId) async {
    try {
      dynamic res = await ProductAPI.useGetGoods({
        "str": sourceGoodsId,
      });
      if (res != null && res?['result']?['code'] == 200) {
        return res['data'];
      } else {
        showErrorMessage(
            I18n.of(context)!.translate("cm_common_addGoodsError"));
        return null;
      }
    } catch (e) {
      showErrorMessage(e.toString());
      return null;
    }
  }

  // 计算预估运费
  Future<void> onCalculateEstimateFreight() async {
    try {
      if (editForm.skuList.isEmpty) {
        return;
      }

      PageClickIncomingModel incomingRes = await Global.getIncomingConfig();

      CalculateEstimateFreightParam? param = CalculateEstimateFreightParam(
          siteId: incomingRes.siteId,
          routeId: editForm.selectedRouteId,
          skuList: editForm.skuList.map((sku) {
            return MidCartSkuAddParam(
                skuId: sku.id ?? '', quantity: sku.cartQty, spm: "TODO");
          }).toList());

      final res = await HomeAPI.useCalculateEstimateFreight(param.toJson());
      if (res['result']['code'] == 200 && res['data']?['routeList'] != null) {
        setState(() {
          CalculateEstimateFreightModel freightModel =
              CalculateEstimateFreightModel.fromJson(res['data']);
          editForm.totalEstimateFreight = freightModel.totalEstimateFreight;
          editForm.freightGoodsQty = freightModel.freightGoodsQty;
          editForm.routeList = freightModel.routeList!;
          editForm.selectedRoute =
              freightModel.routeList?.firstWhere((route) => route.selected);
          editForm.selectedRouteId = editForm.selectedRoute?.routeId;

          // 计算总计&单价
          int? productCost = (editForm.totalMoneyAmount! * 100).round();
          int? shippingCost =
              ((editForm.selectedRoute?.totalEstimateFreight ?? 0) * 100)
                  .round();
          int? totalCostInCents = productCost + shippingCost;
          editForm.totalCost =
              double.parse((totalCostInCents / 100).toStringAsFixed(2));
          if (editForm.totalSpecItem! > 0) {
            int unitPriceInCents =
                ((totalCostInCents * 100) / (editForm.totalSpecItem! * 100))
                    .round();

            editForm.unitPrice =
                double.parse((unitPriceInCents / 100).toStringAsFixed(2));
          } else {
            editForm.unitPrice = 0;
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {}
  }

  // 初始化, 将没有库存的规格全部标记出来
  Future<void> onInitData({String? type}) async {
    try {
      cartData = await Global.getCartData();
      if (cartData != null) {
        setState(() {
          MidCartGoodsModel? goodsData = cartData?.goodsList
              .firstWhereOrNull((item) => item.goodsId == pageData?.id);
          // 标记库存不存在规格
          pageData?.specList?.forEach((spec) {
            spec.items?.forEach((item) {
              // 根据规格ID筛选出所有的SKU
              List<GoodsSkuInfoModel> matchingSkus =
                  (pageData?.skuList ?? []).where((sku) {
                return sku.specItems != null &&
                    (sku.specItems ?? [])
                        .any((specItem) => specItem.itemId == item.itemId);
              }).toList();
              // 库存不存在的则直接标记为禁用
              int count = matchingSkus
                  .where(
                      (sku) => sku.stockQty == null || (sku.stockQty ?? 0) <= 0)
                  .length;

              if (count == matchingSkus.length) {
                item.disabled = true;
              }
            });
          });

          if (goodsData != null) {
            int skuItemSelected = 0;
            int onSpecItemSelected = pageData?.specList?.length ?? 0;
            for (int i = 0; i < goodsData.skuList.length; i++) {
              MidCartSkuModel sku = goodsData.skuList[i];
              GoodsSkuInfoModel? skuItem = pageData?.skuList
                  ?.firstWhereOrNull((item) => item.id == sku.skuId);

              for (GoodsSkuSpecIdPairModel specItem
                  in skuItem?.specItems ?? []) {
                int specIndex = pageData?.specList
                        ?.indexWhere((item) => item.id == specItem.specId) ??
                    -1;
                if (specIndex != -1) {
                  int specItemIndex = pageData?.specList?[specIndex].items
                          ?.indexWhere(
                              (item) => item.itemId == specItem.itemId) ??
                      -1;
                  // 只选中第一个
                  if (specItemIndex != -1 && onSpecItemSelected > 0) {
                    onSpecItemSelected -= 1;
                    pageData?.specList?[specIndex].items?[specItemIndex]
                        .selected = true;
                  }
                  if (specItemIndex == 0) {
                    pageData?.specList?[specIndex].items?[specItemIndex]
                        .cartQty = sku.buyQty;
                  }
                }
              }
              // editForm.skuList 填充数据
              if (skuItem != null) {
                editForm.skuList.add(GoodsSkuInfoModel(
                    id: skuItem.id,
                    skuNo: skuItem.skuNo,
                    skuName: skuItem.skuName,
                    price: onStepPriceByQuantity(skuItem),
                    specItems: skuItem.specItems,
                    stepPrices: skuItem.stepPrices,
                    cartQty: skuItem.cartQty,
                    selected: skuItemSelected == 0 ? true : false,
                    minIncreaseQuantity: skuItem.minIncreaseQuantity));
                skuItemSelected += 1;
              }
            }

            onSkuListFoldUpdate();
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 获取国家
  Future<void> onSiteData() async {
    try {
      SiteListModel? res = await Global.getSiteData();
      if (res != null) {
        editForm.siteData = res;
        // editForm.siteInfo = res;
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 判断商品规格是否禁用
  bool onSpecItemDisabled(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    // 判断商品规格列表的数量
    if (pageData?.specList == null || specItem.disabled == true) {
      return true; // 如果规格列表为空，直接返回 true
    }

    if (pageData?.specList?.length == 1) {
      return specItem.disabled;
    } else if (currentSpecMap.isEmpty) {
      return specItem.disabled;
    } else {
      // 使用已选择的规格筛选出匹配的SKU，判断SKU的库存
      GoodsSkuInfoModel? sku = onSelectSkuBySpec(goodsSpec, specItem);
      if (sku != null && (sku.stockQty ?? 0) <= 0) {
        return true;
      }

      return false;
    }
  }

  // 商品规格选择, 只有最后一种规格可以选择多个
  void onSpecItemSelected(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    try {
      setState(() {
        // 当前规格所在的下标
        int specIndex = (pageData?.specList ?? [])
            .indexWhere((spec) => spec.id == goodsSpec?.id);
        if (specIndex == 0) {
          currentSpecMap.clear();
        }

        // 最后一种规格的下标
        int specLastIndex = pageData?.specList != null
            ? (pageData?.specList?.length ?? 0) - 1
            : 0;
        for (int i = 0; i < (pageData?.specList?.length ?? 0); i++) {
          GoodsSkuSpecModel? spec = pageData?.specList?[i];
          // 当前选中的规格下标后面的规格全部取消选中
          if (i < specIndex && specIndex != 0) {
            continue;
          }
          // 最后一种规格可以选中多个
          if (specIndex == specLastIndex) {
            continue;
          }
          for (GoodsSkuSpecItemModel item in spec?.items ?? []) {
            if (item.selected == true) {
              item.selected = false;
            }
          }
        }
        // 记录选择的规格信息
        currentSpecMap[(goodsSpec?.id ?? "")] = specItem.itemId;
        // 将选择的规格置反&修改加购列表
        for (GoodsSkuSpecModel spec in pageData?.specList ?? []) {
          if (goodsSpec?.id == spec.id) {
            for (GoodsSkuSpecItemModel item in spec.items ?? []) {
              if (item.itemId == specItem.itemId) {
                item.selected = true;
                onAddCartListUpdate(goodsSpec, specItem, item.selected);
              }
            }
          }
        }
      });
    } finally {}
  }

  // 更新加购列表
  void onAddCartListUpdate(GoodsSkuSpecModel? goodsSpec,
      GoodsSkuSpecItemModel specItem, bool selected) {
    try {
      setState(() {
        // 已选择的规格种类与商品规格种类不匹配无法聚焦到SKU
        if (pageData?.specList?.length != currentSpecMap.length) {
          return;
        }
        // 如果是取消则删除加购列表
        GoodsSkuInfoModel? sku = onSelectSkuBySpec(goodsSpec, specItem);
        if (sku == null) {
          return;
        }
        for (int i = 0; i < editForm.skuList.length; i++) {
          editForm.skuList[i].selected = false;
        }

        int index = editForm.skuList.indexWhere((item) => item.id == sku.id);

        if (index == -1) {
          editForm.skuList.add(GoodsSkuInfoModel(
              id: sku.id,
              skuNo: sku.skuNo,
              skuName: sku.skuName,
              price: sku.price,
              specItems: sku.specItems,
              stepPrices: sku.stepPrices,
              cartQty: sku.cartQty,
              selected: true,
              minIncreaseQuantity: sku.minIncreaseQuantity));

          onSpecCartQtyUpdate(editForm.skuList[editForm.skuList.length - 1]);
        } else {
          editForm.skuList[index].selected = true;
        }

        onSkuListFoldUpdate();
      });
    } finally {}
  }

  // 根据商品规格聚焦一个有效的SKU
  GoodsSkuInfoModel? onSelectSkuBySpec(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    List<String> specItemIds = [specItem.itemId];
    try {
      // 筛选出其他的商品规格
      for (var item in currentSpecMap.entries) {
        if (item.key != goodsSpec?.id && !specItemIds.contains(item.value)) {
          specItemIds.add(item.value);
        }
      }
      if (specItemIds.length < (pageData?.specList ?? []).length) {
        return null;
      }

      GoodsSkuInfoModel? sku = (pageData?.skuList ?? []).firstWhere((sku) =>
          specItemIds.every((specItemId) =>
              (sku.specItems ?? []).any((item) => item.itemId == specItemId)));

      if ((sku.stockQty ?? -1) <= 0) {
        return GoodsSkuInfoModel(cartQty: -1, selected: false);
      } else if ((sku.stockQty ?? 0) > 0) {
        sku.cartQty =
            sku.minIncreaseQuantity ?? (pageData?.minIncreaseQuantity ?? 0);
        sku.price = onStepPriceByQuantity(sku);
        return sku;
      }

      return null;
    } catch (e) {
      showErrorMessage(e.toString());
      return GoodsSkuInfoModel(cartQty: -1, selected: false);
    }
  }

  // 增加数量
  void onIncreaseQuantity(GoodsSkuInfoModel skuInfo) {
    setState(() {
      int skuIndex =
          editForm.skuList.indexWhere((item) => item.id == skuInfo.id);
      if (skuIndex != -1) {
        editForm.skuList[skuIndex].cartQty +=
            skuInfo.minIncreaseQuantity ?? (pageData?.minIncreaseQuantity ?? 0);
        onSpecCartQtyUpdate(editForm.skuList[skuIndex]);
      }

      onSkuListFoldUpdate();
    });
  }

  // 减少数量
  void onDecreaseQuantity(StateSetter setState, GoodsSkuInfoModel skuInfo) {
    setState(() {
      int skuIndex =
          editForm.skuList.indexWhere((item) => item.id == skuInfo.id);
      GoodsSkuInfoModel skuItem =
          editForm.skuList.firstWhere((item) => item.id == skuInfo.id);
      if (skuIndex != -1) {
        if (editForm.skuList[skuIndex].cartQty <=
            (skuInfo.minIncreaseQuantity ?? 0)) {
          editForm.skuList[skuIndex].cartQty = 0;
          editForm.skuList.removeAt(skuIndex);
        } else {
          editForm.skuList[skuIndex].cartQty -=
              skuInfo.minIncreaseQuantity ?? 0;
        }
        onSpecCartQtyUpdate(skuItem);
      }

      onSkuListFoldUpdate();
    });
  }

  void onCartQtyUpdate(GoodsSkuInfoModel skuInfo, int newQuantity) {
    setState(() {
      int skuIndex =
          editForm.skuList.indexWhere((item) => item.id == skuInfo.id);
      if (skuIndex != -1) {
        int cartQty =
            (newQuantity / ((pageData?.minIncreaseQuantity ?? 1))).ceil() *
                (pageData?.minIncreaseQuantity ?? 1);
        editForm.skuList[skuIndex].cartQty = cartQty;
        if (cartQty == 0) {
          editForm.skuList.removeAt(skuIndex);
        } else {
          editForm.skuList[skuIndex].cartQty = cartQty;
        }
        onSpecCartQtyUpdate(editForm.skuList[skuIndex]);
      }
    });
  }

  void onSkuListFoldUpdate() {
    if (editForm.skuList.isEmpty) {
      return;
    }

    setState(() {
      editForm.totalSpecItem = editForm.skuList.fold(0, (sum, sku) {
        return sum! + sku.cartQty;
      });

      final int minBuyQty = pageData?.minBuyQuantity?.toInt() ?? 0;

      if (editForm.totalSpecItem! < minBuyQty) {
        editForm.errorMessage =
            '${I18n.of(context)!.translate("cm_goods.addToList")} ${pageData?.minBuyQuantity}';
      } else {
        editForm.errorMessage = null;
      }

      editForm.totalMoneyAmount = editForm.skuList.fold(0, (sum, sku) {
        return sum! + (sku.cartQty) * (sku.price ?? 0);
      });

      onCalculateEstimateFreight();
    });
  }

  // 更新第一个商品规格的购物车数量
  void onSpecCartQtyUpdate(GoodsSkuInfoModel skuInfo) {
    setState(() {
      GoodsSkuSpecIdPairModel? specItem = skuInfo.specItems!.firstOrNull;
      for (int i = 0; i < (pageData?.specList ?? []).length; i++) {
        GoodsSkuSpecModel? specInfo = pageData?.specList?[i];
        for (GoodsSkuSpecItemModel item in specInfo?.items ?? []) {
          if (specItem?.itemId == item.itemId) {
            item.cartQty = skuInfo.cartQty;
          }
          // 清除已选的规格
          if (skuInfo.cartQty == 0) {
            bool? exist =
                skuInfo.specItems?.any((si) => si.itemId == item.itemId);
            if (exist == true) {
              item.selected = false;
            }
          }
        }
      }
    });
  }

  // 根据加购数量筛选价格
  double? onStepPriceByQuantity(GoodsSkuInfoModel sku) {
    for (SkuStepPrice stepPrice in sku.stepPrices!) {
      if (sku.cartQty < stepPrice.start) {
        return stepPrice.price;
      }
      if (sku.cartQty >= stepPrice.start && sku.cartQty <= stepPrice.end) {
        return stepPrice.price;
      }
      if (stepPrice.end == -1) {
        return stepPrice.price;
      }
    }
    return 0;
  }

  Future<void> onUpdateSkuPrice() async {
    try {
      setState(() {
        // 计算总数量
        int skuTotalQuantity =
            skuList.fold(0, (total, sku) => total + (sku.cartQty ?? 0));

        // 遍历 skuList 并处理 stepPrices
        for (var sku in skuList) {
          // 检查 stepPrices 是否为 null，避免直接使用可能为 null 的值
          for (var stepPrice in (sku.stepPrices ?? [])) {
            if (skuTotalQuantity <= stepPrice.end || stepPrice.end == -1) {
              sku.price = stepPrice.price;
              currentStepPriceEnd = stepPrice.end;
              break;
            }
          }
        }
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onCountFirstSkuTotal() async {
    try {
      // 初始化 firstSpecTotals 为空 Map
      Map<String, int> firstSpecTotals = {};

      setState(() {
        for (var sku in skuList) {
          // 获取第一规格的 itemId
          String? firstSpecItemId = sku.specItems?[0].itemId;

          if (firstSpecItemId != null) {
            // 如果 firstSpecItemId 存在且未记录过，则初始化为 0
            if (!firstSpecTotals.containsKey(firstSpecItemId)) {
              firstSpecTotals[firstSpecItemId] = 0;
            }

            // 累加当前 SKU 的数量
            firstSpecTotals[firstSpecItemId] =
                (firstSpecTotals[firstSpecItemId] ?? 0) + (sku.cartQty ?? 0);
          }
        }
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  String onGetStepPrice(int index) {
    GoodsPriceRange? price = pageData?.goodsPriceRanges?[index];
    if (price?.minPrice != price?.maxPrice) {
      return '${setUnit(price?.minPrice ?? 0)}-${price?.maxPrice ?? 0}';
    } else {
      return setUnit(price?.maxPrice ?? 0);
    }
  }

  String filterPriceRange(int index) {
    GoodsPriceRange? price = pageData?.goodsPriceRanges?[index];
    if (price?.end == -1) {
      return '>=${price?.start} ${pageData?.goodsPriceUnitName}';
    } else {
      if (price?.start == price?.end) {
        return '${price?.start} ${pageData?.goodsPriceUnitName}';
      }
      return '${price?.start}-${price?.end} ${pageData?.goodsPriceUnitName}';
    }
  }

  // 加入购物车
  void onAddToCart(BuildContext context) async {
    String? missingSpec;
    for (GoodsSkuSpecModel spec in (pageData?.specList ?? [])) {
      bool itemsSelected = (spec.items ?? [])
          .any((item) => selectedSpecList.contains(item.itemId));
      if (!itemsSelected) {
        missingSpec = spec.name;
        break;
      }
    }

    if (editForm.skuList.isEmpty) {
      return showErrorMessage(
          '${I18n.of(context)!.translate("cm_goods.pleaseSelect")} $missingSpec');
    }
    if (!Global.isLogin.value) {
      widget.onGoodsSpecClose();

      await AuthHelper.showLoginModal(
        context,
        // redirectRoute: AppRoutes.ProductPage, // 可选，登录成功后的重定向路由
        onAuthSuccess: () {
          // NavigatorUtil.pushNamed(context, AppRoutes.ProductPage,
          //     arguments: {'productId': widget.goodsId});
        },
      );
      return;
    }

    // 已登录，直接加购
    await _performAddToCart(context);
  }

  // 加购
  Future<void> _performAddToCart(BuildContext context) async {
    try {
      SiteListModel? siteData = await Global.getSiteData();
      MidCartAddParam? param = MidCartAddParam(
          goodsId: pageData?.id ?? "",
          siteId: siteData?.id,
          routeId: editForm.selectedRouteId,
          skus: editForm.skuList.map((sku) {
            return MidCartSkuAddParam(
                skuId: sku.id ?? '', quantity: sku.cartQty, spm: "TODO");
          }).toList());

      dynamic res = await InquiryAPI.useAddCart(param.toJson());
      if (res['result']['code'] == 200) {
        setState(() {
          cartData = MidCartModel.fromJson(res['data']);
          showSuccessMessage(I18n.of(context)!.translate("cm_goods.addToList"),
              icon: Icon(
                Icons.check_circle_outline_outlined,
                color: Colors.green,
              ));

          // mainController.onCartDataUpdate(data: res['data']);
          Global.setCartList(data: res['data']);
          widget.onCartUpdate(pageData?.id ?? "");
        });
      } else {
        showErrorMessage(res['result']?['message']);
        setState(() {
          editForm.errorMessage = res['result']?['message'];
        });
        Future.delayed(Duration(seconds: 200), () {
          setState(() {
            editForm.errorMessage = "";
          });
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 线路选择
  Future<void> onSelectRoute(CalculateRouteFreightModel routeInfo) async {
    setState(() {
      for (CalculateRouteFreightModel routeItem in editForm.routeList) {
        if (routeItem.routeId == routeInfo.routeId) {
          routeItem.selected = true;
          editForm.selectedRoute = routeInfo;
          editForm.selectedRouteId = routeInfo.routeId;
        } else {
          routeItem.selected = false;
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(widget.context).size.width;
    screenHeight = MediaQuery.of(widget.context).size.height;
    skuNameWidth = screenWidth * 0.43;
    skuPriceWidth = screenWidth * 0.22;
    cartQtyWidth = screenWidth * 0.3;

    return _isLoading
        ? LoadingWidget(showType: "Animated")
        : AppScaffold(
            backgroundColor: Colors.white,
            body: Container(
              width: screenWidth,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  // 顶部固定部分
                  _buildFixedHeader(),
                  // 中间滚动部分
                  Expanded(
                    child: SingleChildScrollView(
                      child: StatefulBuilder(builder: (context, setState) {
                        return Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            _buildPriceRange(setState),
                            _buildGoodsSpecList(setState),
                            _buildGoodsSkuList(setState),
                            _buildTotalAmount(setState),
                            _buildGoodsAmount(setState),
                            _buildRouteList(setState),
                            _buildShippingCost(setState),
                            _buildFreightConfirm(setState),
                            _buildTotalCost(setState),
                          ],
                        );
                      }),
                    ),
                  ),
                  // 底部固定部分
                  _buildFixedFooter(),
                ],
              ),
            ),
          );
  }

  // 顶部固定部分
  Widget _buildFixedHeader() {
    return Container(
      height: 48,
      alignment: Alignment.center,
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: Colors.grey, // 边框颜色
            width: 0.5, // 边框宽度
          ),
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8),
            child: Text(
              I18n.of(context)!.translate("cm_goods.specifications"),
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: GestureDetector(
              onTap: () {
                widget.onGoodsSpecClose();
              },
              child: Icon(
                Icons.close,
                size: 16,
              ),
            ),
          )
        ],
      ),
    );
  }

  // 底部固定部分
  Widget _buildFixedFooter() {
    return Container(
      color: Colors.grey.shade100,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
              visible: editForm.errorMessage != null,
              child: Container(
                width: screenWidth,
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                child: Text(
                  editForm.errorMessage ?? "",
                  style: TextStyle(color: Colors.red),
                ),
              )),
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                flex: 5,
                child: FancyButton(
                  onTap: () {
                    setState(() {
                      onAddToCart(context);
                    });
                  },
                  color: AppColors.primaryColor,
                  borderColor: Colors.grey.shade300,
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 10.sp),
                  borderRadius: BorderRadius.circular(8.0),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      // Icon(
                      //   Icons.shopping_cart,
                      //   color: Colors.white,
                      //   size: 20,
                      // ),
                      SvgPicture.asset(
                        'assets/images/common/cart.svg',
                        width: 26.sp,
                        colorFilter: ColorFilter.mode(
                          Colors.white, // 设置为红色
                          BlendMode.srcIn, // 常用模式，用指定颜色替换原SVG中的所有颜色
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 4),
                        child: Text(
                          I18n.of(context)!.translate("cm_find_addCartConfirm"),
                          style: TextStyle(color: Colors.white, fontSize: 18),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
              Expanded(
                flex: 1,
                child: Container(
                  padding: EdgeInsets.only(right: 8.sp),
                  child: GestureDetector(
                    onTap: () {
                      NavigatorUtil.pushNamed(context, AppRoutes.CartPage);
                    },
                    child: Center(
                      child: Stack(
                        clipBehavior: Clip.none,
                        children: [
                          // const Icon(
                          //   Icons.shopping_cart,
                          //   size: 42,
                          //   color: Colors.grey,
                          // ),
                          SvgPicture.asset(
                            'assets/images/common/cart.svg',
                            width: 32.sp,
                            colorFilter: ColorFilter.mode(
                              Colors.grey, // 设置为红色
                              BlendMode.srcIn, // 常用模式，用指定颜色替换原SVG中的所有颜色
                            ),
                          ),
                          Visibility(
                            visible: (cartData?.stat?.goodsCount ?? 0) > 0,
                            child: Positioned(
                              right: -12,
                              top: 0,
                              child: Container(
                                padding:
                                    const EdgeInsets.symmetric(horizontal: 6),
                                decoration: BoxDecoration(
                                  color: Colors.red,
                                  borderRadius: BorderRadius.circular(2),
                                ),
                                constraints: const BoxConstraints(
                                  minHeight: 12,
                                ),
                                child: Text(
                                  '${cartData?.stat?.goodsCount ?? ''}',
                                  style: TextStyle(
                                    color: Colors.white,
                                    fontSize: 12,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ),
                            ),
                          )
                        ],
                      ),
                    ),
                  ),
                ),
              ),
            ],
          )
        ],
      ),
    );
  }

  // 阶梯价
  Widget _buildPriceRange(StateSetter setState) {
    return Container(
      color: Colors.grey.shade100,
      child: Column(children: <Widget>[
        Visibility(
            visible: (pageData?.minBuyQuantity ?? 0) > 1,
            child: Container(
              padding: EdgeInsets.only(left: 2, top: 6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.only(left: 10),
                    child: Text(
                      '${I18n.of(context)?.translate("cm_goods_minBuyQuanity") ?? ''}: ',
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),
                  Text(
                    (pageData?.minBuyQuantity ?? 1).toString(),
                    style: const TextStyle(
                      fontSize: 12,
                    ),
                  ),
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 6),
                    child: Text(
                      pageData?.goodsPriceUnitName ?? '',
                      style: const TextStyle(
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            )),
        SizedBox(
          height: 50, // 设置 ListView 的高度
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: pageData?.goodsPriceRanges?.length,
            itemBuilder: (context, index) {
              return Container(
                margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        onGetStepPrice(index),
                        style: const TextStyle(
                            color: Colors.black,
                            fontSize: 14,
                            fontWeight: FontWeight.w500),
                      ),
                      Text(filterPriceRange(index),
                          style: const TextStyle(
                              color: Colors.grey,
                              fontSize: 12,
                              fontWeight: FontWeight.w300)),
                    ]),
              );
            },
          ),
        ),
      ]),
    );
  }

  // 商品规格
  Widget _buildGoodsSpecList(StateSetter setState) {
    return Container(
      padding: const EdgeInsets.only(top: 6, left: 6, right: 6),
      child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: pageData?.specList?.length,
          itemBuilder: (context, index) {
            GoodsSkuSpecModel? goods = pageData?.specList?[index];
            return _buildGoodsSpecItems(setState, goods, index);
          }),
    );
  }

  // 商品规格元素列表
  Widget _buildGoodsSpecItems(
      StateSetter setState, GoodsSkuSpecModel? spec, int specIndex) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Text('${specIndex + 1}. ${spec?.name}',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        ),
        Container(
          padding: EdgeInsets.all(2),
          child: Wrap(
            spacing: 8.0, // 水平间距
            runSpacing: 8.0, // 垂直间距
            children: (spec?.items ?? []).map((item) {
              return _buildGoodsSpecItem(setState, spec, item, specIndex);
            }).toList(),
          ),
        )
      ],
    );
  }

  // 商品规格元素
  Widget _buildGoodsSpecItem(StateSetter setState, GoodsSkuSpecModel? spec,
      GoodsSkuSpecItemModel specItem, int specIndex) {
    bool disabled = onSpecItemDisabled(spec, specItem);

    return AbsorbPointer(
      absorbing: disabled,
      child: GestureDetector(
          onTap: () {
            setState(() {
              onSpecItemSelected(spec, specItem);
            });
          },
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Visibility(
                visible: specIndex == 0 && specItem.imageUrl != '',
                child: Container(
                  width: 168,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color:
                          specItem.selected ? Colors.red : Colors.grey.shade400,
                      width: disabled == true ? 0 : 0.5,
                    ),
                    color:
                        disabled == true ? Colors.grey.shade200 : Colors.white,
                    borderRadius: BorderRadius.circular(2.0),
                  ),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Visibility(
                          visible: specItem.imageUrl != null,
                          child: ClipRRect(
                            // 设置左侧圆角半径，右侧为 0
                            borderRadius: BorderRadius.circular(2.0), // 设置圆角半径
                            child: CachedNetworkImage(
                              height: 44,
                              width: 48,
                              fit: BoxFit.cover,
                              imageUrl: specItem.imageUrl ?? '',
                              placeholder: (context, url) =>
                                  const CircularProgressIndicator(),
                            ),
                          ),
                        ),
                        Visibility(
                          child: Container(
                            width: 118,
                            padding: EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            child: Text(specItem.itemName!,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: Colors.black, fontSize: 12)),
                          ),
                        ),
                      ]),
                ),
              ),
              Visibility(
                visible: specItem.imageUrl == '',
                child: Container(
                  decoration: BoxDecoration(
                    border: Border.all(
                      color:
                          specItem.selected ? Colors.red : Colors.grey.shade400,
                      width: disabled == true ? 0 : 0.5,
                    ),
                    color:
                        disabled == true ? Colors.grey.shade200 : Colors.white,
                    borderRadius: BorderRadius.circular(4.0),
                  ),
                  padding:
                      const EdgeInsets.symmetric(horizontal: 6, vertical: 6),
                  child: Text(
                    specItem.itemName!,
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ),
              Positioned(
                right: -5,
                top: -10,
                child: Visibility(
                    visible: specIndex == 0 && specItem.cartQty! > 0,
                    child: Container(
                      padding: const EdgeInsets.all(2),
                      decoration: BoxDecoration(
                        color: Colors.red,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Text(
                        'X${specItem.cartQty}',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                        ),
                      ),
                    )),
              ),
            ],
          )),
    );
  }

  // 展示SKU列表
  Widget _buildGoodsSkuList(StateSetter setState) {
    return Visibility(
      visible: editForm.skuList.isNotEmpty,
      child: Container(
        width: screenWidth,
        margin: EdgeInsets.only(top: 6),
        decoration: BoxDecoration(
          color: Colors.grey.shade200, // 设置背景颜色为灰色
          borderRadius: BorderRadius.circular(2), // 设置圆角半径为 10
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Container(
              padding: EdgeInsets.only(left: 6, top: 6),
              child: Text.rich(TextSpan(
                children: [
                  TextSpan(
                      text: I18n.of(context)!
                          .translate("cm_goods.selectedOptions"),
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                  TextSpan(
                      text:
                          '(${I18n.of(context)!.translate("cm_goods.selectedNum")}: ${editForm.totalSpecItem})',
                      style:
                          TextStyle(fontSize: 14, fontWeight: FontWeight.w500)),
                ],
              )),
            ),
            Container(
              padding: EdgeInsets.only(top: 2, left: 6, bottom: 6),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  // 第一列
                  SizedBox(
                    width: skuNameWidth, // 设置第一列的宽度为 100
                    child: Text(
                      I18n.of(context)!.translate("cm_goods.specifications"),
                      style: TextStyle(fontSize: 14, color: Colors.black54),
                    ),
                  ),
                  // 第二列
                  SizedBox(
                    width: skuPriceWidth, // 设置第二列的宽度为 100
                    child: Center(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            I18n.of(context)!.translate("cm_goods.specPrice"),
                            style:
                                TextStyle(fontSize: 14, color: Colors.black54),
                          ),
                          Text(
                            '(US\$)',
                            style:
                                TextStyle(fontSize: 14, color: Colors.black54),
                          ),
                        ],
                      ),
                    ),
                  ),
                  // 第三列
                  SizedBox(
                    width: cartQtyWidth, // 设置第三列的宽度为 100
                    child: Center(
                      child: Text(
                        I18n.of(context)!.translate("cm_goods.quantity"),
                        style: TextStyle(
                            fontSize: 14, color: Colors.grey.shade500),
                      ),
                    ),
                  ),
                ],
              ),
            ),
            ...editForm.skuList.map((sku) => _buildGoodsSkuItem(setState, sku)),
          ],
        ),
      ),
    );
  }

  Widget _buildGoodsSkuItem(StateSetter setState, GoodsSkuInfoModel sku) {
    return Container(
      decoration: BoxDecoration(
        border: Border.all(
          color: sku.selected ? Colors.red : Colors.grey, // 设置边框颜色为红色
          width: 0.5, // 设置边框宽度为 2
        ),
        borderRadius: BorderRadius.circular(4), //
      ),
      child: CartSkuItem(
        skuItem: sku,
        updateSkuQuantity: (GoodsSkuInfoModel skuItem, int qty) {
          setState(() {
            onCartQtyUpdate(skuItem, qty);
          });
        },
        increaseQuantity: (GoodsSkuInfoModel skuItem) {
          setState(() {
            onIncreaseQuantity(skuItem);
          });
        },
        decreaseQuantity: (GoodsSkuInfoModel skuItem) {
          setState(() {
            onDecreaseQuantity(setState, skuItem);
          });
        },
      ),
    );
  }

  // 总价值
  Widget _buildTotalAmount(StateSetter setState) {
    return Visibility(
      visible: editForm.skuList.isNotEmpty,
      child: Container(
          padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
          child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
            Text(I18n.of(context)!.translate("cm_goods.totalAmount"),
                style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade800,
                    fontWeight: FontWeight.w500)),
            Container(
                padding: EdgeInsets.only(left: 24),
                child: Text(setUnit(editForm.totalMoneyAmount),
                    style: TextStyle(
                        fontSize: 14,
                        color: Colors.grey.shade800,
                        fontWeight: FontWeight.w500)))
          ])),
    );
  }

  // 全部费用
  Widget _buildGoodsAmount(StateSetter setState) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          const Divider(
            color: Color.fromARGB(255, 212, 210, 210), // 分割线颜色
            thickness: 0.5, // 分割线厚度
            indent: 0.0, // 分割线左侧缩进
            endIndent: 0.0, // 分割线右侧缩进
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  I18n.of(context)!.translate("cm_goods.deliveryFee"),
                  style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade800,
                      fontWeight: FontWeight.w500),
                ),
                // 预估运费
                Container(
                  padding: EdgeInsets.only(left: 24),
                  child: GestureDetector(
                      onTap: () {
                        showModalBottomSheet(
                            context: context,
                            isScrollControlled: true,
                            isDismissible: true, // 点击蒙层关闭
                            enableDrag: false, // 拖动关闭
                            builder: (BuildContext context) {
                              return SizedBox(
                                height: screenHeight * 0.7,
                                child: CountrySelectDrawer(
                                  onCountrySelect: (SiteListModel? siteData) {
                                    setState(() {
                                      editForm.siteData = siteData;
                                    });
                                  },
                                  onCloseDrawer: () {
                                    Get.back();
                                  },
                                ),
                              );
                            });
                      },
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.start,
                        children: [
                          Text(I18n.of(context)
                                  ?.translate("cm_common.deliveryTo") ??
                              ''),
                          Container(
                            padding: EdgeInsets.only(left: 4),
                            child: CachedNetworkImage(
                              height: 14,
                              width: 20,
                              fit: BoxFit.cover,
                              imageUrl: editForm.siteData?.logo ?? '',
                              placeholder: (context, url) =>
                                  const CircularProgressIndicator(),
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(left: 4),
                            child: Text(editForm.siteData?.code ?? ''),
                          )
                        ],
                      )),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 展示运费
  Widget _buildShippingCost(StateSetter setState) {
    return Visibility(
        visible: (editForm.skuList.isEmpty || editForm.routeList.isEmpty) &&
            (pageData?.pcsEstimateFreight ?? 0) > 0,
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8, vertical: 6),
            child: Text.rich(TextSpan(children: [
              TextSpan(
                  text:
                      '${I18n.of(context)?.translate("cm_goods.shippingCost")} ',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  )),
              TextSpan(
                  text: '${setUnit(pageData?.pcsEstimateFreight)} ',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  )),
              TextSpan(
                  text: '/',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  )),
              TextSpan(
                  text: '${pageData?.goodsPriceUnitName}',
                  style: TextStyle(
                    color: Colors.grey.shade600,
                  )),
            ]))));
  }

  // 订单提交后，客服将确认运费
  Widget _buildFreightConfirm(StateSetter setState) {
    return Visibility(
        visible: (editForm.skuList.isEmpty || editForm.routeList.isEmpty) &&
            (pageData?.pcsEstimateFreight ?? 0) <= 0,
        child: Container(
            padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 8.sp),
            child: Text(
                '${I18n.of(context)?.translate("cm_goods.freightConfirmation")}')));
  }

  Widget _buildRouteList(StateSetter setState) {
    List<CalculateRouteFreightModel> routeList = editForm.showExpandedRoutes
        ? editForm.routeList
        : editForm.routeList.take(3).toList();

    return Visibility(
        visible: editForm.skuList.isNotEmpty && editForm.routeList.isNotEmpty,
        child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: routeList.map((routeItem) {
              return GestureDetector(
                onTap: () {
                  onSelectRoute(routeItem);
                },
                child: Container(
                  width: screenWidth,
                  margin: EdgeInsets.only(left: 6, right: 6, bottom: 6),
                  padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                      border: Border.all(
                        color: routeItem.selected
                            ? Colors.red
                            : Colors.grey.shade300, // 边框颜色
                        width: 1, // 边框宽度
                      ),
                      borderRadius: BorderRadius.circular(12), // 圆角半径
                      color: Colors.white),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(routeItem.routeName ?? '',
                          style: TextStyle(fontWeight: FontWeight.w500)),
                      Container(
                        padding: EdgeInsets.only(top: 2),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                                '${I18n.of(context)?.translate("cm_goods.shippingCost") ?? ''} ',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                )),
                            Container(
                              padding: EdgeInsets.only(right: 8),
                              child: Text.rich(TextSpan(children: [
                                TextSpan(
                                    text:
                                        '${setUnit(routeItem.totalEstimateFreight)} ',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    )),
                                TextSpan(
                                    text:
                                        '${I18n.of(context)?.translate("cm_goods.perUnit")} ',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    )),
                                TextSpan(
                                    text: '${routeItem.freightGoodsQty} ',
                                    style: TextStyle(
                                      color: Colors.grey.shade600,
                                    )),
                                TextSpan(
                                    text: '${pageData?.goodsPriceUnitName}',
                                    style: TextStyle(
                                      fontSize: 12,
                                      color: Colors.grey.shade600,
                                    )),
                              ])),
                            )
                          ],
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.only(top: 2),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(
                              I18n.of(context)
                                      ?.translate("cm_goods.estimatedTime") ??
                                  '',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                              ),
                            ),
                            Container(
                              padding: EdgeInsets.only(right: 8),
                              child: Text(
                                routeItem.deliverTimeName ?? '',
                                style: TextStyle(
                                  color: Colors.grey.shade600,
                                ),
                              ),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            }).toList()));
  }

  // 路由列表
  Widget _buildRouteList2(StateSetter setState) {
    List<CalculateRouteFreightModel> routeList = editForm.showExpandedRoutes
        ? editForm.routeList
        : editForm.routeList.take(3).toList();

    return Visibility(
        visible: editForm.skuList.isNotEmpty && editForm.routeList.isNotEmpty,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: routeList.map((routeItem) {
            return Container(
              width: screenWidth,
              margin: EdgeInsets.only(left: 6, right: 6, bottom: 6),
              padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                  border: Border.all(
                    color: routeItem.selected
                        ? Colors.red
                        : Colors.grey.shade300, // 边框颜色
                    width: 1, // 边框宽度
                  ),
                  borderRadius: BorderRadius.circular(12), // 圆角半径
                  color: Colors.white),
              child: GestureDetector(
                onTap: () {
                  onSelectRoute(routeItem);
                },
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(routeItem.routeName ?? '',
                        style: TextStyle(fontWeight: FontWeight.w500)),
                    Container(
                      padding: EdgeInsets.only(top: 2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                              '${I18n.of(context)?.translate("cm_goods.shippingCost") ?? ''} ',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                              )),
                          Container(
                            padding: EdgeInsets.only(right: 8),
                            child: Text.rich(TextSpan(children: [
                              TextSpan(
                                  text:
                                      '${setUnit(routeItem.totalEstimateFreight)} ',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                  )),
                              TextSpan(
                                  text:
                                      '${I18n.of(context)?.translate("cm_goods.perUnit")} ',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                  )),
                              TextSpan(
                                  text: '${routeItem.freightGoodsQty} ',
                                  style: TextStyle(
                                    color: Colors.grey.shade600,
                                  )),
                              TextSpan(
                                  text: '${pageData?.goodsPriceUnitName}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  )),
                            ])),
                          )
                        ],
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(top: 2),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            I18n.of(context)
                                    ?.translate("cm_goods.estimatedTime") ??
                                '',
                            style: TextStyle(
                              color: Colors.grey.shade600,
                            ),
                          ),
                          Container(
                            padding: EdgeInsets.only(right: 8),
                            child: Text(
                              routeItem.deliverTimeName ?? '',
                              style: TextStyle(
                                color: Colors.grey.shade600,
                              ),
                            ),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              ),
            );
          }).toList(),
        ));
  }

  // 全部费用
  Widget _buildTotalCost(StateSetter setState) {
    return Visibility(
        visible: editForm.skuList.isNotEmpty && editForm.totalCost > 0,
        child: Container(
          padding: EdgeInsets.symmetric(vertical: 6),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Container(
                padding: EdgeInsets.only(left: 10),
                child: Text(
                  I18n.of(context)!.translate("cm_goods.totalCost"),
                  style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade700,
                      fontWeight: FontWeight.w500),
                ),
              ),
              Container(
                padding: EdgeInsets.only(right: 10),
                child: Text.rich(TextSpan(children: [
                  TextSpan(
                      text: setUnit(editForm.totalCost),
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                  TextSpan(
                      text: '(',
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                  TextSpan(
                      text: setUnit(editForm.unitPrice),
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                  TextSpan(
                      text: '/',
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                  TextSpan(
                      text: pageData?.goodsPriceUnitName,
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                  TextSpan(
                      text: ')',
                      style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade700,
                          fontWeight: FontWeight.w500)),
                ])),
              )
            ],
          ),
        ));
  }
}

// 购物车更换SKU
class GoodsSkuChangeDrawer extends StatefulWidget {
  final String goodsId;
  final MidCartSkuModel sku;
  final BuildContext context; // 添加 required 参数
  final Function(dynamic param) onChangeSkuConfirm;

  const GoodsSkuChangeDrawer(
      {super.key,
      required this.goodsId,
      required this.context,
      required this.sku,
      required this.onChangeSkuConfirm});

  @override
  State<GoodsSkuChangeDrawer> createState() => _GoodsSkuChangeDrawerState();
}

class _GoodsSkuChangeDrawerState extends State<GoodsSkuChangeDrawer> {
  bool _isLoading = true;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  GoodsInfoModel? goodsData;
  Map<String, String> currentSpecMap = {};

  @override
  void initState() {
    super.initState();
    onPageData();
  }

  Future<void> onPageData() async {
    try {
      setState(() => _isLoading = true);
      dynamic res = await ProductAPI.useGoodsInfo({
        "id": widget.goodsId,
        "deviceType": 1,
      });
      if (res?['result']?['code'] == 200) {
        setState(() {
          goodsData = GoodsInfoModel.fromJson(res?['data']);
        });
      }
      setState(() => _isLoading = false);
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 确认按钮点击事件
  Future<void> onSkuChangeConfirm() async {
    List<String> specItemIds = [];

    for (GoodsSkuSpecModel goodsSpec in (goodsData?.specList ?? [])) {
      for (GoodsSkuSpecItemModel specItem in (goodsSpec.items ?? [])) {
        if (specItem.selected == true) {
          specItemIds.add(specItem.itemId);
          break;
        }
      }
    }

    GoodsSkuInfoModel? sku = (goodsData?.skuList ?? []).firstWhereOrNull(
        (sku) => specItemIds.every((specItemId) =>
            (sku.specItems ?? []).any((item) => item.itemId == specItemId)));
    if (sku == null) {
      return;
    }

    widget.onChangeSkuConfirm({
      "selected": true,
      "updatedSkuId": sku.id,
      "skuId": widget.sku.skuId,
      "goodsId": goodsData?.id,
      "padc": widget.sku.padc,
    });
  }

  // 判断商品规格是否禁用
  bool onSpecItemDisabled(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    // 判断商品规格列表的数量
    if (goodsData?.specList == null || specItem.disabled == true) {
      return true; // 如果规格列表为空，直接返回 true
    }

    if (goodsData?.specList?.length == 1) {
      return specItem.disabled;
    } else if (currentSpecMap.isEmpty) {
      return specItem.disabled;
    } else {
      // 使用已选择的规格筛选出匹配的SKU，判断SKU的库存
      GoodsSkuInfoModel? sku = onSelectSkuBySpec(goodsSpec, specItem);
      if (sku != null && (sku.stockQty ?? 0) <= 0) {
        return true;
      }

      return false;
    }
  }

  // 根据商品规格聚焦一个有效的SKU
  GoodsSkuInfoModel? onSelectSkuBySpec(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    List<String> specItemIds = [specItem.itemId];
    try {
      // 筛选出其他的商品规格
      for (var item in currentSpecMap.entries) {
        if (item.key != goodsSpec?.id && !specItemIds.contains(item.value)) {
          specItemIds.add(item.value);
        }
      }
      if (specItemIds.length < (goodsData?.specList ?? []).length) {
        return null;
      }

      GoodsSkuInfoModel? sku = (goodsData?.skuList ?? []).firstWhere((sku) =>
          specItemIds.every((specItemId) =>
              (sku.specItems ?? []).any((item) => item.itemId == specItemId)));

      if ((sku.stockQty ?? -1) <= 0) {
        return GoodsSkuInfoModel(cartQty: -1, selected: false);
      } else if ((sku.stockQty ?? 0) > 0) {
        sku.cartQty =
            sku.minIncreaseQuantity ?? (goodsData?.minIncreaseQuantity ?? 0);
        sku.price = onStepPriceByQuantity(sku);
        return sku;
      }

      return null;
    } catch (e) {
      showErrorMessage(e.toString());
      return GoodsSkuInfoModel(cartQty: -1, selected: false);
    }
  }

  // 根据加购数量筛选价格
  double? onStepPriceByQuantity(GoodsSkuInfoModel sku) {
    for (SkuStepPrice stepPrice in sku.stepPrices!) {
      if (sku.cartQty < stepPrice.start) {
        return stepPrice.price;
      }
      if (sku.cartQty >= stepPrice.start && sku.cartQty <= stepPrice.end) {
        return stepPrice.price;
      }
      if (stepPrice.end == -1) {
        return stepPrice.price;
      }
    }
    return 0;
  }

  // 商品规格选择, 只有最后一种规格可以选择多个
  void onSpecItemSelected(
      GoodsSkuSpecModel? goodsSpec, GoodsSkuSpecItemModel specItem) {
    try {
      setState(() {
        goodsData?.specList = goodsData?.specList?.map((spec) {
          if (spec.id == goodsSpec?.id) {
            spec.items = spec.items?.map((item) {
              if (item.itemId == specItem.itemId) {
                item.selected = true;
              } else if (item.selected == true) {
                item.selected = false;
              }

              return item;
            }).toList();
          }
          return spec;
        }).toList();
      });
    } finally {}
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(widget.context).size.width;
    screenHeight = MediaQuery.of(widget.context).size.height;

    return _isLoading
        ? LoadingWidget(showType: "Animated")
        : AppScaffold(
            backgroundColor: Colors.white,
            body: Container(
              width: screenWidth,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(20),
                  topRight: Radius.circular(20),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 中间滚动部分
                  Expanded(
                    child: SingleChildScrollView(
                      child: StatefulBuilder(builder: (context, setState) {
                        return _buildGoodsSpecList();
                      }),
                    ),
                  ),
                  // 底部固定部分
                  _buildFixedFooter(),
                ],
              ),
            ));
  }

  Widget _buildFixedFooter() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
      ),
      child: Row(
        children: [
          // 确认按钮
          Expanded(
            child: ElevatedButton(
              onPressed: onSkuChangeConfirm,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                padding: const EdgeInsets.symmetric(vertical: 14),
                elevation: 0,
              ),
              child: Text(
                I18n.of(context)?.translate("cm_find.confirm") ?? '',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ),
          const SizedBox(width: 12),
          // 取消按钮
          Expanded(
            child: OutlinedButton(
              onPressed: () => Navigator.pop(context),
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 14),
                side: BorderSide(color: Colors.grey[300]!),
              ),
              child: Text(
                I18n.of(context)?.translate("cm_find.cancel") ?? '',
                style: TextStyle(color: Colors.black87, fontSize: 16),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGoodsSpecList() {
    return Container(
      padding: const EdgeInsets.only(top: 6, left: 6, right: 6),
      child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: goodsData?.specList?.length,
          itemBuilder: (context, index) {
            GoodsSkuSpecModel? specItem = goodsData?.specList?[index];
            return _buildGoodsSpecItems(specItem, index);
          }),
    );
  }

  // 商品规格元素列表
  Widget _buildGoodsSpecItems(GoodsSkuSpecModel? spec, int specIndex) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.stretch,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 4),
          child: Text(spec?.name ?? "",
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500)),
        ),
        Container(
          padding: EdgeInsets.all(2),
          child: Wrap(
            spacing: 8.0, // 水平间距
            runSpacing: 8.0, // 垂直间距
            children: (spec?.items ?? []).map((item) {
              return _buildGoodsSpecItem(spec, item, specIndex);
            }).toList(),
          ),
        )
      ],
    );
  }

  // 商品规格元素
  Widget _buildGoodsSpecItem(
      GoodsSkuSpecModel? spec, GoodsSkuSpecItemModel specItem, int specIndex) {
    bool disabled = onSpecItemDisabled(spec, specItem);

    return AbsorbPointer(
      absorbing: disabled,
      child: GestureDetector(
          onTap: () {
            setState(() {
              onSpecItemSelected(spec, specItem);
            });
          },
          child: Stack(
            clipBehavior: Clip.none,
            children: [
              Visibility(
                visible: specIndex == 0 && specItem.imageUrl != '',
                child: Container(
                  width: 168,
                  decoration: BoxDecoration(
                    border: specItem.selected
                        ? null
                        : Border.all(color: Colors.grey.shade400, width: 0.5),
                    color: disabled == true
                        ? Colors.grey.shade200
                        : specItem.selected
                            ? Colors.red.shade100
                            : Colors.white,
                    borderRadius: BorderRadius.circular(4.0.sp),
                  ),
                  child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Visibility(
                          visible: specItem.imageUrl != null,
                          child: ClipRRect(
                            // 设置左侧圆角半径，右侧为 0
                            borderRadius: BorderRadius.circular(2.0), // 设置圆角半径
                            child: CachedNetworkImage(
                              height: 44,
                              width: 48,
                              fit: BoxFit.cover,
                              imageUrl: specItem.imageUrl ?? '',
                              placeholder: (context, url) =>
                                  const CircularProgressIndicator(),
                            ),
                          ),
                        ),
                        Visibility(
                          child: Container(
                            width: 118,
                            padding: EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            child: Text(specItem.itemName!,
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                                style: TextStyle(
                                    color: Colors.black, fontSize: 12)),
                          ),
                        ),
                      ]),
                ),
              ),
              Visibility(
                visible: specItem.imageUrl == '',
                child: Container(
                  decoration: BoxDecoration(
                    border: specItem.selected
                        ? null
                        : Border.all(color: Colors.grey.shade400, width: 0.5),
                    color: disabled == true
                        ? Colors.grey.shade200
                        : specItem.selected
                            ? Colors.red.shade100
                            : Colors.white,
                    borderRadius: BorderRadius.circular(4.0.sp),
                  ),
                  padding:
                      EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
                  child: Text(
                    specItem.itemName!,
                    style: TextStyle(fontSize: 12),
                  ),
                ),
              ),
              Visibility(
                  visible: specItem.selected,
                  child: Positioned(
                    right: -2,
                    bottom: 0,
                    child: Icon(
                      Icons.check_box,
                      color: AppColors.primaryColor,
                      size: 20,
                    ),
                  ))
            ],
          )),
    );
  }
}

class EditForm {
  double totalCost;
  double? unitPrice;
  double? totalMoneyAmount;
  int? totalSpecItem;
  String? selectedRouteId;
  List<GoodsSkuInfoModel> skuList;
  double? totalEstimateFreight;
  double? partEstimateFreight;
  int? freightGoodsQty;
  CalculateRouteFreightModel? selectedRoute;
  List<CalculateRouteFreightModel> routeList;
  bool showExpandedRoutes;
  SiteListModel? siteData;
  String? errorMessage;

  EditForm({
    required this.totalCost,
    this.unitPrice,
    this.totalMoneyAmount,
    this.totalSpecItem,
    this.selectedRouteId,
    required this.skuList,
    this.totalEstimateFreight,
    this.partEstimateFreight,
    this.freightGoodsQty,
    this.selectedRoute,
    required this.routeList,
    required this.showExpandedRoutes,
    this.siteData,
    this.errorMessage,
  });
}
