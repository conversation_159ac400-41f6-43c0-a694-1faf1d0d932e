import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';

class MineInquiryPage extends StatefulWidget {
  const MineInquiryPage({super.key});

  @override
  State<MineInquiryPage> createState() => _MineInquiryPageState();
}

class _MineInquiryPageState extends State<MineInquiryPage> {
  final ScrollController _scrollController = ScrollController();
  final RxList<dynamic> inquiryList = <dynamic>[].obs;
  final RxBool isLoading = false.obs;
  final RxBool noInquiryData = false.obs;
  final Rx<Map<String, dynamic>> pageInfo = Rx<Map<String, dynamic>>({
    'current': 1,
    'size': 10,
  });

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScrollBottom);
    _fetchInquiryList();
  }

  @override
  void dispose() {
    _scrollController.removeListener(_onScrollBottom);
    _scrollController.dispose();
    super.dispose();
  }

  Future<void> _fetchInquiryList({bool scroll = false}) async {
    if (isLoading.value) return; // 防止重复加载

    try {
      isLoading.value = true;
      var res = await UserAPI.useGoodsLookingList({
        'page': pageInfo.value,
      });

      if (res['result']['code'] == 200) {
        // 如果是滚动加载且没有新数据，则标记已到底
        if (scroll && (res['data'] == null || res['data'].isEmpty)) {
          noInquiryData.value = true;
          return;
        }

        if (scroll) {
          inquiryList.addAll(res['data'] ?? []);
        } else {
          inquiryList.value = res['data'] ?? [];
          if (res['page'] != null) {
            pageInfo.value = Map<String, dynamic>.from(res['page']);
          }
        }
      } else {
        if (scroll) {
          pageInfo.update((val) {
            val?['current'] = (val['current'] ?? 1) - 1;
          });
        }
      }
    } catch (e) {
      print('Error fetching inquiry list: $e');
      if (scroll) {
        pageInfo.update((val) {
          val?['current'] = (val['current'] ?? 1) - 1;
        });
      }
    } finally {
      isLoading.value = false;
    }
  }

  void _onScrollBottom() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      if (!isLoading.value && !noInquiryData.value) {
        pageInfo.update((val) {
          val?['current'] = (val['current'] ?? 1) + 1;
        });
        _fetchInquiryList(scroll: true);
      }
    }
  }

  Future<void> _onRefresh() async {
    noInquiryData.value = false;
    pageInfo.value = {
      'current': 1,
      'size': 10,
    };
    await _fetchInquiryList();
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: Color(0xFFF2F2F2),
      showScrollToTopButton: true,
      scrollController: _scrollController,
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 1,
        leading: IconButton(
          icon: Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          I18n.of(context)?.translate('cm_inquiry.myInquiry') ?? '询盘记录',
        ),
        centerTitle: true,
      ),
      body: Obx(() {
        if (inquiryList.isEmpty && !isLoading.value) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.inbox_outlined,
                  size: 48.sp,
                  color: Colors.grey[400],
                ),
                SizedBox(height: 16.sp),
                Text(
                  I18n.of(context)?.translate('cm_find.noData') ?? '暂无询盘记录',
                  style: TextStyle(
                    fontSize: 16.sp,
                    color: Colors.grey[600],
                  ),
                ),
                SizedBox(height: 8.sp),
                Text(
                  '快去挑选心仪的商品吧',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.grey[400],
                  ),
                ),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: _onRefresh,
          color: AppColors.primaryColor,
          child: ListView.builder(
            controller: _scrollController,
            itemCount: inquiryList.length + 2,
            physics: const AlwaysScrollableScrollPhysics(),
            padding: EdgeInsets.symmetric(vertical: 8.sp),
            itemBuilder: (context, index) {
              if (index == 0) {
                return Container(
                  margin: EdgeInsets.only(
                      left: 16.sp, right: 16.sp,top:4.sp),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(10.sp),
                  ),
                  padding: EdgeInsets.all(12.sp),
                  child: Text(
                    I18n.of(context)?.translate('cm_find_submitTip') ??
                        'Después de enviar pedido, cotizamos el transporte por WhatsApp. ¡Por favor, no pierdas los mensajeas en WhatsApp!',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: AppColors.primaryColor,
                    ),
                  ),
                );
              }

              if (index == inquiryList.length + 1) {
                return Container(
                  padding: EdgeInsets.symmetric(vertical: 20.sp),
                  alignment: Alignment.center,
                  child: noInquiryData.value
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Container(
                              width: 80.sp,
                              height: 1,
                              color: Colors.grey[300],
                            ),
                            Padding(
                              padding: EdgeInsets.symmetric(horizontal: 16.sp),
                              child: Text(
                                '已经到底啦 ~',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Colors.grey[400],
                                ),
                              ),
                            ),
                            Container(
                              width: 80.sp,
                              height: 1,
                              color: Colors.grey[300],
                            ),
                          ],
                        )
                      : isLoading.value
                          ? Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                SizedBox(
                                  width: 16.sp,
                                  height: 16.sp,
                                  child: CircularProgressIndicator(
                                    strokeWidth: 2,
                                    valueColor: AlwaysStoppedAnimation<Color>(
                                      AppColors.primaryColor,
                                    ),
                                  ),
                                ),
                                SizedBox(width: 8.sp),
                                Text(
                                  '加载中...',
                                  style: TextStyle(
                                    fontSize: 14.sp,
                                    color: Colors.grey[600],
                                  ),
                                ),
                              ],
                            )
                          : SizedBox(),
                );
              }

              final inquiry = inquiryList[index - 1];
              final skus = inquiry['skus'] as List<dynamic>?;

              return Container(
                margin: EdgeInsets.symmetric(
                    vertical: 10.0.sp, horizontal: 16.0.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(10.0.sp),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 10.0.sp, horizontal: 10.0.sp),
                      decoration: BoxDecoration(
                        color: Colors.green[50],
                        borderRadius: BorderRadius.vertical(
                            top: Radius.circular(10.0.sp)),
                        border: Border(
                          bottom: BorderSide(
                              color: Colors.green[400]!, width: 0.8.sp),
                        ),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            inquiry['goodsLookingNo']?.toString() ?? '',
                            style: TextStyle(
                              fontSize: 14.sp,
                            ),
                          ),
                          Text(
                            timeFormatByZone(inquiry['submitTime'] ?? ''),
                            style: TextStyle(
                              fontSize: 14.sp,
                            ),
                          ),
                        ],
                      ),
                    ),
                    if (skus != null)
                      ...skus.map<Widget>((sku) {
                        return Padding(
                          padding: EdgeInsets.all(10.0.sp),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              CachedNetworkImage(
                                imageUrl: sku['skuImage'] ?? '',
                                width: 80.sp,
                                height: 80.sp,
                                fit: BoxFit.cover,
                                placeholder: (context, url) => Container(
                                  color: Colors.grey[200],
                                  child: Center(
                                    child: SizedBox(
                                      width: 20.sp,
                                      height: 20.sp,
                                      child: CircularProgressIndicator(
                                        strokeWidth: 2,
                                      ),
                                    ),
                                  ),
                                ),
                                errorWidget: (context, url, error) => Container(
                                  color: Colors.grey[200],
                                  child: Icon(Icons.error),
                                ),
                              ),
                              SizedBox(width: 10.0.sp),
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      sku['goodsName']?.toString() ?? '',
                                      style: TextStyle(fontSize: 13.sp),
                                      overflow: TextOverflow.ellipsis,
                                      maxLines: 2,
                                    ),
                                    Padding(
                                      padding:
                                          EdgeInsets.symmetric(vertical: 2.sp),
                                      child: Text(
                                        _getSpecsString(
                                            sku['specs'] as List<dynamic>?),
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: Color(0xFF7F7F7F),
                                        ),
                                      ),
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          setUnit(sku['salePrice'] ?? 0),
                                          style: TextStyle(
                                              fontSize: 13.sp,
                                              color: AppColors.primaryColor),
                                        ),
                                        Text(
                                            'x ${sku['buyQuantity']?.toString() ?? '0'}',
                                            style: TextStyle(
                                              fontSize: 13.sp,
                                            )),
                                      ],
                                    ),
                                    SizedBox(height: 2.sp),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          I18n.of(context)?.translate(
                                                  'cm_inquiry.shippingFee') ??
                                              'Costo de envío',
                                          style: TextStyle(
                                            fontSize: 13.sp,
                                            color: Color(0xFF7F7F7F),
                                          ),
                                        ),
                                        Text(
                                          sku['estimateFreight'] != null &&
                                                  sku['estimateFreight'] !=
                                                      false
                                              ? setUnit(sku['estimateFreight'])
                                              : I18n.of(context)?.translate(
                                                      'cm_goods.pendingConfirmation') ??
                                                  'Por confirmar',
                                          style: TextStyle(
                                            fontSize: 13.sp,
                                            color: Color(0xFF7F7F7F),
                                          ),
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        );
                      }).toList(),
                    Container(
                      padding: EdgeInsets.symmetric(
                          vertical: 15.0.sp, horizontal: 10.sp),
                      decoration: BoxDecoration(
                        border: Border(
                          top: BorderSide(color: Colors.grey[300]!),
                        ),
                      ),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                I18n.of(context)
                                        ?.translate('cm_find_itemsCost') ??
                                    'Subtotal de artículos',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                setUnit(inquiry['subTotal'] ?? 0),
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 2.sp),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                I18n.of(context)?.translate(
                                        'cm_inquiry.shippingFeeTotal') ??
                                    'Costo de envío total',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              Text(
                                inquiry['totalEstimateFreight'] != null &&
                                        inquiry['totalEstimateFreight'] != false
                                    ? setUnit(
                                        inquiry['totalEstimateFreight'] ?? 0)
                                    : I18n.of(context)?.translate(
                                            'cm_goods.pendingConfirmation') ??
                                        'Por confirmar',
                                style: TextStyle(
                                  color: AppColors.primaryColor,
                                  fontSize: 14.sp,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        );
      }),
    );
  }

  String _getSpecsString(List<dynamic>? specs) {
    if (specs == null || specs.isEmpty) return '';
    try {
      return specs
          .map((spec) => '${spec['specName'] ?? ''}: ${spec['itemName'] ?? ''}')
          .where((spec) => spec.isNotEmpty)
          .join('; ');
    } catch (e) {
      print('Error formatting specs: $e');
      return '';
    }
  }
}
