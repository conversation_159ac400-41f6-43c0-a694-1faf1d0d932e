// 自定义折叠面板-完全控制动画
import 'package:flutter/material.dart';

class CollapsiblePanel extends StatefulWidget {
  final String title;
  final Widget content;
  final bool initiallyExpanded; // 是否默认展开
  final Function(bool) onExpansionChanged;

  const CollapsiblePanel({
    super.key,
    required this.title,
    required this.content,
    this.initiallyExpanded = false,
    required this.onExpansionChanged,
  });

  @override
  State<CollapsiblePanel> createState() => _CollapsiblePanelState();
}

class _CollapsiblePanelState extends State<CollapsiblePanel>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _heightFactor;
  bool _isExpanded = false;

  @override
  void initState() {
    super.initState();
    _isExpanded = widget.initiallyExpanded;
    _controller = AnimationController(
      vsync: this,
      duration: Duration(milliseconds: 300),
    );
    _heightFactor = CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    );

    if (_isExpanded) _controller.value = 1.0;
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  void _toggleExpanded() {
    setState(() {
      _isExpanded = !_isExpanded;
      if (_isExpanded) {
        _controller.forward();
      } else {
        _controller.reverse();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.symmetric(vertical: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // 标题栏
          Container(
            decoration: BoxDecoration(
              color: Colors.grey[200],
              borderRadius: BorderRadius.circular(8),
            ),
            child: ListTile(
              title: Text(widget.title),
              trailing: Icon(
                _isExpanded ? Icons.expand_less : Icons.expand_more,
                color: Colors.blue,
              ),
              onTap: _toggleExpanded,
            ),
          ),

          // 内容区域（带动画）
          ClipRect(
            child: SizeTransition(
              axisAlignment: 0.0,
              sizeFactor: _heightFactor,
              child: Container(
                margin: EdgeInsets.only(top: 4),
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey[300]!),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: widget.content,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
