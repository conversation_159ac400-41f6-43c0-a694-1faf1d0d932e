import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/login/login_page.dart';
import 'package:chilat2_mall_app/pages/login/register_page.dart';
import 'package:chilat2_mall_app/pages/login/modify_password_page.dart';
import 'package:chilat2_mall_app/pages/login/terms_page.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';

/// 认证页面类型枚举
enum AuthPageType {
  login, // 登录
  register, // 注册
  resetPwd, // 重置密码
  terms // 用户协议
}

/// 认证模态框组件
///
/// 提供统一的认证界面，在同一个模态框内无闪烁地切换登录、注册、修改密码和协议页面
class AuthModal extends StatefulWidget {
  /// 初始显示的页面类型
  final AuthPageType initialType;

  /// 认证成功后的重定向路由
  final String? redirectRoute;

  /// 认证成功后的回调函数
  final VoidCallback? onAuthSuccess;

  const AuthModal({
    Key? key,
    this.initialType = AuthPageType.login,
    this.redirectRoute,
    this.onAuthSuccess,
  }) : super(key: key);

  @override
  State<AuthModal> createState() => _AuthModalState();
}

class _AuthModalState extends State<AuthModal>
    with SingleTickerProviderStateMixin {
  late AuthPageType _currentType;
  late AnimationController _animController;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _currentType = widget.initialType;

    // 初始化动画控制器
    _animController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animController,
        curve: Curves.easeInOut,
      ),
    );

    // 启动初始动画
    _animController.forward();
  }

  @override
  void dispose() {
    _animController.dispose();
    super.dispose();
  }

  // 切换模态框内容类型
  void _switchType(AuthPageType type) {
    if (_currentType == type) return;

    _animController.reverse().then((_) {
      setState(() {
        _currentType = type;
      });
      _animController.forward();
    });
  }

  void _onAuthSuccess(BuildContext context) {
    // 先关闭模态框
    Navigator.of(context).pop(true);

    // 如果提供了自定义回调，则执行回调
    if (widget.onAuthSuccess != null) {
      widget.onAuthSuccess!();
    }
    // 否则处理重定向逻辑
    else if (widget.redirectRoute != null && widget.redirectRoute!.isNotEmpty) {
      Future.delayed(const Duration(milliseconds: 200), () {
        final currentRoute = Get.currentRoute;

        // 如果当前在AuthBlockPage，直接跳转到目标页面
        if (currentRoute == AppRoutes.AuthBlockPage) {
          Get.offAllNamed(widget.redirectRoute!);
        }
        // 如果当前页面就是目标页面，刷新页面
        else if (currentRoute == widget.redirectRoute) {
          Get.offAndToNamed(widget.redirectRoute!);
        }
        // 否则正常跳转
        else {
          Get.toNamed(widget.redirectRoute!);
        }
      });
    }
  }

  void _onRegisterSuccess(BuildContext context) {
    // 先关闭模态框
    Navigator.of(context).pop(true);

    // 注册成功后跳转到注册成功页面
    Get.offNamed(AppRoutes.RegisterSuccess);
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop(false);
        return false;
      },
      child: Scaffold(
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          elevation: 0,
          automaticallyImplyLeading: false,
        ),
        body: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: _buildModalContent(context),
          ),
        ),
      ),
    );
  }

  // 构建模态框内容
  Widget _buildModalContent(BuildContext context) {
    switch (_currentType) {
      case AuthPageType.login:
        return LoginPage(
          onLoginSuccess: () => _onAuthSuccess(context),
          onGoRegister: () => _switchType(AuthPageType.register),
          onGoModifyPwd: () => _switchType(AuthPageType.resetPwd),
        );
      case AuthPageType.register:
        return RegisterPage(
          onRegisterSuccess: () => _onRegisterSuccess(context),
          onGoLogin: () => _switchType(AuthPageType.login),
          onOpenAgree: () => _switchType(AuthPageType.terms),
        );
      case AuthPageType.resetPwd:
        return ModifyPasswordPage(
          onModifySuccess: () => _onAuthSuccess(context),
          onGoLogin: () => _switchType(AuthPageType.login),
        );
      case AuthPageType.terms:
        return TermsPage(
          isModal: true,
          modalBackButtonType: AppBarBackType.None,
          onBack: () => _switchType(AuthPageType.register),
        );
      default:
        return Container();
    }
  }
}
