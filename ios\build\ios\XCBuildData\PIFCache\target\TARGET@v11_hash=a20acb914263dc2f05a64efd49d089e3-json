{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e982a2d73415e235cf88d7b7dcd3f7def6d", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98b89882dc28a6d5c860523e82f554cc84", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edf11b808c493b5d49456f6c496ce534", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98e29cd749ce03643e9abbafc9d7ee4c45", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98edf11b808c493b5d49456f6c496ce534", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/flutter_inappwebview_ios/flutter_inappwebview_ios.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "flutter_inappwebview_ios", "PRODUCT_NAME": "flutter_inappwebview_ios", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e982fd3c6a7f4d84d77d3348746052d187a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98ede51750570a408106537ed55980ff10", "guid": "bfdfe7dc352907fc980b868725387e98807bebc10b9b2347b3c38ab76dcd39c8", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98759a2a2d8e79d645434612336813063b", "guid": "bfdfe7dc352907fc980b868725387e98aa1ba6d3a4c5636982a0b11ec75ec7c7", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98e2f978f0bcd85da9c3f3f0ea66389fce", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9847cb72a869a1c73ceaf834ff374d6027", "guid": "bfdfe7dc352907fc980b868725387e98001b4c58b8885c239e7c9cfc8162ff88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981f63c33357150465a113a4a45495364a", "guid": "bfdfe7dc352907fc980b868725387e98649e84af51d805f931a99e3a879af211"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a54500776ba878821120c2d05acb7670", "guid": "bfdfe7dc352907fc980b868725387e98614e752d905a84e36ffbc18c04fde877"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98be13e81379187bc9c591e704395127a8", "guid": "bfdfe7dc352907fc980b868725387e986a4ab3ea7cd444f869c7e61719356315"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df72c77634e0c75efe85bef5d8e3df38", "guid": "bfdfe7dc352907fc980b868725387e981dd2baaa66cfa3606c70fa310516b5dd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981986e889736b21c3b3616805fdcb7348", "guid": "bfdfe7dc352907fc980b868725387e98a4ae8032158743a06339d230b8bb9cad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986b6045b7d3b427a07c8e2a1f1ca41aba", "guid": "bfdfe7dc352907fc980b868725387e98a8ea022ba491525feea6f5b086324bb7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9871655b9e35d73506ca8257cadd284fdc", "guid": "bfdfe7dc352907fc980b868725387e987e5605d651133e8639dd4ab55d771d12"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983bc5b7766d052088195076bf9addb4fe", "guid": "bfdfe7dc352907fc980b868725387e98c99abd1be29b18af2ae411ece7dfd584"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b80d3376c630e49d0a489ea26e25b35c", "guid": "bfdfe7dc352907fc980b868725387e9883a63701a443bb2d3d5dfd20aa2a3959"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98488372ccd18890c1296e15200abe4f24", "guid": "bfdfe7dc352907fc980b868725387e9832c8fc262b7d5526f94de1e9e9fd1583"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836754dd8db48443e5c2dc35fae631537", "guid": "bfdfe7dc352907fc980b868725387e983bc29f503464e9d18244d56d57e7352c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98377ab8202b37ac38b634e29713b324f6", "guid": "bfdfe7dc352907fc980b868725387e98875d00d858f6a0813e6515b9859589ad"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d3d322cd5f02dabf4e6d138b6003b877", "guid": "bfdfe7dc352907fc980b868725387e980bf22d10c184d62d3d79f9c7ead13353"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9825193cd024b9c98ea4bde8645e4a097e", "guid": "bfdfe7dc352907fc980b868725387e98bcf9f5181f2b337d84656c4acfd168de"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980a1e0f1377fe9a5c191c347fa7adbbd7", "guid": "bfdfe7dc352907fc980b868725387e985e94fbe2a497a9fb64c090d24e0d5690"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983cd3acd134147f0246ce261f3f7450e2", "guid": "bfdfe7dc352907fc980b868725387e98df1f036bb7fc71540f6a5a211c7e6323"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987639a4140e3ac79130a8222f2277b349", "guid": "bfdfe7dc352907fc980b868725387e9853f2f33185a849907003db2f1594c1e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834d8e3d526848509c47da2e855034bc2", "guid": "bfdfe7dc352907fc980b868725387e985b703b2874b042f2203690b132179c06"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981385d735a43178328181338a2eb23252", "guid": "bfdfe7dc352907fc980b868725387e983961d468ee99b2fde5eab44eebde2819"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982c691a4a0f389ae3e1b04851902919f9", "guid": "bfdfe7dc352907fc980b868725387e9860c0ca4f1b169c6c3e5bc580c4faf8ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830fd1326b3e52038a9b8adf13e3a833f", "guid": "bfdfe7dc352907fc980b868725387e983fa3f2c874deefe172df15753092bab8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e4290ed842c3f9214c83bf3398190712", "guid": "bfdfe7dc352907fc980b868725387e98ff0fb5b5a1403084c9a21cb3926f43b0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b85e75ad2cfc01ab6cef79a99ebf3ca7", "guid": "bfdfe7dc352907fc980b868725387e9886c6672797c15a229663a5abfb66bd5f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32bd462acb8b15d57b7dd24736e6ab8", "guid": "bfdfe7dc352907fc980b868725387e9890172df80ccf2d29d75d9ac4c876e098"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11a066232913e921d08d38e2a6e4102", "guid": "bfdfe7dc352907fc980b868725387e98b39593b7a2a87e5231f6739a8b4ad3ac"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985e8cdcfc096bda516d22ca7ee4f50cf9", "guid": "bfdfe7dc352907fc980b868725387e985359bf3f299bad8a26efcc1796d7c1cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cccea270e81150ba6d7998308e69c58b", "guid": "bfdfe7dc352907fc980b868725387e9804c95363a50213ac89a029dd5ede2090"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98de4a49e58ed6e3d5a06ef5fed8b2d980", "guid": "bfdfe7dc352907fc980b868725387e982185eaf2a444f33e9a3d24de73c85b20"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d51964dec4a70090f67ddaf349f58506", "guid": "bfdfe7dc352907fc980b868725387e9896cb6b0ffc5c1c3506ec4a58e89ea36f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c2fed47319be00d4dd2f9ab9efe891ae", "guid": "bfdfe7dc352907fc980b868725387e98ee148d666c8acd71907bd33deabb443d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98369033c851dbae2ff36354adaffaf446", "guid": "bfdfe7dc352907fc980b868725387e9892c0a4956427b93df66b450fdb25f99e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98225f4abfb5ac863666d54602d7d64c79", "guid": "bfdfe7dc352907fc980b868725387e982e9b3e8f8707edfca09066e99f85218d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b09fb27f002d736cdb1f6b6f8139f5d1", "guid": "bfdfe7dc352907fc980b868725387e98e729bde87da4c0e16b462b8f7eea558c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983954a5ef591c2822e78ef70e0296c47f", "guid": "bfdfe7dc352907fc980b868725387e98414d5cc7f2e3ec02fcdd41a11bf1f7cb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a182a1c780802991886b875883d21c4", "guid": "bfdfe7dc352907fc980b868725387e98f62846594feaf9ed9784f0223dd34c02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ffa38bd57907682967d4878d7540b296", "guid": "bfdfe7dc352907fc980b868725387e98e0d90704eda6dfa9adba808fc19d9be1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980b8adfd6ddf1eebe4e7a9cc76a41b346", "guid": "bfdfe7dc352907fc980b868725387e98ae9801ad561e319207bfd5132a06ac82"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987de87a994f8886df14018676776d60b5", "guid": "bfdfe7dc352907fc980b868725387e98c1e06560e6e9def7369054338ff7b0b3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c2191b8cd7535fce54f8efdc267d2a1", "guid": "bfdfe7dc352907fc980b868725387e98ef0b00f97868974c9f83ea2f1651747d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c6d2bd2efb6c5d9ad9cd55bfeb767f19", "guid": "bfdfe7dc352907fc980b868725387e984d0465fa6e1dd0469a4dddd2a07f7f67"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98aa4f9fa602b83742dbdca0ce6df4da67", "guid": "bfdfe7dc352907fc980b868725387e98994a718d5e20eed03b315c355decbf02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1e90c08d75f2034ab30aacb7c9ca925", "guid": "bfdfe7dc352907fc980b868725387e98cd4921a50a96eb35902045ca7e171903"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985af05b566225efac6e763adcf8ee7f52", "guid": "bfdfe7dc352907fc980b868725387e98dbacbc5974ef20ac917456bb112db6f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f3dc9dcbc2d97a9ac908ac34d83f5de2", "guid": "bfdfe7dc352907fc980b868725387e98d7662bb6322c653fa15b33f0fab1ec30"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98911f5d6248cbc853332fb55cc5746807", "guid": "bfdfe7dc352907fc980b868725387e982c3cfbfe2ba7c180e025b8639b99f150"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987c4a0f14c1f15f71885ca2c9c71a63f6", "guid": "bfdfe7dc352907fc980b868725387e9857231164d43248b29544f8b75b78771c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836e24208ffff0ee4edee1dd838039a8f", "guid": "bfdfe7dc352907fc980b868725387e984a94a0d630b89951254e0ac0e9afd657"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f7d3ebb3982107042ca467988218e9ab", "guid": "bfdfe7dc352907fc980b868725387e98b4aec1e1f286a6a108a7b6582e1602cc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986da19537df6a6adee9cec00e9c3411d1", "guid": "bfdfe7dc352907fc980b868725387e98c20ca3fdddf9820ad1f9be53cfe3fe33"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984966706fddb6cef2822e7ccbc786f9ce", "guid": "bfdfe7dc352907fc980b868725387e98da14d60bfbbdcae1f84a22751b6c81ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb8b084942f7b86438a6788c9730dd70", "guid": "bfdfe7dc352907fc980b868725387e98f0d37c827c7f865cff77874393ce3655"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cd89cd8df9e860cd69013bd85e92cac1", "guid": "bfdfe7dc352907fc980b868725387e983ce93608f20de2c0323b801632da322d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819e5cd5c7a5ca05bf915f490cfe5cc67", "guid": "bfdfe7dc352907fc980b868725387e98490b794d14bf218c1d455793d7362fe3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cab5da6ff10ae888e0d3308157eccc19", "guid": "bfdfe7dc352907fc980b868725387e9849c98362b09f26b288d7bd21fe522fcd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989543f923107f85639bdc25e6db5c8456", "guid": "bfdfe7dc352907fc980b868725387e982c258b7c0290759cf6c9d89a02337ea1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9831dd367ec730091cf8d2af32ebfde5f3", "guid": "bfdfe7dc352907fc980b868725387e984cfb6e1359f673bbf181025826359474"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800f0bd8703c59826fb300cc7441d1aaa", "guid": "bfdfe7dc352907fc980b868725387e98b4287aa33d38a0c7bdb734ba7c37c333"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98704315d6837a9d0ff06f4160ff035f8f", "guid": "bfdfe7dc352907fc980b868725387e98b6c97ab98c8a321cb51863512d1c136b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98894cae77cef9d856c9f84369e4a75138", "guid": "bfdfe7dc352907fc980b868725387e984af3061c1e4dfff7190d6db3f1240acd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884b3492e5312cb9a44d3e998ccdae96f", "guid": "bfdfe7dc352907fc980b868725387e9870263c41a7082c8fe5ac87a0e2553779"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986a83687c266d57a091be68649ae29b82", "guid": "bfdfe7dc352907fc980b868725387e98f63c311f3c559fed53743f0b95813043"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dea930f29d68c0cd002735b6beaff00d", "guid": "bfdfe7dc352907fc980b868725387e9821aa96f9afbc3dca1fc0b93342f1514b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9801bde62761f702dd4a9dc4f825d60bb6", "guid": "bfdfe7dc352907fc980b868725387e980c844b1611d5b8ac3d3fdf77ad7005e1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829d609822e96fac8901d2234885478a7", "guid": "bfdfe7dc352907fc980b868725387e989cadac3b221deede98a0c1bdbdc89b4f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980cba43d7aa2fd437911ca511a1d3694d", "guid": "bfdfe7dc352907fc980b868725387e98a5e1b4d6fbe6963838096953208d78cd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b01c0551a88b258613395cd4c1e396bd", "guid": "bfdfe7dc352907fc980b868725387e986f76ec32e63ca854831a887b24b785d2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985c10ce6d35b81d15f7c5e3864ba01dfa", "guid": "bfdfe7dc352907fc980b868725387e98daf0397dd37428d109d8295a99b08e0c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e17bee98a6113c6a0108d8276f3b3f31", "guid": "bfdfe7dc352907fc980b868725387e98bfce6cc01cf59da6f3439740a74c24fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98686c3b3af5d64437f52829f39a29a36a", "guid": "bfdfe7dc352907fc980b868725387e9898f3540c1f4eae2e13c71be1416f6d7e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ba146af7485af65772a275a225e199eb", "guid": "bfdfe7dc352907fc980b868725387e98e1be0b2ad0f4269427af2950ba55842a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982119fc0d6aa0621ab3e420e4550cb39f", "guid": "bfdfe7dc352907fc980b868725387e98fb643b42807d21d39463184be905f219"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987aab9a10fa52a8000b28c1fe6cb1f398", "guid": "bfdfe7dc352907fc980b868725387e989ad67a3786a55d597a313155a24b2453"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98db7c52f65a425a0791bc22a400af226b", "guid": "bfdfe7dc352907fc980b868725387e989547d1cbbcecef1878c3cfbae60bf16b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c503b0f1b7c29e3ac9bcc510c2e913a9", "guid": "bfdfe7dc352907fc980b868725387e985c69ef0b75b2cbb40d52f4a266bdfdc6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9877eb04f787f024aba4c287d4c0c04608", "guid": "bfdfe7dc352907fc980b868725387e982cac6bb8e50eb15099c93ff40e0d2d83"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987fadb923a25bb5a65a959db117ce7315", "guid": "bfdfe7dc352907fc980b868725387e9893f454095f84e476ea5c65c354601bef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e79974fbcc26c9f94887773be36be5db", "guid": "bfdfe7dc352907fc980b868725387e98a30266a254208bc16e90e1b622bcff76"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98751a694e84330575f8fd1631858e89f3", "guid": "bfdfe7dc352907fc980b868725387e98157c13b97e83d5613b46429694f396e2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cabe8e63bcae4813b0056b43a237ecfc", "guid": "bfdfe7dc352907fc980b868725387e98be7adab082e824971ea90de1eadf28a1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a15525c1f7b52aa40a535da2f65099d5", "guid": "bfdfe7dc352907fc980b868725387e98ece5fae7c44422bbcf31ea7267925826"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989851a724e221663a9a66f0acae1f59a5", "guid": "bfdfe7dc352907fc980b868725387e987e091eecd2c3b710a9f9efad7c374d9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9875881687a5d376fb5f1c7137793e9f48", "guid": "bfdfe7dc352907fc980b868725387e9895a9b6ab74a8de9cd58f47ef092d330a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987ae060ea9a5245aa04949f39fc2777aa", "guid": "bfdfe7dc352907fc980b868725387e981b400f607e6214eeed10b2a4a64d8c7d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e11dd40365c58606bafa8315235cc3bc", "guid": "bfdfe7dc352907fc980b868725387e9851f0cac7f2518e8fb20f60c1dc1c8daf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98193c9973501ccad6470294bceb16356a", "guid": "bfdfe7dc352907fc980b868725387e981b4b00e8f0008c2da68b582d3689b591"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cd8c12aade62a0d44669239dfb33627", "guid": "bfdfe7dc352907fc980b868725387e980b1e45d37fd69ecb7de37cee6bd93f24"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f32204c02a31780701d3954686316aa0", "guid": "bfdfe7dc352907fc980b868725387e9869f62831cea75bb8491ddab438e0fb2a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987714489307e9abde5af86893e1a10239", "guid": "bfdfe7dc352907fc980b868725387e981ffffc4338e9e53c0521717f354c5c2e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a4ec24ac89cb7b2b22cc621db823d82", "guid": "bfdfe7dc352907fc980b868725387e98f0daaa81b9993ae92fc91256f599838f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cf997a9b9d615d123295f7161881dc2b", "guid": "bfdfe7dc352907fc980b868725387e98336bb6142c1b37e551099e3c199d6de4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98343ba17a909fa71bebb57080a969e682", "guid": "bfdfe7dc352907fc980b868725387e98c6f19ab4b99131bac8ac7de8eaf609f2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d8bc9c3e4e0595c4174ba18dc8aaba80", "guid": "bfdfe7dc352907fc980b868725387e98b28226ffafaffdc53a194ec269b7abd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9827b032f52c3ebdbe98433a51a04c8e18", "guid": "bfdfe7dc352907fc980b868725387e9831136656d16d5828f290314bc05ec260"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ad3177dd36b116d99b26934a254c776", "guid": "bfdfe7dc352907fc980b868725387e984f48b63e00f57258c9a2b9bd1593d0ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988815c24b80659bdd27854e30fc10a38a", "guid": "bfdfe7dc352907fc980b868725387e986fcecda2d822121aaa7f05926c3f435f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9499d07230e2800332b52820b739610", "guid": "bfdfe7dc352907fc980b868725387e98c2f5c511520b91ec9e93c8413ace466b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98029a0ef765d1a06f659a631411bfbfc0", "guid": "bfdfe7dc352907fc980b868725387e980d29b7a16ae29bfcc7b5c23c03c9db9d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e2b8c8c85d2f5a5d8c803440e4288242", "guid": "bfdfe7dc352907fc980b868725387e985309d3fe3137aeb276041045d468482a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbb09125144e6cc09562dc21bd01ae5c", "guid": "bfdfe7dc352907fc980b868725387e9864a11fc129e738d8fde5bb25080b201f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad4caefef1773b9cede69c21bde09d46", "guid": "bfdfe7dc352907fc980b868725387e9862ac89b253ce2d4cbe349fb61dac3867"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9891e20f7057617e5bafc7598c4e456572", "guid": "bfdfe7dc352907fc980b868725387e98bd2f40c4a5397c8ee6d1bf578df7024a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802dc15e00ba6fc5260719c9933d16bef", "guid": "bfdfe7dc352907fc980b868725387e985b6d1fb7818adcfdb3b9e7e5312a8c70"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e093cebcd6f548e5eda9b35ac4133102", "guid": "bfdfe7dc352907fc980b868725387e988b84dce5c233ed442941d155e24cfbc8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982738ec415d73c51bf3c8c52eeff2b116", "guid": "bfdfe7dc352907fc980b868725387e989f60c2fae924734c06a5ccfea29e2adc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f50625f9d3bc1c55dcf94932816e972f", "guid": "bfdfe7dc352907fc980b868725387e989442d70f2ce95ad086821a50013872c0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9829375158f7bbe4621554abdb373beeaf", "guid": "bfdfe7dc352907fc980b868725387e9857b965567ad96909204ec8805394971b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981706abfa4046b7ed220484ec0aba550a", "guid": "bfdfe7dc352907fc980b868725387e98ad39f88b848c95bc9006325b9f045e92"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9802ffb4066dea9246e592e259071a9cd8", "guid": "bfdfe7dc352907fc980b868725387e982927a3153f34497426c2e3203d014715"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868b16d4f140f994ccf9e31e6a0174dcd", "guid": "bfdfe7dc352907fc980b868725387e98a15beee3f9f0a62189359c8c3d8d5b6f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f5a64c3b11c481f71fe7cfccbcaa6dcd", "guid": "bfdfe7dc352907fc980b868725387e982c511e064e19d8873fb76297ee040f96"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983787e8ee42c99ad1a2f2aa98070c6ece", "guid": "bfdfe7dc352907fc980b868725387e985533f504b9152a1246ecb43661d51286"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cc21b705a1d4cf3423664f61f2c63e95", "guid": "bfdfe7dc352907fc980b868725387e98659a3516e63a63979ec7565dce78ff9b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca04b657f59dc9d497b055310a689e2e", "guid": "bfdfe7dc352907fc980b868725387e98167bb65feeeca4cdb36e5fb1862c86ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98797b487ba7cc1d3d8ce970b8cf672523", "guid": "bfdfe7dc352907fc980b868725387e98b3355b1a08f32416909ab496cc76fc49"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab7689e3920217e1901e55cd7515285f", "guid": "bfdfe7dc352907fc980b868725387e984fcb5b5213be7ca62ca18a46a1e2d3b1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9857523ac1e7bd64da85d9900b3daf2b59", "guid": "bfdfe7dc352907fc980b868725387e98361e3f1802e72154ca21970ab7440196"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98378b602ff3eef9ec78d3d0c9be2678d3", "guid": "bfdfe7dc352907fc980b868725387e985bf31d150da8afcf6ac8f5c09858c62d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98398b734459e74724db04dda2cbf5ff00", "guid": "bfdfe7dc352907fc980b868725387e9862d9107c5d59fe46fafa6c465e869818"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fdc43b3c4862e8cb378f7aa0f706f1f4", "guid": "bfdfe7dc352907fc980b868725387e9864bd02608e0bdda13d5c97c0b51d21ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9870a1b55cc8fb0c32fce7b5dfe5bbc33b", "guid": "bfdfe7dc352907fc980b868725387e981b7ec341560cc2f681544456b22c0950"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884d41d7c5db1941296bc19d83d648293", "guid": "bfdfe7dc352907fc980b868725387e984d6d6c8734b273c298b87fc57ef2fd88"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f258e388adc76a80e5a810425cec50e7", "guid": "bfdfe7dc352907fc980b868725387e985e00565da547175c39bcc3be18fd6bb0"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9898e5a27a86b58f7f1433d14d64ccf37b", "guid": "bfdfe7dc352907fc980b868725387e98e360fbc4933760980b6d74a982e4e783"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988873ea185ce2074e7ae64a636b0de898", "guid": "bfdfe7dc352907fc980b868725387e987ef4c4e1b549ec1a0269d447c9a878ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a55881f383c2e60b4e9052d77a06ab0d", "guid": "bfdfe7dc352907fc980b868725387e9811ffefed4c70e004d4113f148ce8ce6d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a6b4e637084d32b738d4e4df7814735f", "guid": "bfdfe7dc352907fc980b868725387e9826743a44b3f711b5e3123ec0e12be373"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eec3c5a4ec3f7c3cff9e4294c81ef594", "guid": "bfdfe7dc352907fc980b868725387e988f1b9dfa6aa1eb8681b3e08799881235"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a89684b3fbdabd2cc382b423377b0f58", "guid": "bfdfe7dc352907fc980b868725387e986aa688e55cb1341b35a4ac34a5ceb134"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988e654cdf1c3a4788ff92a0af122c71ca", "guid": "bfdfe7dc352907fc980b868725387e98fc10d13ea1eadc56d551221e4bd0deb4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98daa8e77d54d00adf63469cfc49949fa8", "guid": "bfdfe7dc352907fc980b868725387e9847c182cfc4dc8eec021b4e903526064f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9836d87a22579593a0f241652cbf0ced4b", "guid": "bfdfe7dc352907fc980b868725387e98fadaab6def96efb1ef21ebca4a0c2858"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988babe22f25b62563df248e41eeb592cd", "guid": "bfdfe7dc352907fc980b868725387e98f47fc183c0e5f1bba6510796eac8a8fe"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989bc93e9a7c8fa74948c875f4a015d933", "guid": "bfdfe7dc352907fc980b868725387e985291dfb94b22528ef719e26860ff8098"}], "guid": "bfdfe7dc352907fc980b868725387e98cac5758aaab041b306b3055c248885f4", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e98027d34dd1e1770c79dd54f5404987fd6"}], "guid": "bfdfe7dc352907fc980b868725387e98d8f70b5ff677ed02b77232961e037957", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b7d49ad1fe66522e6eab9248dd2331d6", "targetReference": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a73c1ee5fe469e676f7657cbdc7d21ce", "guid": "bfdfe7dc352907fc980b868725387e98239db474d923c0b16a55c73b0260af23"}], "guid": "bfdfe7dc352907fc980b868725387e98ba929dd1b11c60f9a4f8fd06e4eaa3e7", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e985f0ec3a68eeed5241cb87afb05bcc380", "name": "OrderedSet"}, {"guid": "bfdfe7dc352907fc980b868725387e98feffba4bc77d9f3d84a98c192cefdc8b", "name": "flutter_inappwebview_ios-flutter_inappwebview_ios_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98a562549a031aeda8bf3440b79b3420bc", "name": "flutter_inappwebview_ios", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e9810acd6d3a97e7ef91b90dda5618dc5c0", "name": "flutter_inappwebview_ios.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}