import 'package:chilat2_mall_app/pages/home/<USER>';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:flutter/material.dart';

class ChooseUs extends StatelessWidget {
  const ChooseUs({super.key});

  List<ChooseItem> chooseItems(BuildContext context) => [
        ChooseItem(
          title: I18n.of(context)!.translate("cm_guestHome.brandTitle"),
          desc: I18n.of(context)!.translate("cm_guestHome.brandDesc"),
        ),
        ChooseItem(
          title: I18n.of(context)!.translate("cm_guestHome.teamTitle"),
          desc: I18n.of(context)!.translate("cm_guestHome.teamDesc"),
        ),
        ChooseItem(
          title: I18n.of(context)!.translate("cm_guestHome.resourceTitle"),
          desc: I18n.of(context)!.translate("cm_guestHome.resourceDesc"),
        ),
      ];

  @override
  Widget build(BuildContext context) {
    return Container(
      color: Colors.white,
      margin: EdgeInsets.only(top: 2),
      padding: EdgeInsets.symmetric(vertical: 12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Center(
            child: Container(
              padding: EdgeInsets.only(top: 6),
              child: Text(
                I18n.of(context)!.translate("cm_guestHome.fullTitle"),
                style: TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 16,
                ),
              ),
            ),
          ),
          Container(
            padding: EdgeInsets.only(left: 6),
            child: ListView.builder(
                shrinkWrap: true,
                itemCount: chooseItems(context).length,
                itemBuilder: (context, index) {
                  ChooseItem item = chooseItems(context)[index];
                  return Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                          padding: EdgeInsets.symmetric(horizontal: 6),
                          width: MediaQuery.of(context).size.width * 0.92,
                          child: Text.rich(TextSpan(
                            children: [
                              TextSpan(
                                text: '0${(index + 1).toString()} ',
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.red.shade600,
                                ),
                              ),
                              TextSpan(
                                text: item.title,
                                style: TextStyle(
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                  color: Colors.grey.shade800,
                                ),
                              ),
                            ],
                          ))),
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                        width: MediaQuery.of(context).size.width * 0.98,
                        child: Text(
                          item.desc,
                          style: TextStyle(
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: Colors.grey.shade600,
                          ),
                        ),
                      )
                    ],
                  );
                }),
          )
        ],
      ),
    );
  }
}
