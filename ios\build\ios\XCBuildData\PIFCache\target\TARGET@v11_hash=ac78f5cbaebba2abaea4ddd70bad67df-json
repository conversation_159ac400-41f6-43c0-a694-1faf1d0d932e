{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98bee77ae14d2c67fdf55814c5bea1bbf9", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ee49f65d26d8a0c930cc03d16e0ffcc8", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1cfa07a2dcb2283ec4767925eff924b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e987d3fcd01a34fff9e18dec0764bcc371e", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b1cfa07a2dcb2283ec4767925eff924b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/sqflite_darwin/sqflite_darwin-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/sqflite_darwin/sqflite_darwin.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "sqflite_darwin", "PRODUCT_NAME": "sqflite_darwin", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984def481d54810d0de6d3335b228b2a5d", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e985f123aa80acb31ec8cdc79c20c56b2e3", "guid": "bfdfe7dc352907fc980b868725387e98f74bfe561cdc142d140be02f934f1dd5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dbbadd9505c9507e2200bb1b611df355", "guid": "bfdfe7dc352907fc980b868725387e98581f54a09e9b765d82abaf8d27da91a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98bd6f9e876f2c7b2be4055af8250fc3d2", "guid": "bfdfe7dc352907fc980b868725387e986a57b1cdddbb1e6822355c5b02e7492c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df0ace16b9d02bd6167fde65ceea9557", "guid": "bfdfe7dc352907fc980b868725387e98b8cbe373d9ed52a995f06e9ace289d31"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858a95fdb885a3c134eb8b08c1d246267", "guid": "bfdfe7dc352907fc980b868725387e985a6b44cb78413f045202892c1a47d0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e9df0fc6f0aee9399521dd8fa942bb48", "guid": "bfdfe7dc352907fc980b868725387e987eca29e02bb9234f55aed7ac9b8febd7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9852809701e7775684735b3c6a1f8286dc", "guid": "bfdfe7dc352907fc980b868725387e98eeda7c7e68e77f20f4d5bc361ce041f1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b4989995ae751319e6863ba01fcc54c1", "guid": "bfdfe7dc352907fc980b868725387e986d185e00411cdd996d60a1d4b6409532"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b1cff7b0d931b36f6b1beb874587a75a", "guid": "bfdfe7dc352907fc980b868725387e98884052ecfc35b8795088fe89e29ea175"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98143ed2a80084476f93458be5088091b1", "guid": "bfdfe7dc352907fc980b868725387e9837e8d9f55ae87130f5f0686b0e90184b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c9f0e9a1a22706738aea164659b92c1c", "guid": "bfdfe7dc352907fc980b868725387e98fb8847974b537a1f1ca77be5bf27aebf", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cfd45e8e2fc1c68f3119ab15c98a7f4e", "guid": "bfdfe7dc352907fc980b868725387e98acdff5ccaa494373da49aa3852209ad5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98edf7a37b3bc3d2924b02cbfb68168ebd", "guid": "bfdfe7dc352907fc980b868725387e985e19dcb46e96ffcd43d81f6db74b9f55"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98874f157837efd7276d833cdcc202be51", "guid": "bfdfe7dc352907fc980b868725387e98aaf90d473486ff2b7ce32f77ecb1ffc6", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98cc465b98567e5be1dff8b7284a07e4e3", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9836521190708c92289f6efb23e43dd79b", "guid": "bfdfe7dc352907fc980b868725387e985adaa6c4e40f33315ac0cec984200988"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9849e2bdbd001edd5578b42bad05eb0c0b", "guid": "bfdfe7dc352907fc980b868725387e9814947539d3f7a0f73f540ae23ebf8264"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d2311b9561e9a981753ccd94f6e95f38", "guid": "bfdfe7dc352907fc980b868725387e981a340bbc7ceca4c28306a5246190a5ef"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4fc6378befc1cb9192eb137da7943c6", "guid": "bfdfe7dc352907fc980b868725387e9869baa04d3a2339610ba424dc331fb8cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d263366efb283b8be730fd02f4276bdc", "guid": "bfdfe7dc352907fc980b868725387e985eff5ce521498aebd60e80cf461604f3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f0a24e28b1f98082b7a0cef138787a", "guid": "bfdfe7dc352907fc980b868725387e981fb4690450d28ad2c3c425d3ebf83b5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980150f69af57b52f2e79c10539b936bdc", "guid": "bfdfe7dc352907fc980b868725387e9810988f844d883bedd17740ddb0ec76f9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9823816c84e127922d402a13f1b0cf237a", "guid": "bfdfe7dc352907fc980b868725387e98e9c9ca8908dcd4351ddd34b99785248a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c561793e50c44037675dd80f5876e292", "guid": "bfdfe7dc352907fc980b868725387e982df0ee29631feacbc5e6cfcf72ebfbfe"}], "guid": "bfdfe7dc352907fc980b868725387e9837c2f0a37c50e959478519168227e455", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e982f461d51c284a55b8f869fc9092ae5dc"}], "guid": "bfdfe7dc352907fc980b868725387e98dd47f73652ff7b522b7942f6a87afd23", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98d17544c34b81de618417de5f9c91b4ec", "targetReference": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8"}], "guid": "bfdfe7dc352907fc980b868725387e98e60a652c76bfee084293e97b00176921", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9883134bb5f399cb37a1eb075d4fea30d8", "name": "sqflite_darwin-sqflite_darwin_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e981304d3d2169071b3ca365b19f5340b7c", "name": "sqflite_darwin", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98dbbec3eebed26c79cc653713be723aba", "name": "sqflite_darwin.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}