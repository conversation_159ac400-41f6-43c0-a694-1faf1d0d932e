import 'package:flutter/material.dart';
import 'package:get/get.dart';
import './components/coupon_select.dart';
import './components/order_pay_agree.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/services/order.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/components/order_cancel_drawer.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class OrderDetailController extends GetxController
    with StateMixin<Map<String, dynamic>> {
  // 页面数据
  final pageData = {
    'orderList': [], // 订单列表
    'statusDesc': '', // 订单状态描述
    'addressInfo': {}, // 地址信息
    'transportAmountList': [], // 运输线路与预估费用列表
    'transportId': null, // 选中的运输线路id
    'expandedTransport': [], // 默认展开的运输线路列表
    'currentTransport': {}, // 当前选中的运输线路 需根据这个线路计算 装箱信息的箱运费、到手单价以及实际支付金额
    'transportAmountType': 0, // 运输金额类型
    'boxList': [], // 装箱列表
    'orderNo': '', // 订单号
    'orderTime': null, // 下单时间
    'productAmount': {'amount': 0, 'feeList': []}, // 产品成本
    'merChantRemark': '', // 商家备注
    'orderRemark': '', // 客户备注
    'actualPaymentAmount': 0.00, // 实际支付金额(订单总价-优惠金额)
    'isLoading': false, // 是否加载中
    'showLoadTrans': false, // 是否展示估计装运的介绍
    'showComDetails': false, // 是否展示费用明细
    'feeDetailsList': [], // 展示的费用明细
    'totalAmount': 0.00, // sku总价值
    'shippingFee': 0.00, // 运费
    'discountAmount': 0.00, // 折扣金额
    'finalAmount': 0.00, // 最终金额
    'acceptTerms': false, // 是否确认协议
    'paymentAmount': 0.00, // 订单总价 前端根据报价模式 支付模式计算
    'discountedAmount': 0.00, // 优惠金额
    'totalCount': 0, // sku总数量
    'mallOrderStatus': null, // 订单状态
    'quotationMode': null, // 报价模式
    'payType': '', // 支付方式 (线上支付或线下支付)
    'payMode': '', // 支付模式 (一次性支付或分开支付)
    'showPayInfoError': false, // 是否展示付款信息错误提示
    'productCouponError': false, // 产品券信息错误
    'commissionCouponError': false, // 佣金券信息错误
    'allCouponError': false, // 所有优惠券信息错误
    'showCouponChoose': false, // 是否展示优惠券选择弹窗
    'chooseCouponType': '', // 当前选择的优惠券类型
    'chooseCouponList': [], // 选择的优惠券列表
    'chooseCouponAmount': 0.00, // 选择的优惠券优惠金额总计
    'selectedCouponIds': [], // 选中的优惠券ID列表
    'chooseCouponLoading': false, // 优惠券选择加载状态
    'hasReadFinished': false, // 是否阅读完支付协议
    'showLogDetails': false, // 是否展示物流信息
    'paymentAmountMessage': null, // 订单总价提示
    'productAddComissSumAmount': null, // 付款成功后的产品券+佣金券优惠总金额
    'paidAmount': null, // 实付款
    'couponInfo': {
      'productCouponList': [], // 产品优惠券列表
      'commissionCouponList': [], // 佣金优惠券列表
      'productCouponAmount': 0.00, // 产品优惠的金额
      'commissionCouponAmount': 0.00, // 佣金优惠的金额
      'productCouponIds': [], // 产品优惠券id列表
      'commissionCouponIds': [], // 佣金优惠券id列表
    },
  }.obs;

  // 订单状态
  final isPayFee = false.obs; // 是否需要支付费用（包括支付所有费用、产品成本和国际费用）
  final isPayInterFee = false.obs; // 是否需要支付国际费用
  final isPayAllFee = false.obs; // 是否需要支付所有费用
  final isPayProduct = false.obs; // 是否需要支付产品成本

  /// 是否隐藏国际费用明细（分开支付时，仅支付产品成本后，国际费用隐藏）
  bool get hideInterFeeDisplay {
    final payMode = pageData['payMode'];
    final quotationMode = pageData['quotationMode'];
    final mallOrderStatus = pageData['mallOrderStatus'];
    return payMode == 'PAY_MODE_PART' &&
        (quotationMode == 'QUOTATION_MODE_DDP' ||
            quotationMode == 'QUOTATION_MODE_CIF') &&
        (mallOrderStatus == 'MALL_PURCHASING' ||
            mallOrderStatus == 'MALL_WAIT_CAL_INTER_FEE' ||
            mallOrderStatus == 'MALL_WAIT_PAY_INTER_FEE');
  }

  final isOrderCanceled = false.obs; // 订单是否已取消

  // 线下支付计算属性
  bool get isOfflinePayment => pageData['payType'] == "ONLINE_PAY_OFFLINE";

  // 协议相关状态
  final _hasReadFinished = false.obs; // 是否已阅读完成支付协议
  final _acceptTerms = false.obs; // 是否接受协议条款

  bool get hasReadFinished => _hasReadFinished.value;
  bool get acceptTerms => _acceptTerms.value;

  // 文本编辑控制器
  final remarkController = TextEditingController(); // 备注输入框控制器

  String getTranslation(String key) {
    final context = Get.context;
    if (context == null) return '';
    return I18n.of(context)?.translate(key) ?? '';
  }

  @override
  void onInit() {
    super.onInit();
    pageData['orderNo'] = Get.arguments['orderNo'];
    getOrderDetail();
  }

  @override
  void onClose() {
    remarkController.dispose();
    super.onClose();
  }

  // 获取订单详情
  Future<void> getOrderDetail() async {
    change(null, status: RxStatus.loading());
    try {
      final res = await OrderService.useGetOrderDetail({
        'orderNo': pageData['orderNo'],
      });

      if (res['result']['code'] == 200) {
        // 更新页面数据
        pageData.assignAll(res['data']);
        // 设置订单状态
        _setOrderStatus(res['data']['mallOrderStatus']);
        change(res['data'], status: RxStatus.success());

        // 对运输方式列表按金额排序（从低到高）
        final transportList = List<Map<String, dynamic>>.from(
            (pageData['transportAmountList'] as List<dynamic>?) ?? []);
        transportList
            .sort((a, b) => (a['amount'] as num).compareTo(b['amount'] as num));
        pageData['transportAmountList'] = transportList;

        // 展开所有运输方式详情
        final expandedTransport = <String>[];
        for (final transport in transportList) {
          expandedTransport.add(transport['transportId']);
        }
        pageData['expandedTransport'] = expandedTransport;

        // 待支付订单请求可用优惠券列表
        if ((isPayAllFee.value || isPayProduct.value) && !isOfflinePayment) {
          await getCouponList('init');
        }

        // 更新运输方式和支付金额
        onUpdateTrans();
        initPaymentAmount();
      } else {
        // 其他错误，显示提示信息
        Get.snackbar(
          '提示',
          res['result']['message'] ?? '获取订单详情失败',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      Get.snackbar('提示', '获取订单详情失败', snackPosition: SnackPosition.TOP);
    }
  }

  // 设置订单状态
  void _setOrderStatus(String? status) {
    if (status == null) return;

    // 重置所有状态
    isPayFee.value = false;
    isPayInterFee.value = false;
    isPayAllFee.value = false;
    isPayProduct.value = false;
    isOrderCanceled.value = false;

    // 根据订单状态设置对应的标志
    switch (status) {
      case 'MALL_WAIT_PAY_ALL_FEE': // 待支付所有费用
        isPayFee.value = true;
        isPayAllFee.value = true;
        break;
      case 'MALL_WAIT_PAY_PRODUCT': // 待支付产品成本
        isPayFee.value = true;
        isPayProduct.value = true;
        break;
      case 'MALL_WAIT_PAY_INTER_FEE': // 待支付国际费用
        isPayFee.value = true;
        isPayInterFee.value = true;
        break;
      case 'MALL_CANCELED': // 订单已取消
        isOrderCanceled.value = true;
        break;
    }
  }

  // 根据运输线路计算箱运费、到手单价、订单总价、实际支付金额
  void onUpdateTrans([String? transportId]) {
    final transportList = pageData['transportAmountList'] as List;
    if (transportList.isEmpty) return;

    Map<String, dynamic> matchTrans;
    if (transportId != null) {
      // 用户选择了新的运输方式
      matchTrans = transportList.firstWhere(
        (item) => item['transportId'] == transportId,
        orElse: () => <String, dynamic>{},
      );
      pageData['transportId'] = transportId;
    } else {
      // 使用默认第一个运输方式（最便宜的）
      matchTrans = transportList[0];
      pageData['transportId'] = matchTrans['transportId'];
    }

    if (matchTrans.isNotEmpty) {
      pageData['currentTransport'] = matchTrans;
      updateTransportCalculations(matchTrans);
    }
  }

  // 更新每个箱子的箱运费和其他相关计算
  void updateTransportCalculations(Map<String, dynamic> matchTrans) {
    if (matchTrans.isNotEmpty) {
      // 更新每个箱子的箱运费
      if (pageData['transportAmountType'] == 1) {
        final boxList = pageData['boxList'] as List<dynamic>;
        for (var box in boxList) {
          final boxTransportFeeList =
              box['boxTransportFeeList'] as List<dynamic>;
          final matchedFee = boxTransportFeeList.firstWhere(
            (fee) => fee['transportId'] == matchTrans['transportId'],
            orElse: () => <String, dynamic>{},
          );

          if (matchedFee.isNotEmpty) {
            box['boxFee'] = matchedFee['amount'];
          }

          // 更新每个sku的到手单价(销售单价+单个商品分摊的运费，单个商品分摊运费=单箱运费/单箱装箱数，除不尽四舍五入，保留两位小数)
          final skuList = box['skuList'] as List<dynamic>;
          for (var sku in skuList) {
            final boxFeePerUnit = (box['boxFee'] * 100) / box['skuCount'] / 100;
            sku['receivedUnitPrice'] =
                (boxFeePerUnit + num.parse(sku['unitPrice'].toString()))
                    .toStringAsFixed(2);
          }
        }
      }

      // 更新订单总价(支付模式为一次性支付时，产品成本 + 国际费用) 、实际支付金额
      if (pageData['payMode'] == 'PAY_MODE_ALL') {
        final productAmount = (pageData['productAmount']
                as Map<String, dynamic>?)?['amount'] as num? ??
            0;
        final transportAmount = matchTrans['amount'] as num? ?? 0;

        pageData['paymentAmount'] = mathRound(productAmount + transportAmount);
        pageData['actualPaymentAmount'] = mathRound(
            ((pageData['paymentAmount'] as num?) ?? 0) +
                ((pageData['discountedAmount'] as num?) ?? 0));
      }
    }
  }

  // 更新运输方式
  void updateTransport(String transportId) {
    onUpdateTrans(transportId);
    // 更新支付金额
    initPaymentAmount();
  }

  // 初始化支付金额
  void initPaymentAmount() {
    final payMode = pageData['payMode'] as String?;
    final quotationMode = pageData['quotationMode'] as String?;
    final productAmount = (pageData['productAmount']
            as Map<String, dynamic>?)?['amount'] as num? ??
        0;
    final currentTransport =
        pageData['currentTransport'] as Map<String, dynamic>?;
    final transportAmount = currentTransport?['amount'] as num? ?? 0;

    // 报价模式为 DDP 或 CIF 时
    if (quotationMode == 'QUOTATION_MODE_DDP' ||
        quotationMode == 'QUOTATION_MODE_CIF') {
      // 支付模式为一次性支付时，产品成本 + 国际费用
      if (payMode == 'PAY_MODE_ALL') {
        pageData['paymentAmount'] = mathRound(productAmount + transportAmount);
        pageData['paymentAmountMessage'] =
            '${getTranslation('cm_order.productCost')} + ${getTranslation('cm_order.interFees')}';
      } else {
        // 支付模式为分开支付时，根据订单状态决定支付金额
        if (isPayProduct.value) {
          pageData['paymentAmount'] = productAmount;
          pageData['paymentAmountMessage'] =
              getTranslation('cm_order.productCost');
        } else if (isPayInterFee.value) {
          pageData['paymentAmount'] = transportAmount;
          pageData['paymentAmountMessage'] =
              getTranslation('cm_order.interFees');
        }
      }
    } else {
      // 其他报价模式时，支付金额为产品成本
      pageData['paymentAmount'] = productAmount;
      pageData['paymentAmountMessage'] = getTranslation('cm_order.productCost');
    }

    // 更新实际支付金额
    pageData['actualPaymentAmount'] = mathRound(
        (pageData['paymentAmount'] as num? ?? 0) +
            (pageData['discountedAmount'] as num? ?? 0));

    update();
  }

  // 显示运输说明
  void showTransportInfoDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (ctx) {
        return Dialog(
          child: Padding(
            padding: EdgeInsets.only(
              left: 14.sp,
              right: 14.sp,
              top: 16.sp,
              bottom: 16.sp,
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  I18n.of(context)?.translate('cm_order.WETrans') ?? '',
                  style: TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 18.sp,
                  ),
                ),
                SizedBox(height: 16.sp),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(I18n.of(context)
                            ?.translate('cm_order.transQuotation') ??
                        ''),
                    SizedBox(height: 8.sp),
                    Text(I18n.of(context)?.translate('cm_order.betterTrans') ??
                        ''),
                    SizedBox(height: 8.sp),
                    Text(I18n.of(context)?.translate('cm_order.transService') ??
                        ''),
                  ],
                ),
                SizedBox(height: 24.sp),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8.r),
                      ),
                      padding: EdgeInsets.symmetric(
                          vertical: 8.sp, horizontal: 8.sp),
                    ),
                    onPressed: () {
                      Navigator.of(ctx).pop();
                    },
                    child: Text(
                      I18n.of(context)?.translate('cm_order.transKnown') ?? '',
                      style: TextStyle(fontSize: 16.sp),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 显示费用明细
  void showFeeDetails(List<dynamic>? detailList) {
    if (detailList == null || detailList.isEmpty) {
      Get.snackbar('提示', '暂无费用明细');
      return;
    }

    Get.dialog(
      Dialog(
        child: Container(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                '费用明细',
                style: TextStyle(fontSize: 16.sp, fontWeight: FontWeight.w500),
              ),
              SizedBox(height: 16),
              ...detailList
                  .map((detail) => Padding(
                        padding: EdgeInsets.only(bottom: 8),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Text(detail['name']?.toString() ?? ''),
                            Text('¥${detail['amount']?.toString() ?? '0'}'),
                          ],
                        ),
                      ))
                  .toList(),
            ],
          ),
        ),
      ),
    );
  }

  // 关闭费用明细
  void closeFeeDetails() {
    pageData['showComDetails'] = false;
  }

  // 更新订单备注
  void updateOrderRemark(String remark) {
    pageData['orderRemark'] = remark;
  }

  // 更新协议接受状态
  void updateAcceptTerms(bool value) {
    if (_hasReadFinished.value) {
      _acceptTerms.value = value;
      update();
      if (value) {
        // TODO: 埋点
        // MyStat.addPageEvent('payment_agree_terms', '同意支付协议');
      } else {
        // TODO: 埋点
        // MyStat.addPageEvent('payment_unselect_terms', '取消选择支付协议');
      }
      return;
    }

    // TODO: 埋点
    // MyStat.addPageEvent('payment_open_terms', '打开支付协议对话框(未阅读完协议，点击勾选框，弹出协议对话框)');

    onOpenAgreeModal(Get.context!);
  }

  // 打开协议弹窗
  void onOpenAgreeModal(BuildContext context) {
    OrderPayAgree.show(
      context,
      () {
        _hasReadFinished.value = true;
        update();
      },
      (value) {
        _acceptTerms.value = value;
        update();
      },
    );
  }

  // 取消订单
  Future<void> cancelOrder() async {
    try {
      final orderNo = pageData['orderNo'];
      final res = await OrderService.useCancelOrder({'orderNo': orderNo});

      if (res['result']['code'] == 200) {
        Get.back(); // 关闭取消订单弹窗
        await getOrderDetail(); // 刷新订单详情
      } else {
        Get.snackbar(
          'Error',
          res['result']['message'] ?? '取消订单失败',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        '取消订单失败',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  // 关闭支付信息错误对话框并刷新订单详情
  void onClosePayError() {
    pageData['orderRemark'] = '';
    remarkController.text = '';
    getOrderDetail(); // 关闭弹窗后刷新订单内容
    update();
  }

  // 处理订单支付
  Future<void> onOrderPay() async {
    // 未确认协议，弹出协议弹框
    if (!_acceptTerms.value) {
      onOpenAgreeModal(Get.context!);
      return;
    }

    if (pageData['couponInfo'] == null) {
      pageData['couponInfo'] = <String, dynamic>{
        'productCouponIds': [],
        'commissionCouponIds': [],
        'productCouponAmount': 0,
        'commissionCouponAmount': 0,
      };
    }

    try {
      final res = await OrderService.useOpenCashDesk({
        'orderNo': pageData['orderNo'],
        'amount': pageData['actualPaymentAmount'],
        'orderRemark': pageData['orderRemark'],
        'transportId': pageData['transportId'],
        'productIdsList': (pageData['couponInfo']
            as Map<String, dynamic>)['productCouponIds'],
        'commissionIdsList': (pageData['couponInfo']
            as Map<String, dynamic>)['commissionCouponIds'],
      });
      if (res['result']['code'] == 200) {
        final paymentId = res['data']['paymentId'];
        Get.toNamed(AppRoutes.OrderPayment, parameters: {
          'orderNo': pageData['orderNo'].toString(),
          'paymentId': paymentId,
        });
      } else {
        // 处理各种错误情况
        if (res['result']['code'] == 81005) {
          // 支付信息错误
          showPayInfoErrorDialog(Get.context!);
          update();
        } else if (res['result']['code'] == 70114) {
          // 产品券信息错误
          pageData['productCouponError'] = true;
          (pageData['couponInfo'] as Map<String, dynamic>)['productCouponIds'] =
              [];
          (pageData['couponInfo']
              as Map<String, dynamic>)['productCouponAmount'] = 0;
          showCouponInfoErrorDialog(Get.context!);
          updatePaymentAmount();
          getCouponList();
          update();
        } else if (res['result']['code'] == 70115) {
          // 佣金券信息错误
          pageData['commissionCouponError'] = true;
          (pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponIds'] = [];
          (pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponAmount'] = 0;
          showCouponInfoErrorDialog(Get.context!);
          updatePaymentAmount();
          getCouponList();
          update();
        } else if (res['result']['code'] == 70116) {
          // 产品/佣金都错误
          pageData['allCouponError'] = true;
          (pageData['couponInfo'] as Map<String, dynamic>)['productCouponIds'] =
              [];
          (pageData['couponInfo']
              as Map<String, dynamic>)['productCouponAmount'] = 0;
          (pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponIds'] = [];
          (pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponAmount'] = 0;
          showCouponInfoErrorDialog(Get.context!);
          updatePaymentAmount();
          getCouponList();
          update();
        } else {
          Get.snackbar(
            '提示',
            res['result']['message'] ?? '支付失败',
            snackPosition: SnackPosition.TOP,
          );
        }
      }
    } catch (e) {
      print('Error during payment: $e');
      Get.snackbar(
        '提示',
        '支付请求失败',
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  // 确认选择优惠券
  void confirmCouponSelection() {
    final type = pageData['chooseCouponType'];
    if (type == 'COUPON_TYPE_PRODUCT') {
      (pageData['couponInfo'] as Map<String, dynamic>)['productCouponAmount'] =
          pageData['chooseCouponAmount'];
      (pageData['couponInfo'] as Map<String, dynamic>)['productCouponList'] =
          pageData['chooseCouponList'];
      (pageData['couponInfo'] as Map<String, dynamic>)['productCouponIds'] =
          pageData['selectedCouponIds'];
    } else if (type == 'COUPON_TYPE_COMMISSION') {
      (pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponAmount'] =
          pageData['chooseCouponAmount'];
      (pageData['couponInfo'] as Map<String, dynamic>)['commissionCouponList'] =
          pageData['chooseCouponList'];
      (pageData['couponInfo'] as Map<String, dynamic>)['commissionCouponIds'] =
          pageData['selectedCouponIds'];
    }

    updatePaymentAmount();
    pageData['showCouponChoose'] = false;
  }

  // 关闭优惠券选择
  void closeCouponSelection() {
    pageData['showCouponChoose'] = false;
  }

  // 打开服务条款
  void openTermsOfService() {
    // 跳转到服务条款页面
    Get.toNamed('/article/detail', arguments: {'type': 'terms'});
  }

  // 获取规格名称
  String getSkuName(List<dynamic> specs) {
    if (specs == null) return ''; // 处理 specs 为 null 的情况
    final names = specs
        .map((spec) => '${spec['specName']}: ${spec['itemName']}')
        .toList();
    return names.join('; ');
  }

  // 检查可用优惠券列表
  Future<void> onCheckAvailableList() async {
    pageData['chooseCouponLoading'] = true;
    try {
      // 选中的优惠券
      final selectCouponModelsList =
          (pageData['selectedCouponIds'] as List<String>)
              .map((id) {
                final coupon =
                    (pageData['chooseCouponList'] as List<Map<String, dynamic>>)
                        .firstWhere(
                  (coupon) => coupon['id'] == id,
                  orElse: () => <String, dynamic>{},
                );
                if (coupon.isNotEmpty) {
                  coupon['check'] = true;
                }
                return coupon;
              })
              .where((coupon) => coupon.isNotEmpty)
              .toList();

      // 未选中的优惠券
      final notCouponModelsList =
          (pageData['chooseCouponList'] as List<Map<String, dynamic>>)
              .where((coupon) {
        final isNotSelected = !(pageData['selectedCouponIds'] as List<String>)
            .contains(coupon['id']);
        if (isNotSelected) {
          coupon['check'] = false;
        }
        return isNotSelected;
      }).toList();

      final res = await OrderService.useCheckAvailableList({
        'orderNo': pageData['orderNo'],
        'selectCouponModelsList': selectCouponModelsList,
        'notCouponModelsList': notCouponModelsList,
        'couponType': pageData['chooseCouponType'],
      });

      if (res != null &&
          res['result'] != null &&
          res['result']['code'] == 200) {
        var totalDiscountAmount = 0.0;
        final couponList =
            pageData['chooseCouponList'] as List<Map<String, dynamic>>;
        final resData =
            (res['data'] is List) ? res['data'] as List<dynamic> : <dynamic>[];

        for (final coupon in couponList) {
          final matchedCoupon = resData.firstWhere(
            (item) => item['id'] == coupon['id'],
            orElse: () => null,
          );

          if (matchedCoupon != null) {
            // 更新优惠券状态
            coupon['check'] = matchedCoupon['check'];
            coupon['availableFlag'] = matchedCoupon['availableFlag'];
            coupon['ticketActualPrice'] = matchedCoupon['ticketActualPrice'];
            coupon['notAvailableReason'] = matchedCoupon['notAvailableReason'];

            // 计算选中优惠券的优惠金额
            if (coupon['check'] == true) {
              totalDiscountAmount +=
                  (coupon['ticketActualPrice'] as num).toDouble();
            }

            // 如果优惠券未被选中但在已选列表中，则移除
            if (coupon['check'] != true &&
                (pageData['selectedCouponIds'] as List<String>)
                    .contains(coupon['id'])) {
              (pageData['selectedCouponIds'] as List<String>)
                  .remove(coupon['id']);
            }
          }
        }
        // 更新优惠金额（负数表示优惠）
        pageData['chooseCouponAmount'] = -totalDiscountAmount;
      } else {
        Get.snackbar('提示', res['result']['message'] ?? '检查优惠券失败');
      }
    } catch (e) {
      Get.snackbar('错误', '检查优惠券失败');
    } finally {
      pageData['chooseCouponLoading'] = false;
    }
  }

  // 重新选择优惠券
  void onChooseCouponAgain() {
    if ((pageData['productCouponError'] == true) ||
        (pageData['allCouponError'] == true)) {
      onChooseCoupon('COUPON_TYPE_PRODUCT');
    } else {
      onChooseCoupon('COUPON_TYPE_COMMISSION');
    }
  }

  // 确认选择优惠券
  void onChooseCouponConfirm() {
    final type = pageData['chooseCouponType'];
    final couponInfo = pageData['couponInfo'] as Map<String, dynamic>;

    if (type == 'COUPON_TYPE_PRODUCT') {
      couponInfo['productCouponAmount'] = pageData['chooseCouponAmount'];
      couponInfo['productCouponList'] = pageData['chooseCouponList'];
      couponInfo['productCouponIds'] = pageData['selectedCouponIds'];
    } else if (type == 'COUPON_TYPE_COMMISSION') {
      couponInfo['commissionCouponAmount'] = pageData['chooseCouponAmount'];
      couponInfo['commissionCouponList'] = pageData['chooseCouponList'];
      couponInfo['commissionCouponIds'] = pageData['selectedCouponIds'];
    }

    onUpdatePaymentAmount();
    pageData['showCouponChoose'] = false;
  }

  // 取消选择优惠券
  void onChooseCouponCancel() {
    pageData['showCouponChoose'] = false;
  }

  // 计算优惠金额和实际支付金额
  void onUpdatePaymentAmount() {
    final couponInfo = pageData['couponInfo'] as Map<String, dynamic>;

    // 计算总优惠金额
    pageData['discountedAmount'] = _to2Fixed(
        (couponInfo['productCouponAmount'] ?? 0.0) +
            (couponInfo['commissionCouponAmount'] ?? 0.0));

    // 更新实际支付金额
    pageData['actualPaymentAmount'] = _to2Fixed(
        (pageData['paymentAmount'] as num? ?? 0) +
            (pageData['discountedAmount'] as num? ?? 0));

    update();
  }

  // 选择优惠券
  void onChooseCoupon(String type) {
    pageData['chooseCouponType'] = type;
    pageData['showCouponChoose'] = true;

    if (type == 'COUPON_TYPE_PRODUCT') {
      pageData['selectedCouponIds'] = List<String>.from((pageData['couponInfo']
              as Map<String, dynamic>)['productCouponIds'] ??
          []);
      pageData['chooseCouponList'] = List<Map<String, dynamic>>.from(
          (pageData['couponInfo']
                  as Map<String, dynamic>)['productCouponList'] ??
              []);
      pageData['chooseCouponAmount'] = ((pageData['couponInfo']
                  as Map<String, dynamic>)['productCouponAmount'] ??
              0)
          .toDouble();
    } else if (type == 'COUPON_TYPE_COMMISSION') {
      pageData['selectedCouponIds'] = List<String>.from((pageData['couponInfo']
              as Map<String, dynamic>)['commissionCouponIds'] ??
          []);
      pageData['chooseCouponList'] = List<Map<String, dynamic>>.from(
          (pageData['couponInfo']
                  as Map<String, dynamic>)['commissionCouponList'] ??
              []);
      pageData['chooseCouponAmount'] = ((pageData['couponInfo']
                  as Map<String, dynamic>)['commissionCouponAmount'] ??
              0)
          .toDouble();
    }

    update();
    CouponSelect.show(Get.context!, this);
  }

  // 获取可用优惠券列表
  Future<void> getCouponList([String? init]) async {
    try {
      final res = await OrderService.useGetCouponUsableList({
        'orderNo': pageData['orderNo'],
      });

      if (res['result']['code'] == 200) {
        if (pageData['productCouponError'] == true) {
          (pageData['couponInfo']
                  as Map<String, dynamic>)['productCouponList'] =
              res['data']['productCouponList'];
          return;
        } else if (pageData['commissionCouponError'] == true) {
          (pageData['couponInfo']
                  as Map<String, dynamic>)['commissionCouponList'] =
              res['data']['commissionCouponList'];
          return;
        }

        pageData['couponInfo'] = res['data'];

        if (init != null) {
          checkCouponInfo();
        }
      } else {
        Get.snackbar(
          '提示',
          res['result']['message'] ?? '获取优惠券列表失败',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      Get.snackbar('提示', '获取优惠券列表失败', snackPosition: SnackPosition.TOP);
    }
  }

  // 检查优惠券信息（校验该订单的优惠券是否被锁定）
  void checkCouponInfo() {
    bool isLocked = false; // 标记是否存在锁定状态的优惠券

    // 定义优惠券列表数组
    final couponLists = [
      {
        'list': (pageData['couponInfo']
            as Map<String, dynamic>)['productCouponList'] as List<dynamic>,
        'type': 'COUPON_TYPE_PRODUCT',
      },
      {
        'list': (pageData['couponInfo']
            as Map<String, dynamic>)['commissionCouponList'] as List<dynamic>,
        'type': 'COUPON_TYPE_COMMISSION',
      },
    ];

    // 检查是否存在锁定状态的优惠券
    isLocked = couponLists.any((item) => (item['list'] as List<dynamic>)
        .any((coupon) => coupon['ticketStatus'] == 'TICKET_LOCK'));

    // 如果存在锁定状态，才进行计算
    if (isLocked) {
      for (final item in couponLists) {
        double totalPreferentialAmount = 0; // 累计优惠金额
        final selectedCouponIds = <String>[]; // 选中的优惠券ID列表
        final chooseCouponList = <Map<String, dynamic>>[]; // 选中的优惠券详情列表

        // 遍历优惠券，计算选中优惠券的信息
        for (final coupon in item['list'] as List<dynamic>) {
          if (coupon['check'] == true) {
            totalPreferentialAmount +=
                (coupon['ticketActualPrice'] ?? 0) as num;
            selectedCouponIds.add(coupon['id'] as String);
            // 深拷贝优惠券信息
            chooseCouponList.add(Map<String, dynamic>.from(coupon as Map));
          }
        }

        // 根据优惠券类型更新对应的数据
        if (item['type'] == 'COUPON_TYPE_PRODUCT') {
          (pageData['couponInfo']
                  as Map<String, dynamic>)['productCouponAmount'] =
              -totalPreferentialAmount;
          // 深拷贝选中的优惠券ID列表
          (pageData['couponInfo'] as Map<String, dynamic>)['productCouponIds'] =
              List<String>.from(selectedCouponIds);
        } else if (item['type'] == 'COUPON_TYPE_COMMISSION') {
          (pageData['couponInfo']
                  as Map<String, dynamic>)['commissionCouponAmount'] =
              -totalPreferentialAmount;
          // 深拷贝选中的优惠券ID列表
          (pageData['couponInfo']
                  as Map<String, dynamic>)['commissionCouponIds'] =
              List<String>.from(selectedCouponIds);
        }
      }

      // 更新支付金额
      updatePaymentAmount();
    }
  }

  // 更新支付金额
  void updatePaymentAmount() {
    final couponInfo = pageData['couponInfo'] as Map<String, dynamic>? ?? {};
    final productCouponAmount =
        (couponInfo['productCouponAmount'] as num?) ?? 0.0;
    final commissionCouponAmount =
        (couponInfo['commissionCouponAmount'] as num?) ?? 0.0;

    // 优惠总金额（保留两位小数）
    final discountedAmount =
        _to2Fixed(productCouponAmount + commissionCouponAmount);
    // 支付基础金额
    final paymentAmount = (pageData['paymentAmount'] as num?) ?? 0.0;
    // 实际需支付金额（保留两位小数）
    final actualPaymentAmount = _to2Fixed(paymentAmount + discountedAmount);

    pageData['discountedAmount'] = discountedAmount;
    pageData['actualPaymentAmount'] = actualPaymentAmount;
    update();
  }

  double _to2Fixed(num value) => double.parse(value.toStringAsFixed(2));

  // 协议相关状态管理
  void onHasReadFinished() {
    _hasReadFinished.value = true;
    update();
  }

  void onUpdateAcceptTerms(bool value) {
    _acceptTerms.value = value;
    update();
  }

  void onOpenAgree(BuildContext context) {
    // TODO: 埋点
    // MyStat.addPageEvent('payment_open_terms', '打开支付协议对话框');
    OrderPayAgree.show(
      context,
      onHasReadFinished,
      onUpdateAcceptTerms,
    );
  }

  // 显示取消订单弹窗
  void showCancelDrawer(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      builder: (context) => OrderCancelDrawer(
        orderNo: pageData['orderNo'].toString(),
        onCancelSuccess: () => getOrderDetail(),
      ),
    );
  }

  // 优惠券信息错误弹窗
  void showCouponInfoErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return Dialog(
          child: Padding(
            padding: EdgeInsets.symmetric(vertical: 24.sp, horizontal: 28.sp),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.info_outlined,
                      color: AppColors.primaryColor,
                      size: 20.sp,
                    ),
                    SizedBox(width: 4.sp),
                    Text(
                      I18n.of(context)
                              ?.translate('cm_order.chooseCouponError') ??
                          '',
                      style: TextStyle(
                        fontSize: 14.sp,
                        color: Color(0xFF333333),
                      ),
                    ),
                  ],
                ),
                SizedBox(height: 18.sp),
                SizedBox(
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(50.r),
                      ),
                      padding: EdgeInsets.symmetric(
                          vertical: 0.sp, horizontal: 16.sp),
                      minimumSize: Size(0, 36.sp),
                      tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                      elevation: 0,
                    ),
                    onPressed: () {
                      Navigator.of(ctx).pop();
                      onChooseCouponAgain();
                    },
                    child: Text(
                      I18n.of(context)
                              ?.translate('cm_order.chooseCouponAgain') ??
                          '',
                      style: TextStyle(
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  // 付款信息错误弹窗
  void showPayInfoErrorDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (ctx) {
        return Dialog(
          child: Stack(
            children: [
              Padding(
                padding:
                    EdgeInsets.symmetric(vertical: 24.sp, horizontal: 28.sp),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.info_outlined,
                          color: AppColors.primaryColor,
                          size: 22.sp,
                        ),
                        SizedBox(width: 8.sp),
                        Flexible(
                          child: Text(
                            I18n.of(context)
                                    ?.translate('cm_order.refreshPage') ??
                                '',
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Color(0xFF333333),
                            ),
                            softWrap: true,
                            overflow: TextOverflow.visible,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              Positioned(
                top: 8.sp,
                right: 8.sp,
                child: GestureDetector(
                    onTap: () {
                      Navigator.of(ctx).pop();
                      onClosePayError();
                    },
                    child: Icon(
                      Icons.close,
                      size: 20.sp,
                      color: Color(0xFF666666),
                    )),
              ),
            ],
          ),
        );
      },
    );
  }
}
