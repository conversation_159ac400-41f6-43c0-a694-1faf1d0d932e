import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/main/main_controller.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

// TabBar项数据模型
class TabBarItemData {
  final String icon;
  final String? iconActive;
  final bool needLogin;
  final String route;
  final String name;
  final String spmCode;

  TabBarItemData({
    required this.icon,
    this.iconActive,
    required this.needLogin,
    required this.route,
    required this.name,
    required this.spmCode,
  });
}

class AppTabbar extends StatefulWidget {
  final int currentIndex;

  const AppTabbar({
    super.key,
    required this.currentIndex,
  });

  @override
  State<AppTabbar> createState() => _AppTabbarState();
}

class _AppTabbarState extends State<AppTabbar> {
  late List<TabBarItemData> tabBarItems;
  MidCartModel? cartData;
  MarketingCategoryTreeResp? categoryData;
  final MainController mainController = Get.put(MainController());
  AuthLoginModel? userInfo;

  @override
  void initState() {
    super.initState();
    _initTabBarItems();
    _loadData();
  }

  // 初始化TabBar项目
  void _initTabBarItems() {
    tabBarItems = [
      TabBarItemData(
        icon: 'assets/images/common/mobile-home.svg',
        iconActive: 'assets/images/common/mobile-home-active.svg',
        needLogin: false,
        route: AppRoutes.HomePage,
        name: 'cm_bar.home',
        spmCode: 'navigation-bottom-home',
      ),
      TabBarItemData(
        icon: 'assets/images/common/mobile-category.svg',
        iconActive: 'assets/images/common/mobile-category-active.svg',
        needLogin: false,
        route: AppRoutes.CategoryPage,
        name: 'cm_bar.category',
        spmCode: 'navigation-bottom-category',
      ),
      TabBarItemData(
        icon: 'assets/images/common/mobile-find.svg',
        needLogin: true,
        route: AppRoutes.SearchLooking,
        name: 'cm_bar.find',
        spmCode: 'button-find-bottom-mid',
      ),
      TabBarItemData(
        icon: 'assets/images/common/mobile-cart.svg',
        iconActive: 'assets/images/common/mobile-cart-active.svg',
        needLogin: true,
        route: AppRoutes.CartPage,
        name: 'cm_bar.list',
        spmCode: 'navigation-bottom-cart',
      ),
      TabBarItemData(
        icon: 'assets/images/common/mobile-user.svg',
        iconActive: 'assets/images/common/mobile-user-active.svg',
        needLogin: false,
        route: AppRoutes.MinePage,
        name: 'cm_bar.account',
        spmCode: 'navigation-bottom-myhome',
      ),
    ];
  }

  // 加载数据
  Future<void> _loadData() async {
    try {
      // 获取购物车数据
      final cartResponse = await InquiryAPI.useGetCart({});
      if (cartResponse?['result']?['code'] == 200) {
        setState(() {
          cartData = MidCartModel.fromJson(cartResponse?['data']);
        });
      }

      // 获取用户信息
      userInfo = await Global.getUserInfo();
    } catch (e) {
      print('加载数据失败: $e');
    }
  }

  // 处理TabBar点击
  void _onTabBarTap(int index) {
    final tabBarItem = tabBarItems[index];

    // 如果点击的是当前页面，不需要跳转
    if (index == widget.currentIndex) {
      return;
    }

    // 检查是否需要登录
    if (tabBarItem.needLogin &&
        (userInfo == null || userInfo!.username?.isEmpty == true)) {
      // 跳转到登录页面
      NavigatorUtil.pushNamed(context, AppRoutes.LoginPage, arguments: {
        'pageSource': tabBarItem.route,
      });
      return;
    }

    // 直接使用路由跳转，无需switch判断
    NavigatorUtil.pushNamed(context, tabBarItem.route);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 25,
            offset: Offset(0, -1),
          ),
        ],
      ),
      child: SafeArea(
        child: SizedBox(
          height: 78.sp,
          child: Stack(
            clipBehavior: Clip.none, // 允许子元素超出边界
            children: [
              // 底部TabBar容器
              Positioned(
                left: 0,
                right: 0,
                bottom: 0,
                child: Container(
                  height: 78.sp,
                  padding: EdgeInsets.only(
                      top: 4.sp, bottom: 0.sp, right: 6.sp, left: 6.sp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: tabBarItems.asMap().entries.map((entry) {
                      int index = entry.key;
                      TabBarItemData item = entry.value;

                      // 中间的找货按钮位置留空
                      if (index == 2) {
                        return SizedBox(width: 60.sp); // 占位符
                      }
                      return _buildTabBarItem(item, index);
                    }).toList(),
                  ),
                ),
              ),
              // 中间找货按钮 - 绝对定位，可以超出容器
              Positioned(
                left: 0,
                right: 0,
                bottom: 14.sp, // 从底部向上偏移14sp，模拟Vue的-mt-[14px]
                child: Center(
                  child: _buildFindButton(tabBarItems[2], 2),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTabBarItem(TabBarItemData item, int index) {
    bool isActive = widget.currentIndex == index;

    return GestureDetector(
      onTap: () => _onTabBarTap(index),
      child: SizedBox(
        width: 60.sp,
        child: Stack(
          clipBehavior: Clip.none,
          children: [
            Column(
              children: [
                SizedBox(
                  width: 26.sp,
                  height: 26.sp,
                  child: SvgPicture.asset(
                    isActive && item.iconActive != null
                        ? item.iconActive!
                        : item.icon,
                    width: 26.sp,
                    height: 26.sp,
                  ),
                ),
                SizedBox(height: 2.sp),
                Text(
                  I18n.of(context)?.translate(item.name) ?? item.name,
                  style: TextStyle(
                    fontSize: 12.sp,
                    height: 1.0,
                    color: isActive ? Color(0xFF333333) : Color(0xFF7F7F7F),
                  ),
                  maxLines: 1,
                ),
              ],
            ),
            if (item.route == AppRoutes.MinePage &&
                (userInfo == null || userInfo!.username?.isEmpty == true))
              Positioned(
                right: -14,
                top: -13,
                child: Image.asset(
                  'assets/images/common/gift.webp',
                  width: 54,
                  height: 54,
                ),
              ),
          ],
        ),
      ),
    );
  }

  // 构建中间的找货按钮
  Widget _buildFindButton(TabBarItemData item, int index) {
    return GestureDetector(
      onTap: () => _onTabBarTap(index),
      child: Column(
        mainAxisSize: MainAxisSize.min, // 使用最小尺寸，避免溢出
        children: [
          Container(
            width: 52.sp,
            height: 52.sp,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white.withOpacity(0.95),
            ),
            child: Stack(
              children: [
                // 背景遮罩效果（模拟Vue的::before伪元素）
                Positioned(
                  top: 0,
                  left: 0,
                  right: 0,
                  height: 14,
                  child: Container(
                    decoration: BoxDecoration(
                      color: Colors.white.withOpacity(0.95),
                      shape: BoxShape.circle,
                    ),
                  ),
                ),
                Positioned(
                  top: 4.sp,
                  left: 7.sp,
                  child: Container(
                    width: 38.sp,
                    height: 38.sp,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Color(0xFFE50113),
                    ),
                    child: Center(
                      child: SvgPicture.asset(
                        item.icon,
                        width: 18.sp,
                        height: 18.sp,
                        colorFilter: ColorFilter.mode(
                          Colors.white,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          Transform.translate(
            offset: Offset(0, -6.sp),
            child: Text(
              I18n.of(context)?.translate(item.name) ?? item.name,
              style: TextStyle(
                fontSize: 12.sp,
                height: 1.0,
                color: Color(0xFF333333),
              ),
              textAlign: TextAlign.center,
            ),
          ),
          Transform.translate(
            offset: Offset(0, -2.sp),
            child: Image.asset(
              'assets/images/logo.png',
              width: 64.sp,
            ),
          ),
        ],
      ),
    );
  }
}
