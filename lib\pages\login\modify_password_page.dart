import 'dart:async';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/login/login_controller.dart';

class ModifyPasswordPage extends StatefulWidget {
  final VoidCallback? onModifySuccess;
  final VoidCallback? onGoLogin;

  const ModifyPasswordPage({
    Key? key,
    this.onModifySuccess,
    this.onGoLogin,
  }) : super(key: key);

  @override
  _ModifyPasswordPageState createState() => _ModifyPasswordPageState();
}

class _ModifyPasswordPageState extends State<ModifyPasswordPage> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _captchaController = TextEditingController();
  bool _showPassword = false;
  int _timeLeft = 0;
  Timer? _timer;
  final LoginController loginController = Get.put(LoginController());

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    _captchaController.dispose();
    _timer?.cancel();
    super.dispose();
  }

  // 发送验证码
  Future<void> _onSendCaptcha() async {
    // 验证邮箱格式
    String email = _emailController.text;
    final pattern = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');
    if (email.isEmpty || !pattern.hasMatch(email)) {
      loginController.showErrorMessage(
          I18n.of(context)?.translate('cm_common.emailTips') ?? '');
      return;
    }

    try {
      final res = await UserAPI.useSendCaptcha({
        "str": email.trim(),
      });

      if (res['result']['code'] == 200) {
        // 验证码发送成功，开始倒计时
        setState(() {
          _timeLeft = 60;
        });
        _timer = Timer.periodic(Duration(seconds: 1), (timer) {
          if (_timeLeft == 0) {
            timer.cancel();
            setState(() {
              _timeLeft = 0;
            });
          } else {
            setState(() {
              _timeLeft--;
            });
          }
        });

        loginController.showSuccessMessage(
            I18n.of(context)?.translate('cm_common_sendSuccess') ?? '');
      } else {
        loginController.showErrorMessage(res['result']['message'] ?? '');
      }
    } catch (e) {
      print(e);
    }
  }

  // 去登录页
  void _onGoLogin() {
    if (widget.onGoLogin != null) {
      // 如果在模态框内，使用回调
      widget.onGoLogin!();
    } else {
      // 如果不是模态框，直接导航
      Get.back();
    }
  }

  // 提交修改密码
  void _modifyPasswordAction(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      try {
        bool success = await loginController.modifyPassword(
          _emailController.text,
          _passwordController.text,
          _captchaController.text,
          context,
        );

        if (success) {
          if (widget.onModifySuccess != null) {
            // 如果设置了修改成功回调，则调用
            widget.onModifySuccess!();
          } else {
            // 在模态框中，返回修改成功
            Navigator.of(context).pop(true);
            ScaffoldMessenger.of(context).showSnackBar(
              SnackBar(
                content: Text('密码修改成功'),
                duration: Duration(seconds: 2),
              ),
            );
          }
        }
      } catch (e) {
        print(e);
        loginController.showErrorMessage('密码修改失败');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title:
            Text(I18n.of(context)?.translate("cm_common_forgotPassword") ?? ''),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: _onGoLogin,
        ),
      ),
      body: SafeArea(
        child: _buildPageContent(context),
      ),
    );
  }

  // 页面主体内容
  Widget _buildPageContent(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(20.0.sp),
      child: Form(
        key: _formKey,
        child: ListView(
          physics: ClampingScrollPhysics(),
          children: [
            _buildEmailInput(),
            SizedBox(height: 20.h),
            _buildPasswordInput(),
            SizedBox(height: 20.h),
            _buildCaptchaInput(),
            SizedBox(height: 30.h),
            SizedBox(
              width: double.infinity,
              height: 48.sp,
              child: ElevatedButton(
                onPressed: () => _modifyPasswordAction(context),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: Colors.white,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(10.0),
                  ),
                  elevation: 0,
                ),
                child: Text(
                  I18n.of(context)?.translate('cm_common.sendingAndLogging') ??
                      '',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.w400,
                  ),
                ),
              ),
            ),
            SizedBox(height: 20.h),
            // 验证码提示
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I18n.of(context)?.translate("cm_common.noSendCode") ?? '',
                  style: TextStyle(fontSize: 14.sp),
                ),
                SizedBox(height: 10.h),
                Wrap(
                  runSpacing: 10.h,
                  children: [
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 10.sp, top: 6.sp),
                          child: Icon(
                            Icons.circle,
                            size: 5.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        SizedBox(width: 6.sp),
                        Expanded(
                          child: Text(
                            I18n.of(context)
                                    ?.translate("cm_common.sendCodeTime") ??
                                '',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ),
                      ],
                    ),
                    Row(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Padding(
                          padding: EdgeInsets.only(left: 10.sp, top: 6.sp),
                          child: Icon(
                            Icons.circle,
                            size: 5.sp,
                            color: const Color(0xFF333333),
                          ),
                        ),
                        SizedBox(width: 6.sp),
                        Expanded(
                          child: Text(
                            I18n.of(context)
                                    ?.translate("cm_common.checkEmail") ??
                                '',
                            style: TextStyle(fontSize: 12.sp),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            )
          ],
        ),
      ),
    );
  }

  Widget _buildEmailInput() {
    return TextFormField(
      controller: _emailController,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.email'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 14.sp,
        ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputEmail'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp,
            top: 4.0.sp,
            right: 4.0.sp,
            bottom: 4.0.sp,
          ),
          child: SvgPicture.asset(
            'assets/images/login/email.svg',
            width: 20.sp,
            height: 20.sp,
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp,
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp),
        errorMaxLines: 3,
      ),
      cursorColor: AppColors.primaryColor,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputEmailTips');
        }
        final pattern = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');
        if (!pattern.hasMatch(value)) {
          return I18n.of(context)?.translate('cm_common.emailTips');
        }
        return null;
      },
    );
  }

  Widget _buildPasswordInput() {
    return TextFormField(
      controller: _passwordController,
      obscureText: !_showPassword,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.password'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 14.sp,
        ),
        suffixIcon: GestureDetector(
          onTap: () {
            setState(() {
              _showPassword = !_showPassword;
            });
          },
          child: Icon(
            _showPassword ? Icons.visibility : Icons.visibility_off,
            size: 18.w,
          ),
        ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputPassword'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp,
            top: 4.0.sp,
            right: 4.0.sp,
            bottom: 4.0.sp,
          ),
          child: SvgPicture.asset(
            'assets/images/login/password.svg',
            width: 20.sp,
            height: 20.sp,
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp,
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp),
        errorMaxLines: 3,
      ),
      cursorColor: AppColors.primaryColor,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputPwdTips');
        }
        final specialCharPattern = RegExp(r'[^A-Za-z\d]');
        final pattern = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$');
        if (specialCharPattern.hasMatch(value)) {
          return I18n.of(context)?.translate('cm_common.pwdFormatTips');
        }
        if (!pattern.hasMatch(value)) {
          return I18n.of(context)?.translate('cm_common.pwdLengthTips');
        }
        return null;
      },
    );
  }

  Widget _buildCaptchaInput() {
    return TextFormField(
      controller: _captchaController,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.verification'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 14.sp,
        ),
        suffixIcon: _timeLeft > 0
            ? TextButton(
                onPressed: null,
                child: Text(
                  '$_timeLeft s',
                  style: TextStyle(color: Color(0xFF034AA6), fontSize: 14.sp),
                ),
              )
            : TextButton(
                onPressed: _onSendCaptcha,
                child: Text(
                  I18n.of(context)?.translate('cm_common.sendCode') ?? '',
                  style: TextStyle(color: Color(0xFF034AA6), fontSize: 14.sp),
                ),
              ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputVerification'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Container(
          width: 10.sp,
          height: 10.sp,
          color: Colors.transparent,
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 10.sp,
          minHeight: 10.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp),
        errorMaxLines: 3,
      ),
      cursorColor: AppColors.primaryColor,
      validator: (value) {
        if (value == null || value.isEmpty) {
          return I18n.of(context)?.translate('cm_common.verificationTips');
        }
        return null;
      },
    );
  }
}
