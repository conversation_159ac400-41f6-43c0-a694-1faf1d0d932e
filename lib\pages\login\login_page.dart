import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/login/login_controller.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';

class LoginPage extends StatefulWidget {
  final VoidCallback? onLoginSuccess;
  final VoidCallback? onGoRegister;
  final VoidCallback? onGoModifyPwd;

  const LoginPage({
    super.key,
    this.onLoginSuccess,
    this.onGoRegister,
    this.onGoModifyPwd,
  });

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  final _unameController = TextEditingController();
  final _pwdController = TextEditingController();
  bool _showPassword = false;
  final LoginController loginController = Get.put(LoginController());

  @override
  void initState() {
    super.initState();

    _unameController.addListener(() {
      setState(() {});
    });
    _pwdController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _unameController.dispose();
    _pwdController.dispose();
    super.dispose();
  }

  // 忘记密码 - 使用修改密码模态框
  void _onGoModifyPwd() {
    if (widget.onGoModifyPwd != null) {
      // 如果设置了回调，直接调用
      widget.onGoModifyPwd!();
    } else {
      // 如果不是在模态框中，则使用原来的导航方式
      final arguments = Get.arguments as Map<String, dynamic>?;
      final redirectRoute = arguments?['redirectRoute'];
      Get.toNamed(AppRoutes.ModifyPassword,
          arguments: {'redirectRoute': redirectRoute});
    }
  }

  // 注册
  void _onGoRegister() {
    if (widget.onGoRegister != null) {
      // 如果设置了回调，直接调用
      widget.onGoRegister!();
    } else {
      // 如果不是在模态框中，则使用原来的导航方式
      final arguments = Get.arguments as Map<String, dynamic>?;
      final redirectRoute = arguments?['redirectRoute'];
      Get.toNamed(AppRoutes.Register,
          arguments: {'redirectRoute': redirectRoute});
    }
  }

  // 登录
  void _loginAction(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      try {
        bool success = await loginController.login(
          _unameController.text,
          _pwdController.text,
          context,
        );
        if (success) {
          if (widget.onLoginSuccess != null) {
            // 如果设置了登录成功回调，则调用
            widget.onLoginSuccess!();
          } else {
            // 模态模式下，返回登录成功
            Navigator.of(context).pop(true);
          }
        }
      } catch (e) {
        loginController.showErrorMessage('登录出错: $e');
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      showWhatsAppButton: false,
      appBar: MyAppBar(
        leadingType: AppBarBackType.None,
        backgroundColor: Colors.white,
        elevation: 0,
        title: GestureDetector(
          onTap: () {
            NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
          },
          child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Image(
              image: AssetImage("assets/images/logo.png"),
              width: 150,
              fit: BoxFit.fill,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFF8F0000),
                              image: DecorationImage(
                                image: AssetImage("assets/images/login/bg.jpg"),
                                fit: BoxFit.contain,
                                alignment: Alignment.topCenter,
                              ),
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 0.0, horizontal: 18.0.sp),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Container(
                                margin: EdgeInsets.only(
                                  top: 150.sp,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 10,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                                padding: EdgeInsets.all(18.0.sp),
                                child: IntrinsicHeight(
                                  child: Form(
                                    autovalidateMode: AutovalidateMode.disabled,
                                    key: _formKey,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: <Widget>[
                                        Text(
                                          I18n.of(context)?.translate(
                                                  'cm_common_login') ??
                                              '',
                                          style: TextStyle(
                                              fontSize: 24.sp,
                                              fontWeight: FontWeight.w500),
                                        ),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        _buildEmailInput(),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        _buildPasswordInput(),
                                        SizedBox(
                                          height: 30.h,
                                        ),
                                        SizedBox(
                                          width: double.infinity,
                                          child: GestureDetector(
                                            onTap: () => _onGoModifyPwd(),
                                            child: Text(
                                              I18n.of(context)?.translate(
                                                      'cm_common.forgotMyPwd') ??
                                                  '',
                                              textAlign: TextAlign.right,
                                              style: TextStyle(
                                                color: Color(0xFF034AA6),
                                                fontSize: 14.sp,
                                                height: 1.4, // 设置行高
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 24.h,
                                        ),
                                        SizedBox(
                                          width: double.infinity,
                                          height: 48.sp,
                                          child: ElevatedButton(
                                            onPressed: () =>
                                                _loginAction(context),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  AppColors.primaryColor,
                                              foregroundColor: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                              ),
                                              elevation: 0,
                                            ),
                                            child: Text(
                                              I18n.of(context)?.translate(
                                                      'cm_common_login') ??
                                                  '',
                                              style: TextStyle(
                                                fontSize: 18.sp,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(
                                          height: 24.h,
                                        ),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: <Widget>[
                                            Text(
                                              I18n.of(context)?.translate(
                                                      'cm_common_registerTip') ??
                                                  '',
                                              style: TextStyle(
                                                fontSize: 14.sp, // 设置字体大小
                                              ),
                                            ),
                                            SizedBox(width: 4.w), // 设置两个文本之间的间距
                                            GestureDetector(
                                              onTap: () =>
                                                  _onGoRegister(), // 定义点击事件处理函数
                                              child: Text(
                                                I18n.of(context)?.translate(
                                                        'cm_common_register') ??
                                                    '',
                                                style: TextStyle(
                                                  color: Color(0xFF034AA6),
                                                  fontSize: 16.sp, // 设置字体大小
                                                  height: 1.4, // 设置行高
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  // 号码
  Widget _buildEmailInput() {
    return TextFormField(
      controller: _unameController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.username'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          // 浮动时的样式
          color: AppColors.primaryColor, // 设置浮动字体颜色
          fontSize: 14.sp, // 设置浮动字体大小
        ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputEmailTips'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp, // 左边的边距
            top: 4.0.sp, // 上边的边距
            right: 4.0.sp, // 右边的边距
            bottom: 4.0.sp, // 下边的边距
          ), // 控制边距
          child: SvgPicture.asset(
            'assets/images/login/email.svg',
            width: 20.sp, // 图标宽度
            height: 20.sp, // 图标高度
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp, // 确保容器不会过大
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp), // 设置错误消息的字体大小
        errorMaxLines: 3,
      ),
      cursorColor: AppColors.primaryColor,
      validator: (v) {
        String t = v ?? '';
        final pattern = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');
        if (t.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputEmailTips');
        } else if (!pattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.emailTips');
        }
        return null;
      },
    );
  }

  // 密码
  Widget _buildPasswordInput() {
    return TextFormField(
      controller: _pwdController,
      keyboardType: TextInputType.visiblePassword,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.password'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          // 浮动时的样式
          color: AppColors.primaryColor, // 设置浮动字体颜色
          fontSize: 14.sp, // 设置浮动字体大小
        ),
        suffixIcon: Row(
          mainAxisSize: MainAxisSize.min,
          mainAxisAlignment: MainAxisAlignment.end,
          children: <Widget>[
            GestureDetector(
              onTap: () => {
                setState(() {
                  _showPassword = !_showPassword;
                })
              },
              child: Icon(
                _showPassword ? Icons.visibility : Icons.visibility_off,
                size: 18.w,
              ),
            ),
            SizedBox(width: 10.w),
          ],
        ),
        suffixIconConstraints: BoxConstraints(
          minWidth: 32.sp, // 确保容器不会过大
          minHeight: 32.sp,
        ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputPassword'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp,
            top: 4.0.sp,
            right: 4.0.sp,
            bottom: 4.0.sp,
          ),
          child: SvgPicture.asset(
            'assets/images/login/password.svg',
            width: 20.sp,
            height: 20.sp,
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp, // 确保容器不会过大
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp), // 设置错误消息的字体大小
        errorMaxLines: 3,
      ),
      obscureText: !_showPassword,
      cursorColor: AppColors.primaryColor,
      validator: (v) {
        String t = v ?? '';
        final specialCharPattern = RegExp(r'[^A-Za-z\d]');
        final pattern = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$');
        if (t.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputPwdTips');
        } else if (specialCharPattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.pwdFormatTips');
        } else if (!pattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.pwdLengthTips');
        }
        return null;
      },
    );
  }
}
