import 'package:flutter/material.dart';

class Iconfont {
  // iconName: redpacket_fil
  static const redpacket_fil = IconData(
    0xe7d3,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );

  // iconName: ticket_fill
  static const ticket_fill = IconData(
    0xe800,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );

  // iconName: round_like_fill
  static const round_like_fill = IconData(
    0xe80a,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );

  // iconName: card_fill
  static const card_fill = IconData(
    0xe81f,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );

  // iconName: settings
  static const settings = IconData(
    0xe68a,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );

  // iconName: notice
  static const notice = IconData(
    0xe70a,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  ///////
  static const discover = IconData(
    0xe67e,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const question = IconData(
    0xe691,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const game = IconData(
    0xe6df,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const tian = IconData(
    0xe748,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const magic = IconData(
    0xe74c,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const haodian = IconData(
    0xe76d,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const qi = IconData(
    0xe76f,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const shuang11 = IconData(
    0xe782,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const shop_light = IconData(
    0xe7b8,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const we_fill_light = IconData(
    0xe7d8,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const news_light = IconData(
    0xe7e6,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
  static const sports = IconData(
    0xe7f1,
    fontFamily: 'Iconfont',
    matchTextDirection: true,
  );
}
