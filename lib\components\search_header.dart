import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;
import 'package:image_picker/image_picker.dart';
import 'package:chilat2_mall_app/pages/product/product_list.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/image_compressor.dart';
import 'package:dio/dio.dart';
import 'dart:io';

enum SearchHeaderType { logoHeader, backHeader }

class SearchHeader extends StatelessWidget {
  final SearchHeaderType type;
  final int cartGoodsCount;
  final bool showCart;
  final bool showBackIcon;
  final bool showHomeIcon;
  final VoidCallback? onBack;
  final VoidCallback? onCartTap;
  final VoidCallback? onHomeTap;
  final VoidCallback? onSearch;
  final VoidCallback? onImageSearch;

  const SearchHeader({
    super.key,
    this.type = SearchHeaderType.logoHeader,
    this.cartGoodsCount = 0,
    this.showCart = false,
    this.showBackIcon = false,
    this.showHomeIcon = false,
    this.onBack,
    this.onCartTap,
    this.onHomeTap,
    this.onSearch,
    this.onImageSearch,
  });

  // 默认返回逻辑：如果可以返回，则返回上一页；否则返回首页
  void _handleBack(BuildContext context) {
    if (Navigator.canPop(context)) {
      Navigator.pop(context);
    } else {
      NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.white, // 设置背景色
      child: Padding(
        padding: EdgeInsets.symmetric(vertical: 6.sp),
        child: _buildContent(context),
      ),
    );
  }

  Widget _buildContent(BuildContext context) {
    if (type == SearchHeaderType.logoHeader) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 50.sp,
            child: Stack(
              children: [
                Center(
                  child: GestureDetector(
                    onTap: onHomeTap ??
                        () => NavigatorUtil.pushNamed(
                            context, AppRoutes.HomePage),
                    child: Image.asset(
                      'assets/images/logo.png',
                      width: 142.sp,
                    ),
                  ),
                ),
                if (showCart)
                  Positioned(
                    right: 16.sp,
                    top: 8.sp,
                    child: GestureDetector(
                      onTap: onCartTap,
                      child: Stack(
                        alignment: Alignment.topRight,
                        children: [
                          SvgPicture.asset(
                            'assets/images/common/cart.svg',
                            width: 26.sp,
                          ),
                          if (cartGoodsCount > 0)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 4, vertical: 1),
                              decoration: BoxDecoration(
                                color: AppColors.primaryColor,
                                borderRadius: BorderRadius.circular(10),
                              ),
                              child: Text(
                                cartGoodsCount > 99 ? '99+' : '$cartGoodsCount',
                                style: const TextStyle(
                                    color: Colors.white, fontSize: 10),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ),
              ],
            ),
          ),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.sp, vertical: 2.sp),
            child: Row(
              children: [
                if (showBackIcon)
                  GestureDetector(
                    onTap: onBack ?? () => _handleBack(context),
                    child: Padding(
                      padding: EdgeInsets.only(right: 6.sp),
                      child: SvgPicture.asset(
                        'assets/images/common/arrow-left.svg',
                        width: 13.sp,
                      ),
                    ),
                  )
                else if (showHomeIcon)
                  GestureDetector(
                    onTap: onHomeTap ??
                        () => NavigatorUtil.pushNamed(
                            context, AppRoutes.HomePage),
                    child: Padding(
                      padding: EdgeInsets.only(right: 4.sp),
                      child: SvgPicture.asset(
                        'assets/images/common/home.svg',
                        width: 22.sp,
                      ),
                    ),
                  ),
                Expanded(
                  child: _SearchBar(
                    onImageSearch: onImageSearch,
                  ),
                ),
              ],
            ),
          ),
        ],
      );
    } else {
      return Container(
        height: 38.sp,
        decoration: BoxDecoration(
          color: Colors.white,
          border: Border.all(color: Colors.white),
        ),
        child: Row(
          children: [
            if (showBackIcon)
              GestureDetector(
                onTap: onBack ?? () => _handleBack(context),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4.sp),
                  child: SvgPicture.asset(
                    'assets/images/common/arrow-left.svg',
                    width: 18.sp,
                  ),
                ),
              )
            else if (showHomeIcon)
              GestureDetector(
                onTap: onHomeTap ??
                    () => NavigatorUtil.pushNamed(context, AppRoutes.HomePage),
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 4.sp),
                  child: SvgPicture.asset(
                    'assets/images/common/home.svg',
                    width: 18.sp,
                  ),
                ),
              ),
            Expanded(
              child: _SearchBar(
                onImageSearch: onImageSearch,
              ),
            ),
          ],
        ),
      );
    }
  }
}

class _SearchBar extends StatefulWidget {
  final VoidCallback? onImageSearch;
  const _SearchBar({this.onImageSearch});

  @override
  State<_SearchBar> createState() => _SearchBarState();
}

class _SearchBarState extends State<_SearchBar> {
  final TextEditingController _controller = TextEditingController();
  final ImagePicker _picker = ImagePicker();

  // 搜索按钮和回车的处理逻辑
  void onKeywordClick([String? keyword]) {
    final word = (keyword?.trim().isEmpty ?? true)
        ? (_controller.text.trim())
        : keyword!.trim();
    // 埋点
    print('click_search: 搜索关键词：$word');

    // 跳转到产品列表页面
    if (word.isNotEmpty) {
      NavigatorUtil.pushNamed(context, AppRoutes.ProductList, arguments: {
        "keyword": word,
      });
    } else {
      // 如果关键词为空，仍然跳转但不带关键词
      NavigatorUtil.pushNamed(
        context,
        AppRoutes.ProductList,
      );
    }
  }

  // 以图搜图功能
  Future<void> onImageSearch() async {
    try {
      final XFile? pickedFile = await _picker.pickImage(
        source: ImageSource.gallery,
        // 限制图片质量和大小
        maxWidth: 800,
        imageQuality: 90,
      );

      if (pickedFile != null) {
        // 使用ImageCompressor压缩图片
        final originalFile = File(pickedFile.path);
        final originalSizeKB = originalFile.lengthSync() / 1024;
        print(
            '【图片压缩】原图大小: ${originalSizeKB.toStringAsFixed(2)}KB - ${originalFile.path}');

        // 显示上传进度对话框，移到压缩之前
        showDialog(
          context: Get.context!,
          barrierDismissible: false,
          builder: (BuildContext context) {
            return Dialog(
              backgroundColor: Colors.white,
              child: Container(
                padding: EdgeInsets.all(20),
                child: const Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    CircularProgressIndicator(
                      valueColor:
                          AlwaysStoppedAnimation<Color>(AppColors.primaryColor),
                    ),
                    SizedBox(height: 16),
                    Text(
                      '正在处理图片...',
                      style: TextStyle(color: Colors.black),
                    ),
                  ],
                ),
              ),
            );
          },
        );

        try {
          final compressedFile = await ImageCompressor.compress(
            originalFile,
            options: CompressOptions(),
          );

          final compressedSizeKB = compressedFile.lengthSync() / 1024;
          final isCompressed = originalFile.path != compressedFile.path;
          print(
              '【图片压缩】压缩后大小: ${compressedSizeKB.toStringAsFixed(2)}KB - ${isCompressed ? "已压缩" : "未压缩"}');

          await _uploadImageForSearch(compressedFile);
        } catch (e) {
          // 关闭加载对话框
          try {
            Navigator.of(Get.context!).pop();
          } catch (_) {}

          print('图片压缩失败: $e');
          // 显示错误提示
          ScaffoldMessenger.of(Get.context!).showSnackBar(
            SnackBar(content: Text('图片压缩失败: ${e.toString()}')),
          );
        }
      }
    } catch (e) {
      // 错误处理，确保对话框被关闭
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      print('图片选择失败: $e');
      // 显示错误提示
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('图片选择失败: ${e.toString()}')),
      );
    }
  }

  // 上传图片进行搜索
  Future<void> _uploadImageForSearch(File file) async {
    try {
      final fileStream = file.openRead();

      // 确保文件名以.jpg结尾
      String filename = 'search_${DateTime.now().millisecondsSinceEpoch}.jpg';

      final multipartFile = MultipartFile.fromStream(
        () => fileStream,
        await file.length(),
        filename: filename,
      );

      dynamic res = await ProductAPI.useUploadImage1688(
          FormData.fromMap({'file': multipartFile}));

      // 关闭加载对话框
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      if (res != null && res?['result']?['code'] == 200) {
        final imageId = res['data']?['imageId'];
        final imageUrl = res['data']?['imageUrl'];

        // 跳转到以图搜图结果页面
        Navigator.push(
          Get.context!,
          MaterialPageRoute(
            builder: (ctx) => ProductList(
              keyword: '',
              imageId: imageId,
              type: 'imgSearch',
              imageUrl: imageUrl,
            ),
          ),
        );
      } else {
        // 显示错误提示
        ScaffoldMessenger.of(Get.context!).showSnackBar(
          SnackBar(content: Text(res?['result']?['message'] ?? '图片上传失败')),
        );
      }
    } catch (e) {
      // 关闭加载对话框
      try {
        Navigator.of(Get.context!).pop();
      } catch (_) {}

      print('图片上传失败: $e');
      // 显示错误提示
      ScaffoldMessenger.of(Get.context!).showSnackBar(
        SnackBar(content: Text('图片上传失败: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    const borderRadius = BorderRadius.all(Radius.circular(24));
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        // 主搜索框区域
        Expanded(
          child: Container(
            height: 40.sp,
            decoration: BoxDecoration(
              border: Border.all(color: AppColors.primaryColor, width: 1),
              borderRadius: borderRadius,
              color: Colors.white,
            ),
            child: Row(
              children: [
                SizedBox(width: 4.sp),
                SvgPicture.asset(
                  'assets/images/common/search.svg',
                  width: 21.sp,
                ),
                SizedBox(width: 4.sp),
                Expanded(
                  child: TextField(
                    controller: _controller,
                    cursorColor: AppColors.primaryColor,
                    decoration: InputDecoration(
                      hintText: I18n.of(context)
                              ?.translate('cm_home.mobileSearchPlaceholder') ??
                          'Palabras clave del producto',
                      hintStyle: TextStyle(
                          color: const Color(0xFFBFBFBF), fontSize: 11.sp),
                      border: InputBorder.none,
                      focusedBorder: InputBorder.none,
                      enabledBorder: InputBorder.none,
                      isDense: true,
                      contentPadding: EdgeInsets.symmetric(vertical: 8),
                    ),
                    style: TextStyle(fontSize: 12.sp, color: Colors.black),
                    onSubmitted: (v) => onKeywordClick(v),
                  ),
                ),
                // 红色按钮，右侧圆角
                SizedBox(
                  width: 55.sp,
                  height: 32.sp,
                  child: ElevatedButton(
                    onPressed: () => onKeywordClick(),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      padding: EdgeInsets.zero,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(20),
                      ),
                      elevation: 0,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_home.search') ?? 'Buscar',
                      style: TextStyle(
                        fontSize: 12.sp,
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
                SizedBox(width: 2.sp),
              ],
            ),
          ),
        ),
        SizedBox(width: 4.sp),
        // 图片搜索按钮，限制最大宽度和字体
        ConstrainedBox(
          constraints: BoxConstraints(
            maxWidth: 90.sp,
            minHeight: 38.sp,
          ),
          child: GestureDetector(
            onTap: widget.onImageSearch ?? onImageSearch,
            child: Container(
              height: 38.sp,
              decoration: BoxDecoration(
                color: AppColors.primaryColor,
                borderRadius: borderRadius,
              ),
              padding: EdgeInsets.only(left: 4.sp),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    'assets/images/common/camera.svg',
                    width: 23.sp,
                  ),
                  SizedBox(width: 2.sp),
                  Flexible(
                    child: Text(
                      I18n.of(context)?.translate('cm_common.imageSearch') ??
                          'Buscar por imagen',
                      softWrap: true,
                      maxLines: 2,
                      style: TextStyle(
                        fontSize: 11.sp,
                        color: Colors.white,
                        height: 12 / 11, // line-height 12.sp
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }
}

// 封装为 SliverPersistentHeaderDelegate
class SearchHeaderDelegate extends SliverPersistentHeaderDelegate {
  final double expandedHeight;
  final Widget child;

  SearchHeaderDelegate({
    required this.expandedHeight,
    required this.child,
  });

  @override
  Widget build(
      BuildContext context, double shrinkOffset, bool overlapsContent) {
    return child;
  }

  @override
  double get maxExtent => expandedHeight;

  @override
  double get minExtent => expandedHeight;

  @override
  bool shouldRebuild(covariant SliverPersistentHeaderDelegate oldDelegate) =>
      false;
}
