import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:chilat2_mall_app/config/config.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/pages/mine/mine_controller.dart';

class LoginController extends GetxController {
  // 获取用户信息
  Map<String, dynamic>? getUserInfo() {
    return LocalStorage().getJSON(USER_INFO);
  }

  // 登录请求
  Future<bool> login(String username, String password, BuildContext context,
      {bool isFromRegister = false}) async {
    try {
      final res = await UserAPI.useLogin({
        "username": username.trim(),
        "password": password.trim(),
      });

      if (res['result']['code'] == 200) {
        // 保存用户信息
        await LocalStorage().setJSON(USER_INFO, res['data']);

        // 更新全局登录状态
        await Global.updateLoginStatus();
        await CookieManager.saveToken(res['data']?['token']);
        await Global.setCartList();

        // 更新用户状态
        if (Get.isRegistered<MineController>()) {
          final mineController = Get.find<MineController>();
          await mineController.loadUserInfo();
        }
        showSuccessMessage(
            I18n.of(context)!.translate("cm_login.loginSuccess", "登录成功"));
        // 登录成功返回true
        return true;
      } else {
        showErrorMessage(res['result']['message'] ??
            I18n.of(context)!.translate("cm_login.loginFailed", "登录失败"));
        return false;
      }
    } catch (e) {
      showErrorMessage(
          I18n.of(context)!.translate("cm_login.loginFailed", "登录失败"));
      return false;
    }
  }

  // 注册请求
  Future<bool> register(String username, String password, BuildContext context,
      {bool isFromRegister = false}) async {
    try {
      final res = await UserAPI.useRegister({
        "username": username.trim(),
        "password": password.trim(),
      });

      if (res['result']['code'] == 200) {
        showSuccessMessage(I18n.of(context)!
            .translate("cm_login.registrationSuccess", "注册成功"));
        // 注册成功后自动登录
        return await login(username, password, context, isFromRegister: true);
      } else {
        showErrorMessage(res['result']['message'] ??
            I18n.of(context)!.translate("cm_login.registrationFailed", "注册失败"));
        return false;
      }
    } catch (e) {
      showErrorMessage(
          I18n.of(context)!.translate("cm_login.registrationFailed", "注册失败"));
      return false;
    }
  }

  // 修改密码请求
  Future<bool> modifyPassword(String email, String password, String captcha,
      BuildContext context) async {
    try {
      final res = await UserAPI.useModifyPassword({
        "email": email.trim(),
        "password": password.trim(),
        "captcha": captcha.trim(),
      });

      if (res['result']['code'] == 200) {
        showSuccessMessage(I18n.of(context)!
            .translate("cm_login.pwdChangedSuccess", "密码修改成功"));
        // 修改密码成功后自动登录
        return await login(email, password, context);
      } else {
        showErrorMessage(res['result']['message'] ??
            I18n.of(context)!.translate("cm_login.pwdChangedFailed", "密码修改失败"));
        return false;
      }
    } catch (e) {
      showErrorMessage(
          I18n.of(context)!.translate("cm_login.pwdChangedFailed", "密码修改失败"));
      return false;
    }
  }

  // 成功提示
  void showSuccessMessage(String message) {
    Get.snackbar(
      'El éxito',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.white,
      colorText: Colors.black,
      duration: const Duration(seconds: 2),
    );
  }

  // 错误提示
  void showErrorMessage(String message) {
    Get.snackbar(
      'error',
      message,
      snackPosition: SnackPosition.TOP,
      backgroundColor: Colors.red,
      colorText: Colors.white,
    );
  }
}
