import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:flutter/material.dart';

/// appbar 返回按钮类型
enum AppBarBackType { Back, Close, None }

const double kNavigationBarHeight = 44.0;

// 自定义 AppBar
class MyAppBar extends AppBar implements PreferredSizeWidget {
  MyAppBar(
      {super.key,
      super.title,
      AppBarBackType? leadingType,
      WillPopCallback? onWillPop,
      Widget? leading,
      Brightness? brightness,
      Color? backgroundColor,
      super.actions,
      bool super.centerTitle = true,
      double? elevation})
      : super(
          backgroundColor: backgroundColor ?? Color(0xfffefefe),
          leading: leading ??
              (leadingType == AppBarBackType.None
                  ? Container()
                  : AppBarBack(
                      leadingType ?? AppBarBackType.Back,
                      onWillPop: onWillPop,
                    )),
          elevation: elevation ?? 0.5,
        );
  @override
  get preferredSize => Size.fromHeight(44);
}

// 自定义返回按钮
class AppBarBack extends StatelessWidget {
  final AppBarBackType _backType;
  final Color? color;
  final WillPopCallback? onWillPop;

  const AppBarBack(this._backType, {super.key, this.onWillPop, this.color});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () async {
        final willBack = onWillPop == null ? true : await onWillPop!();
        if (!willBack) return;
        Navigator.pop(context);
      },
      child: _backType == AppBarBackType.Close
          ? Icon(Icons.close, color: color ?? Color(0xFF222222), size: 24.0)
          : Container(
              padding: EdgeInsets.only(right: 15),
              child: Image.asset(
                'assets/images/nav/nav_back.png',
                color: color,
              ),
            ),
    );
  }
}

class MyTitle extends StatelessWidget {
  final String _title;
  final Color? color;

  const MyTitle(this._title, {super.key, this.color});

  @override
  Widget build(BuildContext context) {
    return Text(_title,
        style: TextStyle(
            color: color ?? AppColors.primaryText,
            fontSize: 16,
            fontWeight: FontWeight.w500));
  }
}
