import 'dart:async';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/utils/navigator_util.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/services/order.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

class OrderPaymentResultPage extends StatefulWidget {
  @override
  _OrderPaymentResultPageState createState() => _OrderPaymentResultPageState();
}

class _OrderPaymentResultPageState extends State<OrderPaymentResultPage> {
  String orderNo = '';
  String paymentId = '';
  Map payResult = {};
  String mallOrderStatus = '';
  Timer? _timer;
  Timer? _payingTimer;
  bool _isLoading = true; // 新增：加载中/未知状态标识
  final RefreshController _refreshController =
      RefreshController(initialRefresh: false);

  @override
  void initState() {
    super.initState();
    orderNo = Get.parameters['orderNo'] ?? '';
    paymentId = Get.parameters['paymentId'] ?? '';
    _isLoading = true; // 仅首次进入时设置 loading
    _getQueryPayResult();
  }

  @override
  void dispose() {
    _timer?.cancel();
    _payingTimer?.cancel();
    _refreshController.dispose();
    super.dispose();
  }

  Future<void> _getQueryPayResult({bool isManualRefresh = false}) async {
    // 如果是手动刷新，先取消已有的定时器
    if (isManualRefresh && _timer != null) {
      _timer?.cancel();
      _timer = null;
    }

    try {
      final res = await OrderService.useQueryPayResult({
        'orderNo': orderNo,
        'paymentId': paymentId,
      });
      if (res['result']['code'] == 200) {
        setState(() {
          payResult = res['data']['payResult'] ?? {};
          mallOrderStatus = res['data']['mallOrderStatus'] ?? '';
          _isLoading = false; // 查询成功，结束加载
        });
        // 未支付，跳转收银台
        if (payResult['payStatus'] == 'INIT') {
          Get.offNamed(AppRoutes.OrderPayment, parameters: {
            'orderNo': orderNo,
            'paymentId': paymentId,
          });
          return;
        }
        // 支付中，定时查询
        if (payResult['payStatus'] == 'PAYING') {
          if (_timer == null) {
            _timer = Timer.periodic(const Duration(seconds: 3), (_) {
              _getQueryPayResult();
            });
          }
        } else {
          // 非支付中，停止定时
          _timer?.cancel();
          _timer = null;
        }
      } else {
        setState(() {
          _isLoading = false;
        });
        Get.snackbar('错误', res['result']['message'] ?? '未知错误',
            snackPosition: SnackPosition.TOP);
      }
    } catch (e) {
      Get.snackbar('错误', e.toString(), snackPosition: SnackPosition.TOP);
      setState(() {
        _isLoading = false;
      });
    } finally {
      // 更新刷新控制器状态
      _refreshController.refreshCompleted();
    }
  }

  void _goHome() {
    // 清空所有页面栈，直接回到首页
    NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
  }

  void _goOrderDetails() {
    // 清空导航栈并跳转到订单详情页，避免导航栈中出现多个订单详情页
    bool hasOrderDetail = false;

    // 判断是否存在订单详情页
    Get.until((route) {
      if (route.settings.name == AppRoutes.OrderDetail) {
        hasOrderDetail = true;
        return true;
      }
      return false;
    });

    if (!hasOrderDetail) {
      // 如果不存在，则直接跳转（替换当前页面）
      Get.offNamed(AppRoutes.OrderDetail);
    }
  }

  void _goOrderPay() {
    // 使用Get.toNamed而不是offNamed，保留导航历史，使收银台可以返回
    Get.toNamed(AppRoutes.OrderPayment, parameters: {
      'orderNo': orderNo,
      'paymentId': paymentId,
    });
  }

  @override
  Widget build(BuildContext context) {
    final i18n = I18n.of(context);

    return AppScaffold(
      backgroundColor: Colors.white,
      onPopInvoked: (result) {
        Get.offNamed(AppRoutes.OrderDetail, arguments: {'orderNo': orderNo});
      },
      body: Column(
        children: [
          const SearchHeader(
            showCart: true,
            showBackIcon: true,
          ),
          Expanded(
            child: SmartRefresher(
              controller: _refreshController,
              enablePullDown: true,
              enablePullUp: false,
              header: const WaterDropMaterialHeader(),
              onRefresh: () async {
                await _getQueryPayResult(isManualRefresh: true);
              },
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.fromLTRB(
                    30.sp, // left
                    60.sp, // top
                    30.sp, // right
                    16.sp, // bottom
                  ),
                  child: _isLoading
                      ? Center(
                          child: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              SizedBox(height: 100.sp),
                              CircularProgressIndicator(),
                              SizedBox(height: 24.sp),
                            ],
                          ),
                        )
                      : _buildResultContent(i18n),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResultContent(I18n? i18n) {
    final payStatus = payResult['payStatus'];
    final errorMsg = payResult['errorMsg'] ?? '';
    // 支付成功
    if (payStatus == 'PAID_SUCCESS') {
      if (mallOrderStatus == 'MALL_WAIT_PAY_PRODUCT' ||
          mallOrderStatus == 'MALL_WAIT_PAY_ALL_FEE') {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/images/order/pay-success.svg',
              width: 50.sp,
              height: 50.sp,
            ),
            SizedBox(height: 12.sp),
            Text(i18n?.translate('cm_order.paySuccess') ?? '支付成功',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 26.sp)),
            SizedBox(height: 12.sp),
            Text(
              '${i18n?.translate('cm_order.payOrderNo') ?? '订单号'} $orderNo, '
              '${mallOrderStatus == 'MALL_WAIT_PAY_PRODUCT' ? i18n?.translate('cm_order.payProductCost') ?? '产品成本支付成功' : i18n?.translate('cm_order.payOrderCost') ?? '订单费用支付成功'}',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600], fontSize: 14.sp),
            ),
            SizedBox(height: 10.sp),
            Divider(thickness: 1, color: Colors.grey[300], height: 32.sp),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  foregroundColor: Colors.white,
                  minimumSize: Size(double.infinity, 40.sp),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32.sp)),
                ),
                onPressed: _goHome,
                child: Text(i18n?.translate('cm_order.keepShopping') ?? '继续购物',
                    style: TextStyle(fontSize: 16.sp)),
              ),
            ),
            SizedBox(height: 8.sp),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey[300]!),
                  foregroundColor: Colors.grey[600],
                  minimumSize: Size(double.infinity, 40.sp),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32.sp)),
                ),
                onPressed: _goOrderDetails,
                child: Text(
                    i18n?.translate('cm_order.viewOrderDetails') ?? '查看订单详情',
                    style: TextStyle(fontSize: 16.sp)),
              ),
            ),
            SizedBox(height: 16.sp),
          ],
        );
      } else if (mallOrderStatus == 'MALL_WAIT_PAY_INTER_FEE') {
        return Column(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            SvgPicture.asset(
              'assets/images/order/pay-success.svg',
              width: 50.sp,
              height: 50.sp,
            ),
            SizedBox(height: 12.sp),
            Text(i18n?.translate('cm_order.paySuccess') ?? '支付成功',
                style: TextStyle(fontWeight: FontWeight.w600, fontSize: 26.sp)),
            SizedBox(height: 12.sp),
            Text(
              '${i18n?.translate('cm_order.payOrderNo') ?? '订单号'} $orderNo, '
              '${i18n?.translate('cm_order.patientlyTransportation') ?? '请耐心等待运输费用确认'}',
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600], fontSize: 14.sp),
            ),
            SizedBox(height: 10.sp),
            Divider(thickness: 1, color: Colors.grey[300], height: 32.sp),
            SizedBox(
              width: double.infinity,
              child: OutlinedButton(
                style: OutlinedButton.styleFrom(
                  side: BorderSide(color: Colors.grey[300]!),
                  foregroundColor: Colors.grey[600],
                  minimumSize: Size(double.infinity, 40.sp),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(32.sp)),
                ),
                onPressed: _goOrderDetails,
                child: Text(
                    i18n?.translate('cm_order.viewOrderDetails') ?? '查看订单详情',
                    style: TextStyle(fontSize: 16.sp)),
              ),
            ),
            SizedBox(height: 16.sp),
          ],
        );
      }
    } else if (payStatus == 'PAYING') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          CircularProgressIndicator(
            color: AppColors.primaryColor,
            strokeWidth: 4.sp,
          ),
          SizedBox(height: 32.sp),
          Text(
            i18n?.translate('cm_order.payPending') ??
                'Se están comprobando los resultados de los pagos...',
            style: TextStyle(fontWeight: FontWeight.w500, fontSize: 20.sp),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 12.sp),
          Divider(thickness: 1, color: Colors.grey[300], height: 32.sp),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: Size(double.infinity, 40.sp),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32.sp)),
              ),
              onPressed: _goOrderPay,
              child: Text(
                  i18n?.translate('cm_order.orderPayAgain') ??
                      'Intenta de nuevo',
                  style: TextStyle(fontSize: 16.sp)),
            ),
          ),
          SizedBox(height: 8.sp),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.grey[300]!),
                foregroundColor: Colors.grey[600],
                minimumSize: Size(double.infinity, 40.sp),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32.sp)),
              ),
              onPressed: _goOrderDetails,
              child: Text(
                  i18n?.translate('cm_order.viewOrderDetails') ??
                      'Ver detalles del pedido',
                  style: TextStyle(fontSize: 16.sp)),
            ),
          ),
          SizedBox(height: 16.sp),
        ],
      );
    } else if (payStatus == 'PAID_FAIL') {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            'assets/images/order/pay-error.svg',
            width: 50.sp,
            height: 50.sp,
          ),
          SizedBox(height: 12.sp),
          Text(i18n?.translate('cm_order.payFailure') ?? '支付失败',
              style: TextStyle(fontWeight: FontWeight.w600, fontSize: 26.sp)),
          SizedBox(height: 12.sp),
          Text(errorMsg,
              textAlign: TextAlign.center,
              style: TextStyle(color: Colors.grey[600], fontSize: 14.sp)),
          SizedBox(height: 12.sp),
          Divider(thickness: 1, color: Colors.grey[300], height: 32.sp),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              style: ElevatedButton.styleFrom(
                backgroundColor: AppColors.primaryColor,
                foregroundColor: Colors.white,
                minimumSize: Size(double.infinity, 40.sp),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32.sp)),
              ),
              onPressed: _goOrderPay,
              child: Text(i18n?.translate('cm_order.orderPayAgain') ?? '重新支付',
                  style: TextStyle(fontSize: 16.sp)),
            ),
          ),
          SizedBox(height: 8.sp),
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: Colors.grey[300]!),
                foregroundColor: Colors.grey[600],
                minimumSize: Size(double.infinity, 40.sp),
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(32.sp)),
              ),
              onPressed: _goOrderDetails,
              child: Text(
                  i18n?.translate('cm_order.viewOrderDetails') ?? '查看订单详情',
                  style: TextStyle(fontSize: 16.sp)),
            ),
          ),
          SizedBox(height: 16.sp),
        ],
      );
    }
    // 其它情况
    return Center(child: Text('未知支付状态', style: TextStyle(color: Colors.grey)));
  }
}
