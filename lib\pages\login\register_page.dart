import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/pages/login/terms_page.dart';
import 'package:chilat2_mall_app/pages/login/login_page.dart';
import 'package:chilat2_mall_app/pages/login/login_controller.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';

class RegisterPage extends StatefulWidget {
  final VoidCallback? onRegisterSuccess;
  final VoidCallback? onGoLogin;
  final VoidCallback? onOpenAgree;

  const RegisterPage({
    Key? key,
    this.onRegisterSuccess,
    this.onGoLogin,
    this.onOpenAgree,
  }) : super(key: key);

  @override
  _RegisterPageState createState() => _RegisterPageState();
}

class _RegisterPageState extends State<RegisterPage> {
  final _formKey = GlobalKey<FormState>();
  final _unameController = TextEditingController();
  final _pwdController = TextEditingController();
  bool _showPassword = false;
  bool acceptTerms = false;
  final LoginController loginController = Get.put(LoginController());

  @override
  void initState() {
    super.initState();
    _unameController.addListener(() {
      setState(() {});
    });
    _pwdController.addListener(() {
      setState(() {});
    });
  }

  @override
  void dispose() {
    _unameController.dispose();
    _pwdController.dispose();
    super.dispose();
  }

  void _registerAction(BuildContext context) async {
    if (_formKey.currentState!.validate()) {
      if (!acceptTerms) {
        loginController.showErrorMessage('请阅读并同意用户协议和隐私政策');
        return;
      }

      try {
        bool success = await loginController.register(
          _unameController.text,
          _pwdController.text,
          context,
          isFromRegister: true,
        );

        if (success) {
          if (widget.onRegisterSuccess != null) {
            // 如果设置了注册成功回调，则调用
            widget.onRegisterSuccess!();
          } else {
            // 注册成功后跳转到注册成功页面
            Get.offNamed(AppRoutes.RegisterSuccess);
          }
        }
      } catch (e) {
        loginController.showErrorMessage('注册出错: $e');
      }
    }
  }

  void _onLogin() {
    if (widget.onGoLogin != null) {
      // 如果设置了回调，直接调用
      widget.onGoLogin!();
    } else {
      // 获取原始参数并传递
      final arguments = Get.arguments as Map<String, dynamic>?;
      final redirectRoute = arguments?['redirectRoute'];
      Get.toNamed(AppRoutes.LoginPage,
          arguments: {'redirectRoute': redirectRoute});
    }
  }

  void _onOpenAgree() {
    if (widget.onOpenAgree != null) {
      // 如果设置了回调，直接调用
      widget.onOpenAgree!();
    } else {
      Get.toNamed(AppRoutes.Terms);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      showWhatsAppButton: false,
      appBar: MyAppBar(
        leadingType: AppBarBackType.None,
        backgroundColor: Colors.white,
        elevation: 0,
        title: GestureDetector(
          onTap: () {
            NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
          },
          child: const Padding(
            padding: EdgeInsets.symmetric(vertical: 8.0),
            child: Image(
              image: AssetImage("assets/images/logo.png"),
              width: 150,
              fit: BoxFit.fill,
            ),
          ),
        ),
        centerTitle: true,
      ),
      body: SafeArea(
        child: GestureDetector(
          behavior: HitTestBehavior.translucent,
          onTap: () => FocusScope.of(context).requestFocus(FocusNode()),
          child: LayoutBuilder(
            builder: (context, constraints) {
              return SingleChildScrollView(
                child: ConstrainedBox(
                  constraints: BoxConstraints(
                    minHeight: constraints.maxHeight,
                  ),
                  child: IntrinsicHeight(
                    child: Column(
                      children: [
                        Expanded(
                          child: Container(
                            decoration: const BoxDecoration(
                              color: Color(0xFF8F0000),
                              image: DecorationImage(
                                image: AssetImage("assets/images/login/bg.jpg"),
                                fit: BoxFit.contain,
                                alignment: Alignment.topCenter,
                              ),
                            ),
                            padding: EdgeInsets.symmetric(
                                vertical: 0.0, horizontal: 18.0.sp),
                            child: Align(
                              alignment: Alignment.topCenter,
                              child: Container(
                                margin: EdgeInsets.only(
                                  top: 150.sp,
                                ),
                                decoration: BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.circular(10),
                                  boxShadow: const [
                                    BoxShadow(
                                      color: Colors.black12,
                                      blurRadius: 10,
                                      offset: Offset(0, 5),
                                    ),
                                  ],
                                ),
                                padding: EdgeInsets.all(18.0.sp),
                                child: IntrinsicHeight(
                                  child: Form(
                                    autovalidateMode: AutovalidateMode.disabled,
                                    key: _formKey,
                                    child: Column(
                                      mainAxisSize: MainAxisSize.min,
                                      children: <Widget>[
                                        Text(
                                          I18n.of(context)?.translate(
                                                  'cm_common_register') ??
                                              '',
                                          style: TextStyle(
                                              fontSize: 24.sp,
                                              fontWeight: FontWeight.w500),
                                        ),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        _buildUsernameInput(),
                                        SizedBox(
                                          height: 20.h,
                                        ),
                                        _buildPasswordInput(),
                                        SizedBox(
                                          height: 16.h,
                                        ),
                                        Row(
                                          children: <Widget>[
                                            Checkbox(
                                              value: acceptTerms,
                                              onChanged: (value) {
                                                setState(() {
                                                  acceptTerms = value!;
                                                });
                                              },
                                              materialTapTargetSize:
                                                  MaterialTapTargetSize
                                                      .shrinkWrap,
                                              visualDensity:
                                                  VisualDensity.compact,
                                            ),
                                            Flexible(
                                              child: GestureDetector(
                                                onTap: _onOpenAgree,
                                                child: Text.rich(
                                                  TextSpan(
                                                    text: I18n.of(context)
                                                        ?.translate(
                                                            'cm_common.readAgree'),
                                                    children: [
                                                      WidgetSpan(
                                                          child: SizedBox(
                                                              width: 4.0)),
                                                      TextSpan(
                                                        text: I18n.of(context)
                                                            ?.translate(
                                                                'cm_common.viewAgree'),
                                                        style: TextStyle(
                                                            color: Color(
                                                                0xFF034AA6)),
                                                      ),
                                                    ],
                                                  ),
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(height: 36.h),
                                        SizedBox(
                                          width: double.infinity,
                                          height: 48.sp,
                                          child: ElevatedButton(
                                            onPressed: () =>
                                                _registerAction(context),
                                            style: ElevatedButton.styleFrom(
                                              backgroundColor:
                                                  AppColors.primaryColor,
                                              foregroundColor: Colors.white,
                                              shape: RoundedRectangleBorder(
                                                borderRadius:
                                                    BorderRadius.circular(10.0),
                                              ),
                                              elevation: 0,
                                            ),
                                            child: Text(
                                              I18n.of(context)?.translate(
                                                      'cm_common.regLogin') ??
                                                  '',
                                              style: TextStyle(
                                                fontSize: 16.sp,
                                                fontWeight: FontWeight.w400,
                                              ),
                                            ),
                                          ),
                                        ),
                                        SizedBox(height: 20.h),
                                        Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.end,
                                          children: [
                                            GestureDetector(
                                              onTap: _onLogin,
                                              child: Text(
                                                I18n.of(context)?.translate(
                                                        'cm_common_login') ??
                                                    '',
                                                style: TextStyle(
                                                  color: Color(0xFF034AA6),
                                                  fontSize: 16.sp,
                                                  height: 1.4,
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ],
                                    ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildUsernameInput() {
    return TextFormField(
      controller: _unameController,
      keyboardType: TextInputType.emailAddress,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.username'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 14.sp,
        ),
        suffixIcon: _unameController.text.isNotEmpty
            ? GestureDetector(
                onTap: () => _unameController.clear(),
                child: Icon(Icons.cancel, size: 18.w),
              )
            : null,
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputEmailTips'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp,
            top: 4.0.sp,
            right: 4.0.sp,
            bottom: 4.0.sp,
          ),
          child: SvgPicture.asset(
            'assets/images/login/email.svg',
            width: 20.sp,
            height: 20.sp,
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp,
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp),
        errorMaxLines: 3,
      ),
      cursorColor: AppColors.primaryColor,
      validator: (v) {
        String t = v ?? '';
        final pattern = RegExp(r'^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$');
        if (t.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputEmailTips');
        } else if (!pattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.emailTips');
        }
        return null;
      },
    );
  }

  Widget _buildPasswordInput() {
    return TextFormField(
      controller: _pwdController,
      keyboardType: TextInputType.visiblePassword,
      decoration: InputDecoration(
        labelText: I18n.of(context)?.translate('cm_common.password'),
        labelStyle: TextStyle(fontSize: 16.sp),
        floatingLabelStyle: TextStyle(
          color: AppColors.primaryColor,
          fontSize: 14.sp,
        ),
        suffixIcon: GestureDetector(
          onTap: () {
            setState(() {
              _showPassword = !_showPassword;
            });
          },
          child: Icon(
            _showPassword ? Icons.visibility : Icons.visibility_off,
            size: 18.w,
          ),
        ),
        suffixIconConstraints: BoxConstraints(
          minWidth: 32.sp,
          minHeight: 32.sp,
        ),
        contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(5),
          borderSide: BorderSide.none,
        ),
        filled: true,
        fillColor: const Color.fromRGBO(245, 247, 247, 1),
        hintText: I18n.of(context)?.translate('cm_common.inputPassword'),
        hintStyle: TextStyle(fontSize: 13.sp, color: Color(0xFFA8A8A8)),
        prefixIcon: Padding(
          padding: EdgeInsets.only(
            left: 6.0.sp,
            top: 4.0.sp,
            right: 4.0.sp,
            bottom: 4.0.sp,
          ),
          child: SvgPicture.asset(
            'assets/images/login/password.svg',
            width: 20.sp,
            height: 20.sp,
          ),
        ),
        prefixIconConstraints: BoxConstraints(
          minWidth: 32.sp,
          minHeight: 32.sp,
        ),
        errorStyle: TextStyle(fontSize: 14.sp),
        errorMaxLines: 3,
      ),
      obscureText: !_showPassword,
      cursorColor: AppColors.primaryColor,
      validator: (v) {
        String t = v ?? '';
        final specialCharPattern = RegExp(r'[^A-Za-z\d]');
        final pattern = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$');
        if (t.isEmpty) {
          return I18n.of(context)?.translate('cm_common.inputPwdTips');
        } else if (specialCharPattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.pwdFormatTips');
        } else if (!pattern.hasMatch(t)) {
          return I18n.of(context)?.translate('cm_common.pwdLengthTips');
        }
        return null;
      },
    );
  }
}
