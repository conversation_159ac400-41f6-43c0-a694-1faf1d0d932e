import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_search_bar.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';

class AboutUsPage extends StatefulWidget {
  const AboutUsPage({super.key});

  @override
  State<AboutUsPage> createState() => _AboutUsPageState();
}

class _AboutUsPageState extends State<AboutUsPage> {
  double screenWidth = 0.0;
  final bool _isLoading = false;
  bool _showBackToTopButton = false;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(() {
      setState(() {
        _showBackToTopButton = _scrollController.offset > 200;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onScrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.grey.shade100,
            body: Stack(
              children: [
                SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                      mainAxisAlignment: MainAxisAlignment.start,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        SearchHeader(showHomeIcon: true),
                        Container(
                          width: screenWidth,
                          margin: EdgeInsets.only(top: 6),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.red.shade200,
                                Colors.grey.shade200,
                              ],
                            ),
                          ),
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.symmetric(
                                    horizontal: 32, vertical: 18),
                                child: Text(
                                  'Haga la importación más fácil y seguro',
                                  style: TextStyle(
                                      color: Colors.black,
                                      fontSize: 20,
                                      fontWeight: FontWeight.w600),
                                ),
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(horizontal: 24),
                                child: Text(
                                  "Chilat shop es la plataforma líder de agente de compras internacional. Chilat shop es la plataforma de importación todo en uno donde gestionamos por ti todo el proceso de compra, logística e importación.",
                                  style: TextStyle(
                                    color: Colors.black,
                                  ),
                                ),
                              ),
                              Center(
                                  child: GestureDetector(
                                onTap: () {
                                  print('按钮被点击');
                                },
                                child: Container(
                                  width: 160,
                                  margin: EdgeInsets.symmetric(vertical: 24),
                                  padding: EdgeInsets.symmetric(vertical: 6),
                                  decoration: BoxDecoration(
                                    color: AppColors.primaryColor, // 设置背景色为红色
                                    borderRadius:
                                        BorderRadius.circular(20), // 设置圆角半径为 20
                                  ),
                                  child: Center(
                                    child: Text(
                                      I18n.of(context)!
                                          .translate('cm_common_registerNow'),
                                      style: TextStyle(
                                        color: Colors.white,
                                      ),
                                    ),
                                  ),
                                ),
                              )),
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 16, vertical: 24),
                          child: Column(
                            children: [
                              Text(
                                "Disfrute de una forma diferente de importar",
                                style: TextStyle(
                                    fontSize: 16, fontWeight: FontWeight.w600),
                              ),
                              Container(
                                padding: EdgeInsets.only(top: 18),
                                child: Text(
                                    "Chilat es un líder en servicios del idioma español en China y llevamos 22 años enfocado en servicios de agente de compras en Latinoamérica.",
                                    style: TextStyle(height: 1.5)),
                              ),
                              Container(
                                padding: EdgeInsets.only(top: 18),
                                child: Text(
                                    "Estamos comprometidos a permitir que más clientes comerciales compren directamente a proveedores de primera mano en China a través de métodos de importación más sencillos y convenientes, obteniendo así precios de compra más competitivos!",
                                    style: TextStyle(height: 1.5)),
                              ),
                              Container(
                                padding: EdgeInsets.only(top: 18),
                                child: Text(
                                    "Nuestra sede central se encuentra en Yiwu, China, y contamos con sucursales en México, Shanghai, Guangzhou y Hangzhou, con una superficie operativa de oficinas de más de 2000 metros cuadrados, una superficie de almacén de 1500 metros cuadrados para nuestro grupo de empresas y más de 100 empleados.",
                                    style: TextStyle(height: 1.5)),
                              ),
                              Container(
                                padding: EdgeInsets.only(top: 18),
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.start,
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      width: 1.5,
                                      height: 60,
                                      color: Colors.grey.shade300,
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 2, left: 8),
                                      child: Column(
                                        children: [
                                          Text(
                                            "+2,000m²",
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: AppColors.primaryText),
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(top: 12),
                                            child: Text(
                                              "de oficinas",
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors
                                                      .primaryGreyText),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 1.5,
                                      height: 60,
                                      color: Colors.grey.shade300,
                                      margin: EdgeInsets.only(left: 18),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 2, left: 8),
                                      child: Column(
                                        children: [
                                          Text(
                                            "+1,500m²",
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: AppColors.primaryText),
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(top: 12),
                                            child: Text(
                                              "almacenes",
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors
                                                      .primaryGreyText),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                    Container(
                                      width: 1.5,
                                      height: 60,
                                      color: Colors.grey.shade300,
                                      margin: EdgeInsets.only(left: 18),
                                    ),
                                    Container(
                                      padding: EdgeInsets.only(top: 2, left: 8),
                                      child: Column(
                                        children: [
                                          Text(
                                            "+100",
                                            style: TextStyle(
                                                fontSize: 16,
                                                fontWeight: FontWeight.w600,
                                                color: AppColors.primaryText),
                                          ),
                                          Container(
                                            padding: EdgeInsets.only(top: 12),
                                            child: Text(
                                              "empleados",
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  fontWeight: FontWeight.w500,
                                                  color: AppColors
                                                      .primaryGreyText),
                                            ),
                                          )
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          ),
                        ),
                        Container(
                          padding: EdgeInsets.symmetric(vertical: 12),
                          child: CachedNetworkImage(
                              height: 528,
                              width: screenWidth,
                              fit: BoxFit.cover,
                              imageUrl:
                                  "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/07/ddac5ec3-bf81-4671-9550-fb8e427b6bc6.png"),
                        ),
                        Container(
                          padding:
                              EdgeInsets.only(top: 18, left: 12, right: 12),
                          color: Colors.grey.shade100,
                          child: Column(
                            children: [
                              Text(
                                  "Nuestro negocio se extiende por toda América Latina, y nuestra visión es convertirnos en el líder de las marcas chinas que salen al exterior en América Latina.",
                                  style: TextStyle(
                                      fontSize: 16,
                                      fontWeight: FontWeight.w600)),
                              Container(
                                padding: EdgeInsets.only(top: 18),
                                child: Text(
                                    "Nuestra misión es ayudar a las marcas chinas a liderar el comercio latinoamericano. Ser reconocida en Latinoamérica como la empresa internacional líder que genera las mejores oportunidades de negocios con China basándose en el respeto por el cliente, la transparencia y honestidad en todo acuerdo comercial, agilidad, innovación y profesionalismo en los servicios que ofrece y por su calidez humana y relaciones de confianza a largo plazo."),
                              ),
                              Container(
                                padding: EdgeInsets.symmetric(vertical: 12),
                                child: CachedNetworkImage(
                                    fit: BoxFit.cover,
                                    width: screenWidth,
                                    imageUrl:
                                        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/06/3b696a30-86fe-42ea-b608-c19f243950f5.png"),
                              )
                            ],
                          ),
                        ),
                        MyFooter()
                      ]),
                ),
                if (_showBackToTopButton)
                  Positioned(
                    bottom: 20,
                    right: 20,
                    child: FloatingActionButton(
                      onPressed: onScrollToTop,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(64),
                      ),
                      child: const Icon(Icons.arrow_upward),
                    ),
                  ),
              ],
            ),
          );
  }
}
