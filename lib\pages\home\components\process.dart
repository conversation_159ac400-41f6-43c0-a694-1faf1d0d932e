import 'package:chilat2_mall_app/pages/home/<USER>';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:flutter/material.dart';

class ProcessCard extends StatefulWidget {
  const ProcessCard({super.key});

  @override
  State<ProcessCard> createState() => _ProcessCardState();
}

class _ProcessCardState extends State<ProcessCard> {
  int selectedStepIndex = 0;
  List<double> timelineHeights = [57, 30, 30, 30];

  List<String> contents = [];

  List<ProcessItem> processItems(BuildContext context) => [
        ProcessItem(
          icon: Icons.fact_check,
          iconActive: "",
          title: I18n.of(context)!.translate("cm_guestHome.addGoods"),
          contents: [
            I18n.of(context)?.translate("cm_guestHome.addGoods") as String,
            I18n.of(context)?.translate("cm_guestHome.orderGoods") as String,
          ],
        ),
        ProcessItem(
          icon: Icons.request_quote,
          iconActive: "",
          title: I18n.of(context)!.translate("cm_guestHome.confirmPrice"),
          contents: [
            I18n.of(context)?.translate("cm_guestHome.countPrice") as String,
            I18n.of(context)?.translate("cm_guestHome.predictPrice") as String,
            I18n.of(context)?.translate("cm_guestHome.payPrice") as String,
          ],
        ),
        ProcessItem(
          icon: Icons.add_box_sharp,
          iconActive: "",
          title: I18n.of(context)!.translate("cm_guestHome.payProduct"),
          contents: [
            I18n.of(context)?.translate("cm_guestHome.transProduct") as String,
            I18n.of(context)?.translate("cm_guestHome.checkProduct") as String,
            I18n.of(context)?.translate("cm_guestHome.storageProduct")
                as String,
          ],
        ),
        ProcessItem(
          icon: Icons.directions_boat,
          iconActive: "",
          title: I18n.of(context)!.translate("cm_guestHome.interLogistics"),
          contents: [
            I18n.of(context)?.translate("cm_guestHome.chooseLogistics")
                as String,
            I18n.of(context)?.translate("cm_guestHome.trackLogistics")
                as String,
            I18n.of(context)?.translate("cm_guestHome.confirmLogistics")
                as String,
          ],
        ),
      ];

  @override
  void initState() {
    super.initState();
  }

  void calculateContainerHeight(int index) {
    if (selectedStepIndex == 0) {
      setState(() {
        timelineHeights[0] = 57;
        timelineHeights[1] = 26;
        timelineHeights[2] = 26;
        timelineHeights[3] = 26;
      });
    } else if (selectedStepIndex == 1) {
      setState(() {
        timelineHeights[0] = 26;
        timelineHeights[1] = 98;
        timelineHeights[2] = 26;
        timelineHeights[3] = 26;
      });
    } else if (selectedStepIndex == 2) {
      setState(() {
        timelineHeights[0] = 26;
        timelineHeights[1] = 26;
        timelineHeights[2] = 84;
        timelineHeights[3] = 26;
      });
    } else {
      setState(() {
        timelineHeights[0] = 26;
        timelineHeights[1] = 26;
        timelineHeights[2] = 26;
        timelineHeights[3] = 60;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Expanded(
          flex: 1,
          child: Container(
            margin: EdgeInsets.symmetric(vertical: 10),
            child: ListView.builder(
                shrinkWrap: true,
                itemCount: processItems(context).length,
                itemBuilder: (BuildContext context, int index) {
                  return Column(children: [
                    GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedStepIndex = index;
                            contents.clear();
                            calculateContainerHeight(index);
                            for (var item
                                in processItems(context)[index].contents) {
                              contents.add(item);
                            }
                          });
                        },
                        child: Container(
                          width: 6.0,
                          height: 6.0,
                          decoration: BoxDecoration(
                            shape: BoxShape.circle,
                            color: selectedStepIndex == index
                                ? Colors.red
                                : Colors.white,
                            border: Border.all(
                              color: Colors.red,
                              width: 1.0,
                            ),
                          ),
                        )),
                    if (index != processItems(context).length - 1)
                      Container(
                        width: 1.0,
                        height: timelineHeights[index],
                        color: Colors.grey[200],
                      ),
                  ]);
                }),
          ),
        ),
        Expanded(
          flex: 8,
          child: ListView.builder(
              shrinkWrap: true,
              itemCount: processItems(context).length,
              itemBuilder: (BuildContext context, int index) {
                return Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    GestureDetector(
                        onTap: () {
                          setState(() {
                            selectedStepIndex = index;
                            contents.clear();
                            calculateContainerHeight(index);
                            for (var item
                                in processItems(context)[index].contents) {
                              contents.add(item);
                            }
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.only(right: 32),
                          padding: const EdgeInsets.symmetric(
                              horizontal: 12, vertical: 3),
                          decoration: BoxDecoration(
                            color: selectedStepIndex == index
                                ? Colors.red
                                : Colors.white,
                            border: Border.all(
                                color: selectedStepIndex == index
                                    ? Colors.red
                                    : Colors.grey.shade200),
                            borderRadius: BorderRadius.circular(16),
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // 左侧内容，这里用一个图标示例
                              Icon(
                                processItems(context)[index].icon,
                                color: selectedStepIndex == index
                                    ? Colors.white
                                    : Colors.red,
                                size: 18,
                              ),
                              const SizedBox(width: 2),
                              // 右侧内容，这里用文本示例
                              Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      processItems(context)[index].title,
                                      style: TextStyle(
                                          fontSize: 12,
                                          color: selectedStepIndex == index
                                              ? Colors.white
                                              : Colors.grey.shade800),
                                    )
                                  ],
                                ),
                              ),
                            ],
                          ),
                        )),
                    Visibility(
                      visible: selectedStepIndex == index,
                      child: Container(
                        padding: EdgeInsets.only(left: 12, bottom: 2, top: 4),
                        width: MediaQuery.of(context).size.width * 0.75,
                        color: Colors.white,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: contents.isNotEmpty
                                ? contents
                                    .map((line) => Text(line,
                                        style: TextStyle(
                                            fontSize: 10,
                                            color: Colors.grey.shade600)))
                                    .toList()
                                : [
                                    Text(
                                      I18n.of(context)?.translate(
                                              "cm_guestHome.addGoods") ??
                                          "",
                                      style: TextStyle(
                                          fontSize: 10,
                                          color: Colors.grey.shade600),
                                    ),
                                    Text(
                                      I18n.of(context)?.translate(
                                              "cm_guestHome.orderGoods") ??
                                          "",
                                      style: TextStyle(
                                          fontSize: 10,
                                          color: Colors.grey.shade600),
                                    ),
                                  ]),
                      ),
                    ),
                    SizedBox(
                      height: 6,
                    )
                  ],
                );
              }),
        )
      ]),
    );
  }
}
