import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';
import 'package:get/get.dart';

class InvitedRewardPage extends StatefulWidget {
  const InvitedRewardPage({super.key});

  @override
  State<InvitedRewardPage> createState() => _InvitedRewardPageState();
}

class _InvitedRewardPageState extends State<InvitedRewardPage> {
  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    return AppScaffold(
      showScrollToTopButton: true,
      backgroundColor: Colors.white,
      scrollController: scrollController,
      body: SafeArea(
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 顶部搜索栏
              const SearchHeader(
                showBackIcon: true,
              ),

              // 活动头部
              Stack(
                children: [
                  Container(
                    width: double.infinity,
                    height: 360.sp,
                    decoration: const BoxDecoration(
                      image: DecorationImage(
                        image: AssetImage(
                            'assets/images/article/invite-header-bg.jpg'),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  SizedBox(
                    width: double.infinity,
                    height: 360.sp,
                    child: Column(
                      children: [
                        Padding(
                          padding: EdgeInsets.only(top: 60.sp),
                          child: Text(
                            '¡Invita a tus amigos a Chilatshop y obtén cupones!',
                            style: TextStyle(
                              color: Colors.white,
                              fontWeight: FontWeight.w500,
                              fontSize: 32.sp,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ),
                        SizedBox(height: 40.sp),
                        ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppColors.primaryColor,
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(8.r),
                            ),
                            padding: EdgeInsets.symmetric(
                                horizontal: 14.sp, vertical: 0.sp),
                          ),
                          onPressed: () {
                            // 跳转邀请页面
                            Get.toNamed(AppRoutes.MineInvitePage);
                          },
                          child: Text(
                            'INVITAR',
                            style: TextStyle(
                              fontSize: 24.sp,
                              fontWeight: FontWeight.w500,
                              color: Colors.white,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              // 活动描述
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 50.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'DESCRIPCIÓN DE LA ACTIVIDAD',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 18.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 16.sp),
                    Text(
                      'Gracias clientes por confiar en Chilatshop. Con el fin de retribuir su apoyo, Chilatshop ha lanzado la campaña "Los antiguos clientes invitan a nuevos clientes a registrarse", los antiguos clientes pueden invitar a nuevos clientes a unirse a través de un enlace de invitación exclusivo, ¡y ambas partes pueden obtener un cupón de descuento de comisión sin umbral como recompensa!',
                      style: TextStyle(fontSize: 16.sp),
                      textAlign: TextAlign.left,
                    ),
                  ],
                ),
              ),

              // 谁可以参加
              Container(
                color: const Color(0xFFF4F4F4),
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 50.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      '¿Quién puede participar?',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 24.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 30.sp),
                    Column(
                      children: [
                        Image.asset(
                          'assets/images/article/old-customer.png',
                          width: 240.sp,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(height: 10.sp),
                        Text(
                          'Esta promoción está abierta a clientes existentes que se hayan registrado con éxito en Chilatshop y hayan completado al menos una transacción.',
                          style: TextStyle(fontSize: 16.sp),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    SizedBox(height: 30.sp),
                    Column(
                      children: [
                        Image.asset(
                          'assets/images/article/new-customer.png',
                          width: 240.sp,
                          fit: BoxFit.contain,
                        ),
                        SizedBox(height: 10.sp),
                        Text(
                          'Los nuevos clientes deben registrarse por primera vez y no deben haber creado una cuenta en Chilatshop anteriormente.',
                          style: TextStyle(fontSize: 16.sp),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // 如何参与
              Padding(
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 50.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      '¿Cómo participar?',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 24.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 40.sp),
                    Image.asset(
                      'assets/images/article/personal-center.png',
                      fit: BoxFit.contain,
                    ),
                    SizedBox(height: 18.sp),
                    RichText(
                      text: TextSpan(
                        children: [
                          TextSpan(
                            text:
                                '1. Los clientes antiguos pueden iniciar sesión en el sitio web de Chilatshop e ingresar al ',
                            style:
                                TextStyle(fontSize: 16.sp, color: Colors.black),
                          ),
                          TextSpan(
                            text: '"Centro personal"',
                            style: TextStyle(
                              fontSize: 16.sp,
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          TextSpan(
                            text:
                                ' para obtener enlace de invitación el exclusivo.',
                            style:
                                TextStyle(fontSize: 16.sp, color: Colors.black),
                          ),
                        ],
                      ),
                    ),
                    SizedBox(height: 12.sp),
                    RichText(
                      text: TextSpan(
                        style: TextStyle(fontSize: 16.sp, color: Colors.black),
                        children: [
                          TextSpan(
                            text: '2. Comparta el ',
                          ),
                          TextSpan(
                            text: 'enlace de invitación',
                            style: TextStyle(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w500,
                              fontSize: 16.sp,
                            ),
                          ),
                          TextSpan(
                            text:
                                ' con amigos o socios comerciales que tengan necesidades de compra, y los nuevos clientes deben registrar una cuenta a través de este enlace.',
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // 奖励详情
              Container(
                color: const Color(0xFFF4F4F4),
                padding:
                    EdgeInsets.symmetric(horizontal: 16.sp, vertical: 50.sp),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    Text(
                      'Detalles de la recompensa',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                        fontSize: 24.sp,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 40.sp),
                    Column(
                      children: [
                        Stack(
                          alignment: Alignment.bottomCenter,
                          clipBehavior: Clip.none,
                          children: [
                            Image.asset(
                              'assets/images/article/old-customer.png',
                              width: 280.sp,
                              fit: BoxFit.contain,
                            ),
                            Positioned(
                              bottom: -80.sp,
                              child: Image.asset(
                                'assets/images/article/ten.png',
                                width: 280.sp,
                                fit: BoxFit.contain,
                              ),
                            ),
                          ],
                        ),
                        SizedBox(height: 70.sp),
                        Text(
                          '*Recompensa para los clientes habituales: Por cada nuevo cliente que se registre con éxito invitado por ellos, el cliente veterano que invita recibirá un cupón de descuento de comisión de US\$10 sin umbral, que puede utilizar en pedidos posteriores.',
                          style: TextStyle(
                            fontSize: 16.sp,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                    SizedBox(height: 20.sp),
                    Stack(
                      alignment: Alignment.bottomCenter,
                      clipBehavior: Clip.none,
                      children: [
                        Image.asset(
                          'assets/images/article/new-customer.png',
                          width: 280.sp,
                          fit: BoxFit.contain,
                        ),
                        Positioned(
                          bottom: -80.sp,
                          child: Image.asset(
                            'assets/images/article/fifteen.png',
                            width: 280.sp,
                            fit: BoxFit.contain,
                          ),
                        ),
                      ],
                    ),
                    SizedBox(height: 70.sp),
                    Text(
                      '*Recompensa para los nuevos clientes: Después de registrarse con éxito, los nuevos clientes recibirán un cupón de descuento de comisión de US\$15 sin umbral, que pueden utilizar en su primer pedido.',
                      style: TextStyle(fontSize: 16.sp),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 40.sp),
                    Container(
                      decoration: BoxDecoration(
                        border: Border.all(color: AppColors.primaryColor),
                        borderRadius: BorderRadius.circular(8.r),
                        color: Colors.white,
                      ),
                      padding: EdgeInsets.symmetric(
                          horizontal: 10.sp, vertical: 15.sp),
                      child: Column(
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Icon(Icons.warning_amber_rounded,
                                  color: AppColors.primaryColor, size: 30.sp),
                              SizedBox(width: 2.sp),
                              Text(
                                'Cosas a tener en cuenta',
                                style: TextStyle(
                                  fontWeight: FontWeight.w500,
                                  fontSize: 20.sp,
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.sp),
                          Text(
                              '1. Los cupones de descuento solo se pueden deducir de las comisiones.',
                              style: TextStyle(fontSize: 16.sp)),
                          SizedBox(height: 8.sp),
                          Text(
                              '2. Cada cliente habitual puede repetir la invitación y acumular recompensas sin límite de número de personas.',
                              style: TextStyle(fontSize: 16.sp)),
                          SizedBox(height: 8.sp),
                          Text(
                              '3. Los nuevos clientes deben completar el registro a través del enlace de invitación del cliente veterano para que sea válido.',
                              style: TextStyle(fontSize: 16.sp)),
                        ],
                      ),
                    ),
                  ],
                ),
              ),

              // 页脚
              Container(
                width: double.infinity,
                height: 180.sp,
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: AssetImage(
                        'assets/images/article/invite-footer-bg.jpg'),
                    fit: BoxFit.cover,
                  ),
                ),
                alignment: Alignment.center,
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.sp),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'Obtén el enlace de invitación ahora',
                        style: TextStyle(
                          color: AppColors.primaryColor,
                          fontWeight: FontWeight.w500,
                          fontSize: 19.sp,
                        ),
                      ),
                      SizedBox(height: 16.sp),
                      Text(
                        '¡Compártelo con tus amigos y disfruta de más descuentos!',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 19.sp,
                          fontWeight: FontWeight.w500,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              ),

              // 底部信息栏
              const MyFooter(),
            ],
          ),
        ),
      ),
    );
  }
}
