import 'package:chilat2_mall_app/utils/utils.dart';

class BasisAPI {
  // 购物车列表
  static Future<dynamic> useGetCountry(dynamic data) async {
    var response =
        await RequestUtil().post('basis/Country/listAll', params: data);

    return response;
  }

  // 获取配置
  static Future<dynamic> useGetNuxtConfig(dynamic data) async {
    var response = await RequestUtil().post('mystat/incoming', params: data);

    return response;
  }

  // 获取配置
  static Future<dynamic> useUploadStream(dynamic data) async {
    var response = await RequestUtil()
        .post('foundation/FileManager/uploadStream', params: data);
    // basis/FileManager/uploadStream
    // foundation/FileManager/uploadStream
    return response;
  }
}
