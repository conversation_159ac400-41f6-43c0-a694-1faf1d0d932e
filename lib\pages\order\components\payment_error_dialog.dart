import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';

class PaymentErrorDialog extends StatelessWidget {
  final String
      errorType; // 'payment_info', 'product_coupon', 'commission_coupon', 'all_coupon'
  final VoidCallback onClose;
  final VoidCallback? onChooseCouponAgain;

  const PaymentErrorDialog({
    Key? key,
    required this.errorType,
    required this.onClose,
    this.onChooseCouponAgain,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Dialog(
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(10.r),
      ),
      child: Padding(
        padding: EdgeInsets.all(16.sp),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            _buildDialogContent(context),
            if (errorType != 'payment_info') _buildActionButton(context),
          ],
        ),
      ),
    );
  }

  Widget _buildDialogContent(BuildContext context) {
    return Column(
      children: [
        Icon(
          Icons.warning_amber_rounded,
          color: AppColors.primaryColor,
          size: 40.sp,
        ),
        SizedBox(height: 12.sp),
        Text(
          _getErrorMessage(context),
          textAlign: TextAlign.center,
          style: TextStyle(
            fontSize: 16.sp,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(top: 20.sp),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: onChooseCouponAgain,
          style: ElevatedButton.styleFrom(
            backgroundColor: AppColors.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
            padding: EdgeInsets.symmetric(vertical: 12.sp),
          ),
          child: Text(
            I18n.of(context)?.translate('cm_order.chooseCouponAgain') ??
                'Choose Coupon Again',
            style: TextStyle(
              color: Colors.white,
              fontSize: 16.sp,
            ),
          ),
        ),
      ),
    );
  }

  String _getErrorMessage(BuildContext context) {
    switch (errorType) {
      case 'payment_info':
        return I18n.of(context)?.translate('cm_order.refreshPage') ??
            'Please refresh the page';
      case 'product_coupon':
      case 'commission_coupon':
      case 'all_coupon':
        return I18n.of(context)?.translate('cm_order.chooseCouponError') ??
            'Coupon information error';
      default:
        return 'An error occurred';
    }
  }

  // 显示支付信息错误对话框
  static void showPaymentInfoError(BuildContext context, VoidCallback onClose) {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) => PaymentErrorDialog(
        errorType: 'payment_info',
        onClose: onClose,
      ),
    );
  }

  // 显示优惠券信息错误对话框
  static void showCouponError(
    BuildContext context, {
    required String errorType,
    required VoidCallback onChooseCouponAgain,
  }) {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => PaymentErrorDialog(
        errorType: errorType,
        onClose: () {},
        onChooseCouponAgain: onChooseCouponAgain,
      ),
    );
  }
}
