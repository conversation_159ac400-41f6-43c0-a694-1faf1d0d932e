{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98be66f5b89e39e7a49b577231aa29829a", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98398bac938fb47b84c16f19633c15c92f", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986dd1a037feb0a53308838fbe4aff6ccb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9839d79a52f24f54321de12379511903ab", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e986dd1a037feb0a53308838fbe4aff6ccb", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/video_player_avfoundation/video_player_avfoundation-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MODULEMAP_FILE": "Target Support Files/video_player_avfoundation/video_player_avfoundation.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "video_player_avfoundation", "PRODUCT_NAME": "video_player_avfoundation", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9894fef8acb7de742f96aaab49fd64da62", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98fdfc1d8a442ac74910127e728ed08114", "guid": "bfdfe7dc352907fc980b868725387e9853e500882f310405cdd60ed863967cd9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e0fcbe48dffc422cb64d6226a259483a", "guid": "bfdfe7dc352907fc980b868725387e986fd37c6d5ebb34fce3ad2deaf0d28fe2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9851c793f5344ba9b01b4033ebe20876f6", "guid": "bfdfe7dc352907fc980b868725387e984be8b1e9561bf691f555d29c52717f51", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b5d26af4d4282e39b66a28b9e79a09ed", "guid": "bfdfe7dc352907fc980b868725387e98f5b3d7671fd0adb9939429e252a7be90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9887e94459fde73ade81a429b22aa5ddb5", "guid": "bfdfe7dc352907fc980b868725387e983b647ba50182f75353362acc53d12468", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982ec0f0897c1a5c8939bb55536a55e0f7", "guid": "bfdfe7dc352907fc980b868725387e98b972dc9e82c0f4e9f3a355a6f2319070", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9859d88f100a41e5eab54b6c2507d26932", "guid": "bfdfe7dc352907fc980b868725387e98b1d1fc3aa31d06cdc25b9ede3641a84b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987307cef802a6a0f0913d69f98414429d", "guid": "bfdfe7dc352907fc980b868725387e9859e1ccdc4ef3cdc59f21ddb178587961", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9866f0511f4754ed029eaaafcbd9f29456", "guid": "bfdfe7dc352907fc980b868725387e983c50e97a9eee2bdf07846ffecfc86c90", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a190932066ec6888d3fda36b29bab86", "guid": "bfdfe7dc352907fc980b868725387e98078019623dfc478ff71089cdd3e1c6f2", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98c52b6322c0e64a1370450f60bb700f73", "guid": "bfdfe7dc352907fc980b868725387e9875dd53d838c5d6e46da125391279d1cc", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b77ac17e1856603e90b832c7b2b9f5e7", "guid": "bfdfe7dc352907fc980b868725387e98f118eb1ea78cab1f8e87ec81898982dd", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e989a929861cf9a7f7a4c6db75c37f0cea6", "guid": "bfdfe7dc352907fc980b868725387e986b5d53e84d3cbab9dbe972a4724bac69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983a882b6904cd9816af15db2690fc1bbd", "guid": "bfdfe7dc352907fc980b868725387e982014931802a2aa0eea4889d60f6d9053", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9828981ab724ff018298e53e5feedc7c20", "guid": "bfdfe7dc352907fc980b868725387e989314e081c3f5bfab1279e3c2c1139a24", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e983145ad27ab9a5d703e29269b9b2b583a", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e982101bbf6d7b6001e89206542c098d1d2", "guid": "bfdfe7dc352907fc980b868725387e98e9b87457c20d4e488a41991502473062"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98516c22c111416a43a49c58a7b9002875", "guid": "bfdfe7dc352907fc980b868725387e98456eb759d1ce04f9d7c85a549390618f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983f122f50a10de641dd8f11ea37fd9d45", "guid": "bfdfe7dc352907fc980b868725387e98030298b42f9d00d7e49f5505935c8341"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985cc33e3d8d9a9fa2a41ea78e019ba6e8", "guid": "bfdfe7dc352907fc980b868725387e982cb3c74215054107c0e8a0eb5cb99b5e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988c01dbae1961d06cb7d4e7fa88755467", "guid": "bfdfe7dc352907fc980b868725387e983eebde66b3227fcb690286332b132d62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b35c1cd63f97307c00f2e2b0fbe44c6", "guid": "bfdfe7dc352907fc980b868725387e98f57a1c545a5373ee8185e8ee8f4e36e4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ad34a67a0b995c8ccc99611245bc738b", "guid": "bfdfe7dc352907fc980b868725387e98d83911e62d80d63c9ceecd20a35e5cfb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9853dbe7b3407492ff5d16b1ca250608dd", "guid": "bfdfe7dc352907fc980b868725387e98cf9c584ae4850e49090a0517b83615cf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9800a3f4b48bd08ae5eb54d2582060eabe", "guid": "bfdfe7dc352907fc980b868725387e98f7608108c022cc3bd2e724a353f99dfa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ca1a10fca7e6d8a90c555cbec26abbfe", "guid": "bfdfe7dc352907fc980b868725387e98bc47ccb1a8c802798a004236f348b6ae"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98df3097025aa54478a15219e54ffa64da", "guid": "bfdfe7dc352907fc980b868725387e98a89106363ecf1c82c77a5e97d941ae89"}], "guid": "bfdfe7dc352907fc980b868725387e9884539da3bdf2ed4ad426ca202aaf6bc7", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e98c1dae7e29df157eddda815ae5462108c"}], "guid": "bfdfe7dc352907fc980b868725387e98119e723bd2257d68f10fcea70feb1342", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e981c10b7a3ff26395a834064a0172d6124", "targetReference": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04"}], "guid": "bfdfe7dc352907fc980b868725387e98a3435bfc3b1be550d78f0653eca9c848", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e98e4af9c7b061f7f7eb180b825e303ea04", "name": "video_player_avfoundation-video_player_avfoundation_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988a0a5b40b007f81bee1472e4d0fb23da", "name": "video_player_avfoundation", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98b5f237537920ce49888f6f7f73c80a6c", "name": "video_player_avfoundation.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}