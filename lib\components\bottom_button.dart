import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter/material.dart';

class BottomButton extends StatelessWidget {
  final String? text;
  final Function? handleOk;
  final TextStyle? textStyle; // 添加 textStyle 属性

  const BottomButton({super.key, this.handleOk, this.text, this.textStyle});

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () => handleOk!(),
      child: Container(
        margin: EdgeInsets.only(
          bottom: MediaQuery.of(context).padding.bottom,
          left: 10,
          right: 10,
        ),
        height: 48,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            //背景径向渐变
            colors: [AppColors.buttonLine1, AppColors.buttonLine2],
          ),
          borderRadius: BorderRadius.circular(10.0),
        ),
        child: Center(
          child: Text(
            text!,
            style: textStyle ??
                TextStyle(
                  color: Colors.white,
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                ),
          ),
        ),
      ),
    );
  }
}
