import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/pages/product/components/product_card.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class BannerProduct extends StatefulWidget {
  final dynamic data;
  final String? banner;
  const BannerProduct({super.key, this.data, this.banner});

  @override
  State<BannerProduct> createState() => _BannerProductState();
}

class _BannerProductState extends State<BannerProduct> {
  final double _goodsWidth = 150.0.sp;
  final double _imageHeight = 128.0.sp;
  final double _bannerHeight = 90.0.sp;
  final double _priceFontSize = 12.0.sp;
  final double _titleFontSize = 12.0.sp;
  final double _borderRadius = 4.0.sp;

  @override
  void initState() {
    super.initState();
  }

  // 检查是否有预估运费
  bool onShowEstimateFreight() {
    for (dynamic item in widget.data?['hotSaleGoods'] ?? []) {
      if (item?['pcsEstimateFreight'] != null) {
        return true;
      }
    }

    return false;
  }

  @override
  Widget build(BuildContext context) {
    bool showEstimateFreight = onShowEstimateFreight();

    return Container(
        margin: const EdgeInsets.only(top: 0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Visibility(
              visible: widget.banner != null,
              child: Padding(
                padding: const EdgeInsets.only(top: 6, bottom: 2),
                child: GestureDetector(
                  onTap: () {
                    NavigatorUtil.pushNamed(context, AppRoutes.ProductList);
                  },
                  child: CachedNetworkImage(
                    imageUrl: widget.banner ?? '',
                    fit: BoxFit.fill,
                    width: MediaQuery.sizeOf(context).width,
                    height: _bannerHeight,
                  ),
                ),
              )),
          SizedBox(
            height: showEstimateFreight ? 208.sp : 172.sp,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: widget.data?['hotSaleGoods'].length,
              itemBuilder: (context, index) {
                dynamic product = widget.data?['hotSaleGoods']?[index];
                return MidSearchGoodsCard(goods: product);
              },
            ),
          ),
        ]));
  }
}
