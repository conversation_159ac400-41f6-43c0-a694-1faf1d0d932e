// lib/components/app_scaffold.dart
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/components/whatsapp_button.dart';
import 'package:chilat2_mall_app/components/scroll_to_top_button.dart';
import 'package:flutter/services.dart';

class AppScaffold extends StatefulWidget {
  final Widget body;
  final bool? canPop;
  final PreferredSizeWidget? appBar;
  final Widget? bottomNavigationBar;
  final bool showWhatsAppButton;
  final String? whatsAppPhoneNumber;
  final bool showScrollToTopButton;
  final ScrollController? scrollController; // 可选参数，如果不提供则自动创建
  final double? showWhatsBottom; //  whatsapp按钮距离底部的距离
  final double scrollToTopThreshold;
  final double? scrollToTopButtonBottom;
  final double? scrollToTopButtonRight;
  final double? scrollToTopButtonSize;
  final Color? backgroundColor; // 添加背景色参数
  final SystemUiOverlayStyle? systemUiOverlayStyle; // 添加状态栏样式参数
  final Function(dynamic result)? onPopInvoked; // 左滑右滑回调事件
  final bool? resizeToAvoidBottomInset; // 添加键盘避让控制参数

  const AppScaffold({
    super.key,
    required this.body,
    this.appBar,
    this.canPop = false,
    this.bottomNavigationBar,
    this.showWhatsAppButton = true,
    this.whatsAppPhoneNumber,
    this.showScrollToTopButton = false,
    this.scrollController, // 现在是可选的
    this.scrollToTopThreshold = 300,
    this.scrollToTopButtonBottom = 140,
    this.scrollToTopButtonRight = 12,
    this.scrollToTopButtonSize,
    this.backgroundColor,
    this.systemUiOverlayStyle, // 状态栏样式参数
    this.onPopInvoked,
    this.resizeToAvoidBottomInset,
    this.showWhatsBottom = 120, // 键盘避让控制参数
  });

  @override
  State<AppScaffold> createState() => _AppScaffoldState();
}

class _AppScaffoldState extends State<AppScaffold> {
  ScrollController? _internalScrollController;
  bool _showScrollToTopButton = false;

  // 获取实际使用的 ScrollController
  ScrollController get _effectiveScrollController {
    return widget.scrollController ?? _internalScrollController!;
  }

  @override
  void initState() {
    super.initState();
    // 如果没有提供外部 ScrollController，则创建内部的
    if (widget.scrollController == null) {
      _internalScrollController = ScrollController();
    }

    // 添加滚动监听器
    if (widget.showScrollToTopButton) {
      _effectiveScrollController.addListener(_scrollListener);
    }
  }

  @override
  void dispose() {
    // 移除监听器并释放内部控制器
    if (widget.showScrollToTopButton) {
      _effectiveScrollController.removeListener(_scrollListener);
    }
    _internalScrollController?.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final shouldShow = _effectiveScrollController.positions.isNotEmpty &&
        _effectiveScrollController.position.pixels >
            widget.scrollToTopThreshold;

    if (_showScrollToTopButton != shouldShow) {
      setState(() {
        _showScrollToTopButton = shouldShow;
      });
    }
  }

  // 是否退出应用程序
  Future<void> onExitPressed(BuildContext context) async {
    final shouldExit = await showExitConfirmation(context);
    if (shouldExit == true) {
      // 根据平台执行退出逻辑
      if (Theme.of(context).platform == TargetPlatform.android) {
        SystemNavigator.pop(); // Android 退出
      } else if (Theme.of(context).platform == TargetPlatform.iOS) {
        // iOS 无法主动退出，通常返回首页
        Navigator.popUntil(context, (route) => route.isFirst);
      } else {
        SystemNavigator.pop();
      }
    }
  }

  Future<bool?> showExitConfirmation(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title:
            Text(I18n.of(context)!.translate("cm_app.confirmToExit", "确认退出")),
        content: Text(I18n.of(context)!
            .translate("cm_app.confirmToExitTips", "确定要退出应用吗？")),
        actions: [
          TextButton(
            child: Text(I18n.of(context)!.translate("cm_app.cancel", "取消")),
            onPressed: () => Navigator.of(context).pop(false),
          ),
          TextButton(
            child: Text(I18n.of(context)!.translate("cm_app.exit", "退出")),
            onPressed: () => Navigator.of(context).pop(true),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏样式：如果有传入自定义样式则使用，否则使用默认样式
    final SystemUiOverlayStyle effectiveSystemUiOverlayStyle =
        widget.systemUiOverlayStyle ??
            const SystemUiOverlayStyle(
              statusBarColor: Colors.white, // 默认状态栏背景色为白色
              statusBarIconBrightness: Brightness.dark, // 默认状态栏图标为深色（黑色）
              statusBarBrightness: Brightness.light, // 默认iOS状态栏文字为深色
            );

    SystemChrome.setSystemUIOverlayStyle(effectiveSystemUiOverlayStyle);

    return SafeArea(
        child: PopScope(
      canPop: widget.canPop == true,
      onPopInvokedWithResult: (bool didPop, dynamic result) {
        if (didPop) {
          return;
        }
        if (Navigator.of(context).canPop()) {
          if (widget.onPopInvoked != null) {
            widget.onPopInvoked!(result);
          } else {
            Navigator.of(context).pop();
          }
        } else {
          onExitPressed(context);
        }
      },
      child: Stack(
        children: [
          Scaffold(
            appBar: widget.appBar,
            body: widget.body,
            bottomNavigationBar: widget.bottomNavigationBar,
            backgroundColor: widget.backgroundColor,
            resizeToAvoidBottomInset: widget.resizeToAvoidBottomInset ?? true,
          ),
          if (widget.showWhatsAppButton)
            WhatsAppButton(
                phoneNumber: widget.whatsAppPhoneNumber,
                distanceBottom: widget.showWhatsBottom),
          if (widget.showScrollToTopButton && _showScrollToTopButton)
            Positioned(
              bottom: widget.scrollToTopButtonBottom,
              right: widget.scrollToTopButtonRight,
              child: ScrollToTopButton(
                controller: _effectiveScrollController,
                showThreshold: widget.scrollToTopThreshold,
                size: widget.scrollToTopButtonSize,
              ),
            ),
        ],
      ),
    ));

    // return Stack(
    //   children: [
    //     Scaffold(
    //       appBar: widget.appBar,
    //       body: widget.body,
    //       bottomNavigationBar: widget.bottomNavigationBar,
    //       backgroundColor: widget.backgroundColor,
    //     ),
    //     if (widget.showWhatsAppButton)
    //       WhatsAppButton(phoneNumber: widget.whatsAppPhoneNumber),
    //     if (widget.showScrollToTopButton && _showScrollToTopButton)
    //       Positioned(
    //         bottom: widget.scrollToTopButtonBottom,
    //         right: widget.scrollToTopButtonRight,
    //         child: ScrollToTopButton(
    //           controller: _effectiveScrollController,
    //           showThreshold: widget.scrollToTopThreshold,
    //           size: widget.scrollToTopButtonSize,
    //         ),
    //       ),
    //   ],
    // );
  }
}
