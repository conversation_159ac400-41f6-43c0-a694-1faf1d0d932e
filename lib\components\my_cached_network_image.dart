import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';

class MyCachedNetworkImage extends StatelessWidget {
  final String imageurl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;

  const MyCachedNetworkImage({
    super.key,
    required this.imageurl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
  });

  @override
  Widget build(BuildContext context) {
    // 空URL检查
    final uri = Uri.tryParse(imageurl);
    if (imageurl.isEmpty ||
        imageurl == 'null' ||
        imageurl == 'undefined' ||
        uri == null ||
        !uri.hasAbsolutePath) {
      return _buildErrorWidget();
    }

    return ClipRRect(
      borderRadius: borderRadius ?? BorderRadius.circular(0),
      child: CachedNetworkImage(
        imageUrl: imageurl,
        width: width,
        height: height,
        placeholder: (_, __) => _buildPlaceholder(),
        fit: fit,
        memCacheWidth: _getOptimalCacheWidth(),
        memCacheHeight: _getOptimalCacheHeight(),
        maxWidthDiskCache: _getOptimalDiskWidth(),
        maxHeightDiskCache: _getOptimalDiskHeight(),
        errorWidget: (context, url, error) => _buildErrorWidget(),
        fadeInDuration: const Duration(milliseconds: 200),
        fadeOutDuration: const Duration(milliseconds: 100),
      ),
    );
  }

  /// 构建占位符 - 红色动态加载效果
  Widget _buildPlaceholder() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        borderRadius: borderRadius ?? BorderRadius.circular(0),
      ),
      child: Center(
        child: SizedBox(
          width: _getLoadingSize(),
          height: _getLoadingSize(),
          child: CircularProgressIndicator(
            strokeWidth: _getStrokeWidth(),
            valueColor: const AlwaysStoppedAnimation<Color>(Color(0xFFE50113)),
          ),
        ),
      ),
    );
  }

  /// 构建错误组件
  Widget _buildErrorWidget() {
    return Container(
      width: width,
      height: height,
      decoration: BoxDecoration(
        color: Colors.grey[200],
        borderRadius: borderRadius ?? BorderRadius.circular(0),
      ),
      child: Center(
        child: Icon(
          Icons.broken_image_outlined,
          size: _getIconSize(),
          color: Colors.grey[400],
        ),
      ),
    );
  }

  /// 获取图标大小
  double _getIconSize() {
    if (width == null && height == null) return 24;

    final size = width ?? height ?? 100;
    if (size <= 50) return 16;
    if (size <= 100) return 20;
    if (size <= 200) return 24;
    return 32;
  }

  /// 获取加载动画大小
  double _getLoadingSize() {
    if (width == null && height == null) return 20;

    final size = width ?? height ?? 100;
    if (size <= 50) return 12;
    if (size <= 100) return 16;
    if (size <= 200) return 20;
    return 24;
  }

  /// 获取加载动画线条宽度
  double _getStrokeWidth() {
    if (width == null && height == null) return 2;

    final size = width ?? height ?? 100;
    if (size <= 50) return 1.5;
    if (size <= 100) return 2;
    if (size <= 200) return 2.5;
    return 3;
  }

  /// 获取最优内存缓存宽度
  int _getOptimalCacheWidth() {
    if (width == null) return 600; // 默认高质量

    // 根据显示尺寸动态调整缓存尺寸
    final displayWidth = width!;
    if (displayWidth <= 100) return 300; // 小图标
    if (displayWidth <= 200) return 600; // 列表缩略图
    if (displayWidth <= 400) return 800; // 中等尺寸
    return 1200; // 大图/详情页
  }

  /// 获取最优内存缓存高度
  int _getOptimalCacheHeight() {
    if (height == null) return 600; // 默认高质量

    final displayHeight = height!;
    if (displayHeight <= 100) return 300;
    if (displayHeight <= 200) return 600;
    if (displayHeight <= 400) return 800;
    return 1200;
  }

  /// 获取最优磁盘缓存宽度
  int _getOptimalDiskWidth() {
    final cacheWidth = _getOptimalCacheWidth();
    return (cacheWidth * 1.5).round(); // 磁盘缓存比内存缓存大50%
  }

  /// 获取最优磁盘缓存高度
  int _getOptimalDiskHeight() {
    final cacheHeight = _getOptimalCacheHeight();
    return (cacheHeight * 1.5).round();
  }
}
