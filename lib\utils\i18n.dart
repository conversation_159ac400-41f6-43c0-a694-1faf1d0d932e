import 'package:flutter/material.dart';

class I18n {
  final Locale locale;
  Map<String, String> localizedStrings = {};

  I18n(this.locale);

  // 加载国际化数据
  Future<bool> load(Map<String, String> jsonData) async {
    // Map<String, dynamic> jsonMap = json.decode(jsonData);

    // localizedStrings = jsonMap[locale.languageCode].cast<String, String>();
    localizedStrings.addAll(jsonData);

    return true;
  }

  // 获取对应key的本地化字符串
  String translate(String key, [String? def]) {
    return localizedStrings[key] ?? def ?? key;
  }

  static I18n? of(BuildContext context) {
    return Localizations.of<I18n>(context, I18n);
  }

  // 静态方法方便获取当前语言环境的实例
  // static I18n of(BuildContext context) {
  //   return "TODO 2235:";
  //   // return Localizations.of<I18n>(context, I18n);
  // }
}

class AppLocalizationsDelegate extends LocalizationsDelegate<I18n> {
  final Map<String, String> jsonData;

  AppLocalizationsDelegate(this.jsonData);

  @override
  bool isSupported(Locale locale) {
    return ['en', 'zh'].contains(locale.languageCode);
  }

  @override
  Future<I18n> load(Locale locale) async {
    I18n localizations = I18n(locale);
    await localizations.load(jsonData);
    return localizations;
  }

  @override
  bool shouldReload(covariant LocalizationsDelegate<I18n> old) {
    return false;
  }
}
