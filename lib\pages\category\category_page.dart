import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/main/main_controller.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';

class CategoryPage extends StatefulWidget {
  final String? cateId;
  const CategoryPage({super.key, this.cateId});

  @override
  State<CategoryPage> createState() => _CategoryPageState();
}

class _CategoryPageState extends State<CategoryPage> {
  bool _isLoading = false;
  double screenWidth = 0.0;
  int selectedCateIndex = 0;
  MidCartModel? cartData;
  List<MarketingCategoryModel> secondaryCates = [];
  List<MarketingCategoryModel>? categoryList = [];

  final TextEditingController _textController = TextEditingController();
  final MainController mainController = Get.put(MainController());

  @override
  void initState() {
    super.initState();
    onCartData();
    onPageData();
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      MarketingCategoryTreeResp? cateData =
          await mainController.onCategoryTree();

      print("==>>TODO 3321: ${cateData == null}");
      if (cateData != null) {
        setState(() {
          int? index = cateData.data
              ?.indexWhere((element) => element.id == widget.cateId);
          MarketingCategoryModel? target;

          if (index == -1) {
            target = cateData.data?.first;
          } else {
            target = cateData.data?[index ?? 0];
          }

          categoryList = cateData.data;
          onUpdateSecondary(target, index ?? 0);
        });
      } else {
        dynamic res = await ProductAPI.useCategoryTree({});
        print("==>>TODO 3323: $res");
        if (res['result']['code'] == 200) {
          setState(() {
            MarketingCategoryTreeResp categoryData =
                MarketingCategoryTreeResp.fromJson(res);
            int? index = categoryData.data
                ?.indexWhere((element) => element.id == widget.cateId);
            MarketingCategoryModel? target;

            if (index == -1) {
              target = categoryData.data?.first;
            } else {
              target = categoryData.data?[index ?? 0];
            }

            categoryList = categoryData.data;
            onUpdateSecondary(target, index ?? 0);
          });
        }
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> onCartData() async {
    MidCartModel? result = await Global.getCartData();
    if (result != null) {
      setState(() {
        cartData = result;
      });
    }
  }

  Future<void> onUpdateSecondary(
      MarketingCategoryModel? cate, int index) async {
    try {
      setState(() {
        secondaryCates = cate?.children ?? [];
      });
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> showSecondaryCates(
      MarketingCategoryModel? cate, int index) async {
    await onUpdateSecondary(cate, index);
  }

  // 购物车列表
  void onGotoCartList() async {
    Future.delayed(Duration(milliseconds: 50), () {
      if (Global.isLogin.value == false) {
        Get.toNamed(AppRoutes.LoginPage,
            arguments: {"pageSource": AppRoutes.CartPage});
      } else {
        Get.toNamed(AppRoutes.CartPage, arguments: {'back': true});
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;

    return AppScaffold(
        onPopInvoked: (result) {
          print("==>>TODO 5522: $result");
        },
        body: Stack(children: [
          Column(
            children: [
              SearchHeader(
                  showCart: true,
                  cartGoodsCount: cartData?.stat?.goodsCount ?? 0),
              Expanded(
                child: Row(
                  children: [
                    Expanded(
                      flex: 1,
                      child: Container(
                        color: Colors.grey.shade100,
                        alignment: Alignment.center,
                        child: ListView.builder(
                          itemCount: categoryList?.length,
                          itemBuilder: (context, index) {
                            // 判断当前项目是否被选中
                            return _buildPrimaryCate(
                                categoryList?[index], index);
                          },
                        ),
                      ),
                    ),
                    Expanded(
                      flex: 2,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 6),
                        child: ListView.builder(
                          itemCount: secondaryCates.length,
                          itemBuilder: (context, index) {
                            MarketingCategoryModel? cateItem =
                                secondaryCates[index];
                            return _buildSecondaryCate(cateItem);
                          },
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              AppTabbar(currentIndex: 1)
            ],
          ),
          Visibility(
              visible: _isLoading,
              child: Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.1),
                  child: Center(
                    child: RotationalLoadingWidget(
                        // colors: [Colors.blue, Colors.green, Colors.red], // 多段颜色
                        ),
                  ),
                ),
              ))
        ]));
  }

  Widget _buildPrimaryCate(MarketingCategoryModel? cateItem, int index) {
    final isSelected = index == selectedCateIndex;

    return GestureDetector(
      onTap: () {
        setState(() {
          // 更新选中的项目索引
          selectedCateIndex = index;
          showSecondaryCates(cateItem, index);
        });
      },
      child: Container(
        // 根据选中状态改变背景色
        padding: EdgeInsets.only(left: 6, top: 6, bottom: 6),
        decoration: BoxDecoration(
          border: Border(
            left: BorderSide(
              color: isSelected ? Colors.red : Colors.grey.shade100,
              width: 4,
            ),
          ),
          color: isSelected ? Colors.white : Colors.grey.shade100,
        ),
        child: Text(
          cateItem?.cateName ?? '',
          style: TextStyle(
            fontSize: 18.sp,
            color: isSelected ? Colors.red : Colors.black,
          ),
        ),
      ),
    );
  }

  Widget _buildSecondaryCate(MarketingCategoryModel? cateItem) {
    List<MarketingCategoryModel>? children = cateItem?.children;

    return Container(
      padding: EdgeInsets.only(
        top: 8,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        FancyButton(
          onTap: () {
            NavigatorUtil.pushNamed(context, AppRoutes.ProductList, arguments: {
              "categoryId": cateItem?.id,
              "cateName": cateItem?.cateName,
            });
          },
          color: Colors.white,
          width: screenWidth,
          borderColor: Colors.white,
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start, // 顶部对齐
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  cateItem?.cateName ?? "",
                  softWrap: true, // 允许换行
                  style:
                      TextStyle(fontSize: 18.sp, fontWeight: FontWeight.w400),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(vertical: 4),
                child: Center(
                  child: Icon(
                    Icons.arrow_forward_ios,
                    size: 18.sp,
                    color: Colors.black54,
                  ),
                ),
              )
            ],
          ),
        ),
        SizedBox(height: 10),
        ...children
                ?.map((child) => FancyButton(
                      onTap: () {
                        NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                            arguments: {
                              "categoryId": child.id,
                              "cateName": child.cateName,
                            });
                      },
                      color: Colors.white,
                      width: screenWidth * 0.6,
                      borderColor: Colors.white,
                      child: Container(
                        width: double.infinity, // 占满整行宽度
                        padding: EdgeInsets.only(left: 12, top: 4),
                        child: Text(
                          child.cateName ?? "",
                          softWrap: true, // 允许换行
                          style:
                              TextStyle(fontSize: 16.sp, color: Colors.black54),
                        ),
                      ),
                    ))
                .toList() ??
            [],
      ]),
    );
  }
}
