import 'package:flutter/material.dart';

class AnimatedIconButton extends StatefulWidget {
  final IconData icon; // 默认图标
  final IconData activeIcon; // 激活状态图标
  final double size; // 图标尺寸
  final Color color; // 默认颜色
  final Color activeColor; // 激活状态颜色
  final Duration duration; // 动画时长
  final Curve curve; // 动画曲线
  final Function(bool) onPressed; // 点击回调

  const AnimatedIconButton({
    super.key,
    required this.icon,
    required this.activeIcon,
    this.size = 24,
    this.color = Colors.grey,
    this.activeColor = Colors.red,
    this.duration = const Duration(milliseconds: 300),
    this.curve = Curves.easeInOutBack,
    required this.onPressed,
  });

  @override
  State<AnimatedIconButton> createState() => _AnimatedIconButtonState();
}

class _AnimatedIconButtonState extends State<AnimatedIconButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<Color?> _colorAnimation;

  bool _isActive = false;

  @override
  void initState() {
    super.initState();

    // 动画控制器
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    );

    // 缩放动画
    _scaleAnimation = TweenSequence<double>([
      TweenSequenceItem(tween: Tween(begin: 1.0, end: 0.8), weight: 50),
      TweenSequenceItem(tween: Tween(begin: 0.8, end: 1.2), weight: 50),
    ]).animate(CurvedAnimation(parent: _controller, curve: widget.curve));

    // 颜色动画
    _colorAnimation = ColorTween(
      begin: widget.color,
      end: widget.activeColor,
    ).animate(_controller);
  }

  void _handleTap() {
    setState(() => _isActive = !_isActive);

    if (_controller.status == AnimationStatus.completed) {
      _controller.reverse();
    } else {
      _controller.forward();
    }

    widget.onPressed(_isActive);
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _controller,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: IconButton(
            iconSize: widget.size,
            icon: Icon(
              _isActive ? widget.activeIcon : widget.icon,
              color: _colorAnimation.value,
            ),
            onPressed: _handleTap,
          ),
        );
      },
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }
}
