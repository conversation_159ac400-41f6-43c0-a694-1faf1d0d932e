import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';

class TermsPage extends StatelessWidget {
  final bool isModal;
  final AppBarBackType modalBackButtonType;
  final VoidCallback? onBack;

  const TermsPage({
    Key? key,
    this.isModal = false,
    this.modalBackButtonType = AppBarBackType.None,
    this.onBack,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // 在模态框模式下，使用自定义布局
    if (isModal) {
      return _buildModalContent(context);
    }

    // 非模态框模式下，使用原有Scaffold布局
    return Scaffold(
      appBar: AppBar(
        title: Text(
          'Política de privacidad',
          style: TextStyle(fontWeight: FontWeight.bold),
        ),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            Navigator.pop(context);
          },
        ),
      ),
      body: _buildPageContent(context),
    );
  }

  // 模态框模式下的内容
  Widget _buildModalContent(BuildContext context) {
    return Column(
      children: [
        // 自定义模态框导航栏
        if (onBack != null)
          Container(
            padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 16.w),
            child: Row(
              children: [
                IconButton(
                  icon: Icon(Icons.arrow_back),
                  onPressed: onBack,
                ),
                Text(
                  'Política de privacidad',
                  style: TextStyle(
                    fontSize: 18.sp,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        // 内容区域
        Expanded(
          child: _buildPageContent(context),
        ),
      ],
    );
  }

  // 页面内容 - 无论模态框还是独立页面都使用
  Widget _buildPageContent(BuildContext context) {
    return SingleChildScrollView(
      padding: EdgeInsets.all(16.0),
      physics: ClampingScrollPhysics(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            'De conformidad con la legislación en Protección de Datos y el artículo 10 de la Ley 34/2002 de Servicios de la Sociedad de la Información y Comercio Electrónico, el propietario del sitio y responsable del tratamiento de los datos es:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Ming Zhan Import & Export Co., Limited',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Datos de contacto de la empresa:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'L5, Edificio A, Wangdao RD., Yiwu, Zhejiang, China',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          InkWell(
            onTap: () {
              // 跳转到首页
              Get.offAllNamed(AppRoutes.HomePage);
            },
            child: Text(
              'shop.chilat.com',
              style: TextStyle(fontSize: 16, color: Colors.blue),
            ),
          ),
          SizedBox(height: 16),
          Text(
            'Uso y finalidad de los datos obtenidos',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'Los datos que solicitamos en nuestra página web son los adecuados, pertinentes y estrictamente necesarios para la finalidad de gestionar y tramitar la petición que nos ha realizado, poder enviarle información de nuestros servicios, así como realizar la compraventa de productos y servicios, y en ningún caso está obligado a facilitarlos.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Usos de los datos obtenidos:',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          _buildBulletPoint(
              'Gestionar la petición realizada en nuestros respectivos formularios de contacto.'),
          _buildBulletPoint(
              'Enviar comunicaciones comerciales vía SMS, WhatsApp y correo electrónico.'),
          _buildBulletPoint(
              'Gestionar la compra de productos por parte del titular de los datos.'),
          SizedBox(height: 16),
          Text(
            'Los datos de cumplimentación obligatoria se especifican en el propio formulario donde se recaban los datos, y su negativa a suministrarlos implicará no poder gestionar su petición. Asimismo, nos asegura que todos los datos facilitados son ciertos, veraces y pertinentes para la finalidad por la que los solicitamos.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Los datos personales proporcionados se conservarán mientras se mantenga la relación comercial, y una vez finalice la citada relación se mantendrán bloqueados el tiempo legalmente establecido, antes de su destrucción.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'La base de legitimación para el tratamiento de sus datos:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          _buildBulletPoint(
              'El consentimiento del interesado para la gestión y tramitación de peticiones, así como para el envío de comunicaciones comerciales.'),
          _buildBulletPoint(
              'Relación contractual para la formalización y gestión de la compraventa de productos.'),
          SizedBox(height: 16),
          Text(
            'El envío de los mismos implica su autorización expresa a incorporarlos a nuestros ficheros correspondientes, siempre y cuando Ming Zhan Import & Export Co., Limited lo considere conveniente para la gestión de la petición que solicite.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Sus datos no serán cedidos a terceros sin su consentimiento expreso, a excepción de aquellas cesiones necesarias para dar cumplimiento a una obligación legal.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Le comunicamos que sus datos serán transferidos a un tercer país fuera del espacio económico latinoamericano dado que la compra de productos se realiza a empresas residentes en la República Popular de China y los datos recabados en la página web son conservados en un servidor instalado en el mismo país. Dicha transferencia es necesaria para la realización de la compraventa y la ejecución del contrato.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Mediante la aceptación de la casilla del formulario, usted consiente el envío de información de nuestros servicios que puedan resultar de su interés.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Es importante que para que podamos mantener sus datos personales actualizados, nos informe siempre que haya alguna modificación en ellos. En caso contrario, no respondemos de la veracidad de los mismos. Consideramos que, si no cancela sus datos personales expresamente de nuestros ficheros, continúa interesado en seguir incorporado a los mismos hasta que el Responsable lo considere oportuno y mientras sea adecuado a la finalidad por la que se obtuvieron.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Derechos de los interesados',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'El titular de los datos personales tiene derecho a:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          _buildBulletPoint(
              'Obtener confirmación sobre si el Responsable está tratando los datos personales que les conciernan, o no.'),
          _buildBulletPoint(
              'Acceder a sus datos personales, así como a solicitar la rectificación de los datos inexactos o, en su caso, solicitar su supresión cuando, entre otros motivos, los datos ya no sean necesarios para los fines que fueron recogidos.'),
          _buildBulletPoint(
              'En determinadas circunstancias, los interesados podrán solicitar la limitación del tratamiento de sus datos, en cuyo caso únicamente el Responsable los conservará para el ejercicio o la defensa de reclamaciones.'),
          _buildBulletPoint(
              'En determinadas circunstancias y por motivos relacionados con su situación particular, los interesados podrán oponerse al tratamiento de sus datos.'),
          _buildBulletPoint(
              'En determinadas circunstancias, en virtud del derecho de portabilidad, los interesados tendrán derecho a obtener sus datos personales en un formato estructurado de uso común y lectura mecánica y transmitirlo a otro responsable.'),
          SizedBox(height: 16),
          Text(
            'El titular puede ejercer sus derechos:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          _buildBulletPoint(
              'Mediante escrito dirigido a Ming Zhan Import & Export Co., Limited, L5, Edificio A, Wangdao RD., Yiwu, Zhejiang, China, referencia "Protección de Datos".'),
          _buildBulletPoint(
              'Mediante correo electrónico a la dirección <EMAIL> indicando en el asunto "Protección de Datos".'),
          SizedBox(height: 16),
          Text(
            'Si considera que sus derechos no se han atendido adecuadamente, tiene derecho a presentar una reclamación ante la Agencia China de Protección de Datos.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Consentimiento para el uso de cookies.',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'Nuestros servicios utilizan cookies para recopilar y almacenar cierta información. Por lo general, implican piezas de información o código que un sitio web transfiere o accede desde el disco duro de su computadora o dispositivo móvil para almacenar y, a veces, rastrear información sobre usted. Las cookies y tecnologías similares le permiten ser recordado cuando usa esa computadora o dispositivo para interactuar con sitios web y servicios en línea y se pueden usar para administrar una variedad de funciones y contenido, así como para almacenar búsquedas y presentar contenido personalizado.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Nuestros servicios utilizan cookies y tecnologías similares para distinguirlo de otros usuarios de nuestros servicios. Esto nos ayuda a brindarle una buena experiencia cuando navega por nuestro sitio/usa nuestra aplicación y también nos permite mejorar nuestros servicios.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'La mayoría de los navegadores web aceptan automáticamente cookies y tecnologías similares, pero si lo prefiere, puede cambiar su navegador para evitarlo y su pantalla de ayuda o manual le indicará cómo hacerlo.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Varias cookies y tecnologías similares que utilizamos duran solo la duración de su sesión web o aplicación y caducan cuando cierra su navegador o sale de la aplicación. Otros se usan para recordarlo cuando regresa a los servicios y durarán más.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Utilizamos estas cookies y otras tecnologías sobre la base de que son necesarias para la ejecución de un contrato con usted, o porque su uso es en nuestro interés legítimo (donde hemos considerado que estos no son anulados por sus derechos), y, en algunos casos, cuando así lo exija la ley, en los que haya dado su consentimiento para su uso.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          Text(
            'Usamos cookies para crear una identificación única.',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'Utilizamos los siguientes tipos de cookies:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          _buildBulletPoint(
              'Cookies estrictamente necesarias. Estas son cookies que se requieren para el funcionamiento de nuestro sitio web y según nuestros términos con usted. Incluyen, por ejemplo, cookies que le permiten iniciar sesión en áreas seguras de nuestro sitio web, usar un carrito de compras o hacer uso de los servicios de facturación electrónica.'),
          _buildBulletPoint(
              'Cookies analíticas/de rendimiento. Nos permiten reconocer y contar el número de visitantes y ver cómo se mueven los visitantes por nuestro sitio web cuando lo están utilizando. Esto nos ayuda a nuestros intereses legítimos de mejorar la forma en que funciona nuestro sitio web, por ejemplo, al garantizar que los usuarios encuentren lo que buscan fácilmente.'),
          _buildBulletPoint(
              'Cookies de funcionalidad. Estas se utilizan para reconocerlo cuando regresa a nuestro sitio web. Esto nos permite, sujeto a sus elecciones y preferencias, personalizar nuestro contenido, saludarlo por su nombre y recordar sus preferencias (por ejemplo, su elección de idioma o región).'),
          SizedBox(height: 16),
          Text(
            'CAMBIOS AL AVISO DE PRIVACIDAD',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'EL RESPONSABLE, se reserva el derecho de modificar el presente Aviso de Privacidad como estime conveniente, de conformidad al cumplimiento de la legislación sobre la protección de datos y los intereses de EL RESPONSABLE, se le informa que el Aviso de Privacidad actualizado estará disponible en la siguiente página de internet:',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 16),
          InkWell(
            onTap: () {
              // 跳转到首页
              Get.offAllNamed(AppRoutes.HomePage);
            },
            child: Text(
              'shop.chilat.com',
              style: TextStyle(fontSize: 16, color: Colors.blue),
            ),
          ),
          SizedBox(height: 16),
          Text(
            'CONTACTO',
            style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
          ),
          SizedBox(height: 16),
          Text(
            'Cualquier duda al presente aviso de privacidad, nos puede escribir al siguiente correo electrónico <EMAIL>, número telefónico +86 15924262117 o visitarnos en nuestro domicilio ubicado en L5, Edificio A, Wangdao RD., Yiwu, Zhejiang, China.',
            style: TextStyle(fontSize: 16),
          ),
          SizedBox(height: 40), // 底部留白，避免内容被遮挡
        ],
      ),
    );
  }

  Widget _buildBulletPoint(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text('• ', style: TextStyle(fontSize: 16)),
          Expanded(child: Text(text, style: TextStyle(fontSize: 16))),
        ],
      ),
    );
  }
}
