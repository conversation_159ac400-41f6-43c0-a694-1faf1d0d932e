import 'package:flutter/material.dart';

class GoodsPrice extends StatefulWidget {
  final double price;
  final String? prefix;
  final String? suffix;
  final TextStyle? textStyle; // 添加 textStyle 属性

  const GoodsPrice(
      {super.key,
      required this.price,
      this.prefix,
      this.suffix,
      this.textStyle});

  @override
  State<GoodsPrice> createState() => _GoodsPriceState();
}

class _GoodsPriceState extends State<GoodsPrice> {
  String _integerPart = '';
  String _decimalPart = '';
  late TextStyle _textStyle;
  late TextStyle _integerStyle;

  @override
  void initState() {
    super.initState();

    String priceStr = widget.price.toString();
    List<String> parts = priceStr.split('.');
    _integerPart = parts[0];
    _decimalPart = parts[1];
    _textStyle = widget.textStyle ?? const TextStyle(fontSize: 12);
    _integerStyle = _textStyle.copyWith(
      fontSize: (_textStyle.fontSize ?? 12) + 2,
    );
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Text.rich(
        TextSpan(
          children: [
            if (widget.prefix != null)
              TextSpan(
                text: widget.prefix,
                style: _textStyle,
              ),
            TextSpan(
              text: _integerPart,
              style: _integerStyle,
            ),
            TextSpan(
              text: '.',
              style: _textStyle,
            ),
            TextSpan(
              text: _decimalPart,
              style: _textStyle,
            ),
            if (widget.suffix != null)
              TextSpan(
                text: widget.suffix,
                style: _textStyle,
              ),
          ],
        ),
      ),
    );
  }
}

// 带单位的价格组件
class GoodsPriceUnit extends StatefulWidget {
  final double minPrice; //最低价
  final double? maxPrice; //最高价
  final String? suffix; //后缀
  final bool? integerZoom; //整数部分是否放大
  final TextStyle? textStyle; // 添加 textStyle 属性

  const GoodsPriceUnit(
      {super.key,
      required this.minPrice,
      this.maxPrice,
      this.suffix,
      this.integerZoom = true,
      this.textStyle});

  @override
  State<GoodsPriceUnit> createState() => _GoodsPriceUnitState();
}

class _GoodsPriceUnitState extends State<GoodsPriceUnit> {
  String _integerMinPart = '';
  String _decimalMinPart = '';
  String _integerMaxPart = '';
  String _decimalMaxPart = '';
  late TextStyle _textStyle;
  late TextStyle _integerStyle;

  @override
  void initState() {
    super.initState();

    String minPriceStr = widget.minPrice.toString();
    List<String> minParts = minPriceStr.split('.');
    _integerMinPart = minParts[0];
    _decimalMinPart = minParts[1];
    String maxPriceStr = widget.maxPrice.toString();
    List<String> maxParts = maxPriceStr.split('.');
    _integerMaxPart = maxParts[0];
    _decimalMaxPart = maxParts[1];

    _textStyle = widget.textStyle ?? const TextStyle(fontSize: 12);
    if (widget.integerZoom == true) {
      _integerStyle = _textStyle.copyWith(
        fontSize: (_textStyle.fontSize ?? 12) + 3,
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Text.rich(
      TextSpan(
        children: [
          TextSpan(
            text: 'US\$ ',
            style: _textStyle,
          ),
          TextSpan(
            text: _integerMinPart,
            style: _integerStyle,
          ),
          TextSpan(
            text: '.',
            style: _textStyle,
          ),
          TextSpan(
            text: _decimalMinPart,
            style: _textStyle,
          ),
          if (_integerMaxPart.isNotEmpty)
            TextSpan(
              text: "-",
              style: _textStyle,
            ),
          if (_integerMaxPart.isNotEmpty)
            TextSpan(
              text: _integerMaxPart,
              style: _integerStyle,
            ),
          if (_decimalMaxPart.isNotEmpty)
            TextSpan(
              text: '.',
              style: _textStyle,
            ),
          if (_decimalMaxPart.isNotEmpty)
            TextSpan(
              text: _decimalMaxPart,
              style: _textStyle,
            ),
          if (widget.suffix != null)
            TextSpan(
              text: widget.suffix,
              style: _textStyle,
            ),
        ],
      ),
    );
  }
}
