{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9856804c33e2fecb400b8ef3376b52514f", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98519c0f1aabf34eacbf6f755b813f1496", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b93ac8ae39a05c396f03c7eee9831993", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98ec62137b0ee28ca4265944856877be27", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e98b93ac8ae39a05c396f03c7eee9831993", "buildSettings": {"CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DEFINES_MODULE": "YES", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/permission_handler_apple/permission_handler_apple-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "LD_RUNPATH_SEARCH_PATHS": "$(inherited) @executable_path/Frameworks @loader_path/Frameworks", "MACH_O_TYPE": "staticlib", "MODULEMAP_FILE": "Target Support Files/permission_handler_apple/permission_handler_apple.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "permission_handler_apple", "PRODUCT_NAME": "permission_handler_apple", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e9832ebf16d88dad2729e444c32094aa46a", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98d59d3ccda6dab576780ef3122cc46eac", "guid": "bfdfe7dc352907fc980b868725387e984f7cf89ed34758d3a81093289626eb69", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d4637978616e8741eaf6d63baaca9ef0", "guid": "bfdfe7dc352907fc980b868725387e98859209d5a3ed79c3e78a3f49862f3d1b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987e7119e8be56fbeaeca0d839e0aa764f", "guid": "bfdfe7dc352907fc980b868725387e98f45ded9ceec30b7dfe122ff9dd0d5031", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858628d063027fc071135e8a7883c8904", "guid": "bfdfe7dc352907fc980b868725387e989b689afac02d9b54922b5fec0a11c5b1", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98183b048c5be44367efc8b72b5e58aa7d", "guid": "bfdfe7dc352907fc980b868725387e9805a8f55c733fa9e84a541a07415e4ca3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f53c75e42b58140e55abdb0e4ce81d93", "guid": "bfdfe7dc352907fc980b868725387e98dc776b690cd0dfbdc895637a03f4d9e4", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986f5aed7ed116a53bb9b4c94dbc20d39c", "guid": "bfdfe7dc352907fc980b868725387e98c22656481befe64dac56bd274d68d0f9", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e980c8f129ddd527d942c09479e36bd3865", "guid": "bfdfe7dc352907fc980b868725387e98b36a9f4c6395bc6cd8eb1f9d69dfdee6", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b73934618d6fe98a2ce3fc648e43ffcf", "guid": "bfdfe7dc352907fc980b868725387e98140417c5d4fa5f911d8fc132c111b92a", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984660234cb08a46dd5d017d6c4d54831d", "guid": "bfdfe7dc352907fc980b868725387e986a722ea2e57bb5f3690c51af461247f3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ab65da116fc32c7a0e75c6e97ff9b50e", "guid": "bfdfe7dc352907fc980b868725387e982425e448466713704933ef899eaf691f", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dde3dc35ac19db309f3dc1146ba0e5d2", "guid": "bfdfe7dc352907fc980b868725387e9886ecb787f248fa5a3a1a23073074e68d", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fa1b6cea00f64700432d0650bfed2f87", "guid": "bfdfe7dc352907fc980b868725387e98dc02dbc796bf1a1c79ffc8c7bf3cea72", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984844c5759926567230e2f23fdb9ea38f", "guid": "bfdfe7dc352907fc980b868725387e9870ce5004614c61a0f2d5d90f290c1b81", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9864cad3d17cfc6aa58cf0b79e5df79541", "guid": "bfdfe7dc352907fc980b868725387e98d3f5683291790d2248fa5af90c1d56b0", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d1f6abaa13135887efe25900c1a34304", "guid": "bfdfe7dc352907fc980b868725387e98455edd93c3cff3205af94fd0a084ed18", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f631c9b1445286abcbe87b997263d01e", "guid": "bfdfe7dc352907fc980b868725387e9876a1d072b02069be5d444cda129221b3", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdff0e7390e633302bf7bbf066006864", "guid": "bfdfe7dc352907fc980b868725387e98b629aedff171c173ed1e3a0ba9e63919", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e51b09da45ddb6a45c034018ca108592", "guid": "bfdfe7dc352907fc980b868725387e98abaee0d717002e71b2e76a5da3d2be82", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98322a84800c5ee038e7b0a6260c09c425", "guid": "bfdfe7dc352907fc980b868725387e98b34142a65e8e5565a4eb185213970c7b", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e813137b50ea6c4e7c680551bc90c9de", "guid": "bfdfe7dc352907fc980b868725387e989470db41f8b1bf85ff6ea68866dcb7ee", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98695293efc1477d3b906aa90dce772811", "guid": "bfdfe7dc352907fc980b868725387e982b627d7a4f8083c498f9dfdff23fc4e5", "headerVisibility": "public"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981ac100d2234f39ae6d63bb86c16e89c3", "guid": "bfdfe7dc352907fc980b868725387e983192cedc4de0129d6ab8932bf6561751", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e986a03fa67e33d48dce94be2b8eb2259ec", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e98b1e1780ad8c3428628054e32ecc13d9e", "guid": "bfdfe7dc352907fc980b868725387e98eb85571dff8602fb3ff62ab6b20e01bb"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98cdc170a65e25d8d08e96649aaf25487d", "guid": "bfdfe7dc352907fc980b868725387e98c330906633432a369001ff99c355a814"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9884a8f8366bf0f9283fd371735cc6c47c", "guid": "bfdfe7dc352907fc980b868725387e98526eb141dfa77c2d362544da14ef6ea2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98acdc95958e2ee048de98448d8f05207b", "guid": "bfdfe7dc352907fc980b868725387e981d4f1c8081f416da2fe4fe4afa1eea0b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9834abf65ec5937fbec2334ebf3c1b7cff", "guid": "bfdfe7dc352907fc980b868725387e981eaa958d6b40e7a1bfb54d30a059afbd"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988b374ba16ddf28584f4e01ee2df99566", "guid": "bfdfe7dc352907fc980b868725387e98e5c56bd964ace111ba3f9c9b5451f97a"}, {"fileReference": "bfdfe7dc352907fc980b868725387e985d8da055c8ffdd178a9631ed55c172f9", "guid": "bfdfe7dc352907fc980b868725387e98d3fd70543a59c92a0505c4fd49c941aa"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868cf8d30f76839c5d25c79d73b2a5db5", "guid": "bfdfe7dc352907fc980b868725387e9846634925bca309d6dbfdfd68b531c38f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9868f44a7b86bfa4174fd9e0ccf917749d", "guid": "bfdfe7dc352907fc980b868725387e9885e90ee3f7d8fbe3b37ad77af732dfd5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98da3701566ebd96afc2a42b47775f591e", "guid": "bfdfe7dc352907fc980b868725387e98ed9a8b497c47fc4637965bf20f3ce31e"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98a44aeac2fe4afb1e1c282477a61d03df", "guid": "bfdfe7dc352907fc980b868725387e984e558761201d7732f913a16aea0220e3"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981fb8ed8cc0622f550a0acc598cff3e90", "guid": "bfdfe7dc352907fc980b868725387e984e7f78d0016a5f8aa9aa2b9b7fd11746"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9830868cddd4126341224705d85095ddf9", "guid": "bfdfe7dc352907fc980b868725387e981850b344567e5543096f0a83d9eac0b2"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fae8be0d4986a2d92573c02760c0a982", "guid": "bfdfe7dc352907fc980b868725387e985c069b691fd89872ad77768be56929a4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e981501413b91381ce3f4fd73de3293641e", "guid": "bfdfe7dc352907fc980b868725387e98c83a0577e94e943f655b0bafdbed9b90"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eee53ca66d929b944237d89e1fec22fb", "guid": "bfdfe7dc352907fc980b868725387e9874b019cfb7af38804db0a02bb6284102"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98b3e316f97dffbff523bf2bd2bf4b972a", "guid": "bfdfe7dc352907fc980b868725387e98c174c0037abfb7470d31b56482cffd09"}, {"fileReference": "bfdfe7dc352907fc980b868725387e987d1aae3e9473931a2103e04f67f684fa", "guid": "bfdfe7dc352907fc980b868725387e98ae2dde01ae45f5121f2b35c11e509a02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98859a7902e5a8c7669ea849407ab6d557", "guid": "bfdfe7dc352907fc980b868725387e9851cf6780d3bed6f8d0880297278c602d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f60879376c15ed401ee147c15a0615bc", "guid": "bfdfe7dc352907fc980b868725387e982b806ffb7232fe1ddc9b402367ea08c5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988409f490de932fbe2a0369fe52880f4d", "guid": "bfdfe7dc352907fc980b868725387e9821fe014ab1feadcb63c3a4fcb8598ca8"}], "guid": "bfdfe7dc352907fc980b868725387e98be6229230a4715433df2f8e74fbafc5b", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e980797c2152e50219ee4196549bb34f857"}], "guid": "bfdfe7dc352907fc980b868725387e984d290968aff9eafa4ed5b85c80a8c610", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [], "guid": "bfdfe7dc352907fc980b868725387e98fa0d11ed0b4e1a85c13d68e37d1547e0", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e9802f35ab680609a626ebd2ddd692a3822", "name": "permission_handler_apple-permission_handler_apple_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e98ef10255b706f98e1e88fae00855b0968", "name": "permission_handler_apple", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Objective-C-Plus-Plus", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e98f8f53f8ba4165e76c7481b24262177ed", "name": "permission_handler_apple.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}