import 'dart:math';

import 'package:flutter/material.dart';

class RotatingDotsIndicator extends StatefulWidget {
  final double size;
  final Widget logo;
  final Duration duration;
  final int dotCount;
  final Color dotColor;

  const RotatingDotsIndicator({
    Key? key,
    this.size = 120.0,
    required this.logo,
    this.duration = const Duration(seconds: 2),
    this.dotCount = 12,
    this.dotColor = Colors.blue,
  }) : super(key: key);

  @override
  _RotatingDotsIndicatorState createState() => _RotatingDotsIndicatorState();
}

class _RotatingDotsIndicatorState extends State<RotatingDotsIndicator>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: widget.duration,
    )..repeat();
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: widget.size,
      height: widget.size,
      child: Stack(
        alignment: Alignment.center,
        children: [
          // 旋转的点
          RotationTransition(
            turns: _controller,
            child: CustomPaint(
              size: Size(widget.size, widget.size),
              painter: DotsPainter(
                dotCount: widget.dotCount,
                dotColor: widget.dotColor,
              ),
            ),
          ),

          // 中间 Logo
          Container(
            width: widget.size * 0.65,
            height: widget.size * 0.65,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              color: Colors.white,
            ),
            child: widget.logo,
          ),
        ],
      ),
    );
  }
}

class DotsPainter extends CustomPainter {
  final int dotCount;
  final Color dotColor;

  DotsPainter({
    required this.dotCount,
    required this.dotColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final center = Offset(size.width / 2, size.height / 2);
    final radius = (size.width * 0.85) / 2; // 点的分布半径

    // 创建点的画笔
    final dotPaint = Paint()
      ..color = dotColor
      ..style = PaintingStyle.fill;

    // 绘制多个点
    for (int i = 0; i < dotCount; i++) {
      final angle = (2 * pi / dotCount) * i;
      final x = center.dx + radius * cos(angle);
      final y = center.dy + radius * sin(angle);

      // 根据位置调整点的透明度，形成动画效果
      final opacity = 1.0 - (i / dotCount);
      dotPaint.color = dotColor.withOpacity(opacity);

      canvas.drawCircle(Offset(x, y), 3.0, dotPaint);
    }
  }

  @override
  bool shouldRepaint(covariant DotsPainter oldDelegate) {
    return false;
  }
}
