// 常见问题
import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/config/config.dart';
import 'package:chilat2_mall_app/services/article.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html_core/flutter_widget_from_html_core.dart';
import 'package:get/get.dart';

class FAQPage extends StatefulWidget {
  const FAQPage({super.key});

  @override
  State<FAQPage> createState() => _FAQPageState();
}

class _FAQPageState extends State<FAQPage> {
  double screenWidth = 0.0;
  bool _isLoading = true;
  bool _showBackToTopButton = false;
  final dynamic _pageData = {};
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    onPageData();
    _scrollController.addListener(() {
      setState(() {
        _showBackToTopButton = _scrollController.offset > 200;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onScrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> onPageData([bool? scroll]) async {
    try {
      dynamic res = await ArticleAPI.useArticleDetail({
        "id": Get.arguments?["id"],
        "title": Get.arguments?["title"],
        "articleCode": Get.arguments?["code"],
      });
      if (res?['result']?['code'] == 200) {
        setState(() {
          _pageData['articleDetail'] = res?['data'];
        });
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.white,
            body: Stack(children: [
              Container(
                height: 32,
                padding: EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.black26,
                      width: 0.3,
                    ),
                  ),
                ),
                child: Center(
                  child: CachedNetworkImage(
                    imageUrl: APP_LOGO,
                    width: 128,
                    height: 24,
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              Container(
                height: 48,
                margin: EdgeInsets.only(top: 32),
                padding: EdgeInsets.symmetric(vertical: 8),
                decoration: BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Colors.black26,
                      width: 0.3,
                    ),
                  ),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    Center(
                      child: IconButton(
                        icon: const Icon(
                          Icons.arrow_back_ios,
                          size: 20,
                        ),
                        onPressed: () {
                          Get.back();
                        },
                      ),
                    ),
                    Expanded(
                      child: Center(
                        child: Text(
                          Get.arguments?['columnTitle'],
                          style: TextStyle(
                              fontSize: 16, color: Colors.grey.shade800),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 88),
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                        child: Text(
                          _pageData?['articleDetail']?['title'],
                          style: TextStyle(
                              fontSize: 16, fontWeight: FontWeight.w300),
                        ),
                      ),
                      Container(
                        padding:
                            EdgeInsets.only(left: 16, right: 16, bottom: 128),
                        child: HtmlWidget(
                          _pageData?['articleDetail']?['content'],
                          textStyle: TextStyle(color: Colors.grey.shade700),
                        ),
                      ),
                      MyFooter()
                    ],
                  ),
                ),
              ),
              if (_showBackToTopButton)
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: FloatingActionButton(
                    onPressed: onScrollToTop,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(64),
                    ),
                    child: const Icon(Icons.arrow_upward),
                  ),
                ),
            ]),
          );
  }
}
