import 'dart:ui';
import 'package:flutter/material.dart';

class AppColors {
  /// 主背景
  static const Color primaryBackground = Color(0xFFFFFFFF);

  /// 主文本
  static const Color primaryText = Color(0xFFE50113);

  /// 主文本灰色
  static const Color primaryGreyText = Color(0xFF767676);

  /// 主文本灰色
  static const Color primaryGreyText1 = Color(0xFFE0DDF5);

  /// tabBar 默认颜色 灰色
  static const Color tabBarElement = Color(0xFFE0DDF5);

  /// tabBar 激活颜色
  static const Color tabBarActive = Color(0xFF8B63E6);

  /// 分类tab渐变色
  static Color buttonLine1 = Color(0xFFBA68C8);

  /// 分类tab渐变色
  static const Color buttonLine2 = Color(0xFF7265E3);

  /// 主题色
  static const Color primaryColor = Color(0xFFE50113);

  static const Color primaryColorAccent = Color(0xFFE1DDF5);

  /// 价格颜色
  static const Color priceColor = Color(0xFFF77777);

  /// 提货方式-自提
  static const Color deliveryColor1 = Color(0xFFFE9C5E);

  /// 提货方式-物流
  static const Color deliveryColor2 = Color(0xFF6155CC);

  /// 提货方式-自提
  static const Color deliveryBackColor1 = Color.fromRGBO(254, 156, 94, 0.15);

  /// 提货方式-物流
  static const Color deliveryBackColor2 = Color.fromRGBO(97, 85, 204, 0.15);

  /// 加入购物车
  static const Color addToCart2 = Color(0xFFFE9C5E);
  static const Color addToCart1 = Color(0xFFfdcb6e);

  /// 立即采购
  static const Color buyNow1 = Color(0xFFF77777);
  static const Color buyNow2 = Color(0xFFd63031);

  /// splashColor
  static const Color splashColor = Color(0xFFE1DDF5);

  /// 店铺背景渐变
  static const Color supplierColor1 = Color(0xFF8B63E6);
  static const Color supplierColor2 = Color(0xFF7265E3);
}
