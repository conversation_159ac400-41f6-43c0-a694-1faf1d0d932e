import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/global.dart';
import 'package:chilat2_mall_app/components/auth_modal.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';

// 导出 AuthPageType 枚举以便外部使用
export 'package:chilat2_mall_app/components/auth_modal.dart' show AuthPageType;

/// 认证助手类
///
/// 提供统一的认证相关工具函数，如显示模态框、检查登录状态等
class AuthHelper {
  // 防止重复弹出登录框的标志
  static bool _isShowingAuthModal = false;

  /// 显示认证模态框
  ///
  /// 参数:
  /// - context: 当前上下文
  /// - initialType: 初始显示的页面类型，默认为登录页面
  /// - redirectRoute: 认证成功后的重定向路由，如果为空则停留在当前页面
  ///
  /// 返回 Future<bool>，认证成功返回 true，否则返回 false
  static Future<bool> showAuthModal(
    BuildContext context, {
    AuthPageType initialType = AuthPageType.login,
    String? redirectRoute,
    VoidCallback? onAuthSuccess,
  }) async {
    // 防止重复弹出登录框
    if (_isShowingAuthModal) {
      print('认证模态框已在显示中，跳过重复弹出');
      return false;
    }

    _isShowingAuthModal = true;

    // 使用底部弹出sheet的方式显示认证页面
    final result = await showModalBottomSheet<bool>(
      context: context,
      isScrollControlled: true, // 允许内容撑开到需要的高度
      backgroundColor: Colors.white, // 白色背景
      isDismissible: false, // 禁止点击外部关闭
      enableDrag: false, // 禁止拖动关闭
      // useSafeArea: true, // 确保与顶部保持安全距离
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(0)), // 移除圆角
      ),
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height, // 设置最大高度为屏幕高度
      ),
      builder: (BuildContext context) {
        return AuthModal(
          initialType: initialType,
          redirectRoute: redirectRoute,
          onAuthSuccess: onAuthSuccess,
        );
      },
    ).whenComplete(() {
      // 模态框关闭时重置标志
      _isShowingAuthModal = false;
    });

    // 如果模态框被关闭且用户未登录，检查当前路由是否为AuthBlockPage
    if ((result == null || result == false) && !Global.isLogin.value) {
      final currentRoute = Get.currentRoute;
      if (currentRoute == AppRoutes.AuthBlockPage) {
        // 如果当前在AuthBlockPage，返回到上一个页面
        if (Get.routing.previous.isNotEmpty) {
          Get.back();
        } else {
          // 如果没有上一个页面，跳转到首页
          Get.offAllNamed(AppRoutes.HomePage);
        }
      }
    }

    return result ?? false;
  }

  /// 显示登录模态框
  ///
  /// 参数:
  /// - context: 当前上下文
  /// - redirectRoute: 登录成功后的重定向路由
  ///
  /// 返回 Future<bool>，登录成功返回 true，否则返回 false
  static Future<bool> showLoginModal(
    BuildContext context, {
    String? redirectRoute,
    VoidCallback? onAuthSuccess,
  }) async {
    return showAuthModal(
      context,
      initialType: AuthPageType.login,
      redirectRoute: redirectRoute,
      onAuthSuccess: onAuthSuccess,
    );
  }

  /// 显示注册模态框
  ///
  /// 参数:
  /// - context: 当前上下文
  /// - redirectRoute: 注册成功后的重定向路由
  ///
  /// 返回 Future<bool>，注册成功返回 true，否则返回 false
  static Future<bool> showRegisterModal(
    BuildContext context, {
    String? redirectRoute,
    VoidCallback? onAuthSuccess,
  }) async {
    return showAuthModal(
      context,
      initialType: AuthPageType.register,
      redirectRoute: redirectRoute,
      onAuthSuccess: onAuthSuccess,
    );
  }

  /// 显示修改密码模态框
  ///
  /// 参数:
  /// - context: 当前上下文
  /// - redirectRoute: 修改密码成功后的重定向路由
  ///
  /// 返回 Future<bool>，修改密码成功返回 true，否则返回 false
  static Future<bool> showModifyPasswordModal(
    BuildContext context, {
    String? redirectRoute,
    VoidCallback? onAuthSuccess,
  }) async {
    return showAuthModal(
      context,
      initialType: AuthPageType.resetPwd,
      redirectRoute: redirectRoute,
      onAuthSuccess: onAuthSuccess,
    );
  }

  /// 要求认证，先检查是否已登录，如未登录则显示登录模态框
  ///
  /// 参数:
  /// - context: 当前上下文
  /// - redirectRoute: 登录成功后的重定向路由
  ///
  /// 返回 Future<bool>，已登录或登录成功返回 true，否则返回 false
  static Future<bool> requireAuth(
    BuildContext context, {
    String? redirectRoute,
  }) async {
    // 检查登录状态
    if (Global.isLogin.value) {
      return true;
    }

    // 未登录，显示登录模态框
    return showLoginModal(context, redirectRoute: redirectRoute);
  }
}
