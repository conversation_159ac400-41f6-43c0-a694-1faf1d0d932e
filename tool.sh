#! /bin/bash

APIDOC=./apidoc/proto

generateProto() {
    echo "arg is:" $@
    mkdir -p lib/model

    if [ -d $1 ]; then
        dir=`ls $1`
        for i in $dir;
        do
            echo "arg is:" $1/$i
            generateProto $1/$i
        done
    else
        if [[ $1 == *.proto ]]; then
            protoc --dart_out=:lib/model $1 -I $APIDOC
        fi
    fi
}

projectPublish() {
    # flutter clean
    flutter pub get
    flutter build apk --release
    adb install build\\app\\outputs\\flutter-apk\\app-release.apk
}

case $1 in
    "-h" | "--help")
        printHelp
    ;;
    "-gd" | "--gen-dart")
        generateProto $APIDOC\/$2
    ;;
    "-p" | "--publish")
        projectPublish
    ;;
    *)
        printHelp
    ;;
esac
