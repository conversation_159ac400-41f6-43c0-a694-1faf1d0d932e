import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class SignUpOfferWidget extends StatelessWidget {
  final Function() onSignUpOfferClose;
  const SignUpOfferWidget({super.key, required this.onSignUpOfferClose});

  void _toggleOverlay() {
    onSignUpOfferClose();
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Center(
          child: Container(
            width: 300.sp,
            height: 364.sp,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2.sp),
              boxShadow: [
                BoxShadow(
                  color: Colors.white.withOpacity(0.3),
                  blurRadius: 20.sp,
                  spreadRadius: 5.sp,
                )
              ],
              image: const DecorationImage(
                image: AssetImage('assets/icons/home/<USER>'),
                fit: BoxFit.cover,
              ),
            ),
          ),
        ),

        // 3. 关闭按钮（右上角）
        Positioned(
          top: 126.sp,
          right: 28.sp,
          child: IconButton(
            icon: Icon(Icons.close, color: Colors.white, size: 40.sp),
            onPressed: _toggleOverlay,
          ),
        ),

        // 4. 底部操作按钮（可选）
        Positioned(
          top: 396.sp,
          left: 0,
          right: 0,
          child: Center(
            child: FancyButton(
              onTap: () {
                AuthHelper.showLoginModal(
                  context,
                  redirectRoute: AppRoutes.HomePage, // 可选，登录成功后的重定向路由
                  onAuthSuccess: () {
                    // 可选，登录成功后的回调函数
                    print('登录成功');
                  },
                );
              },
              width: 280.sp,
              color: Colors.white,
              borderRadius: BorderRadius.circular(20.sp),
              padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 6.sp),
              child: Text(
                  I18n.of(context)!.translate('cm_common.createAccount'),
                  style:
                      TextStyle(fontSize: 18, color: AppColors.primaryColor)),
            ),
          ),
        ),
        Positioned(
          top: 448.sp,
          left: 0,
          right: 0,
          child: Center(
            child: RichText(
              text: TextSpan(
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18.sp,
                  height: 1.5.sp, // 行高
                  fontWeight: FontWeight.w500,
                ),
                children: [
                  TextSpan(
                    text: I18n.of(context)!.translate('cm_common.haveAccount'),
                  ),
                  TextSpan(text: ' '),
                  TextSpan(
                    text: I18n.of(context)!.translate('cm_common.loginAccount'),
                    style: TextStyle(
                      color: Colors.white, // 链接通常用蓝色
                      decoration: TextDecoration.underline, // 下划线
                      decorationColor: Colors.white, // 下划线颜色
                      decorationThickness: 1.2.sp, // 下划线厚度
                    ),
                    recognizer: TapGestureRecognizer()
                      ..onTap = () {
                        AuthHelper.showLoginModal(
                          context,
                          redirectRoute: AppRoutes.HomePage, // 可选，登录成功后的重定向路由
                          onAuthSuccess: () {
                            // 可选，登录成功后的回调函数
                            print('登录成功');
                          },
                        );
                      },
                  ),
                ],
              ),
            ),
          ),
        )
      ],
    );
  }
}
