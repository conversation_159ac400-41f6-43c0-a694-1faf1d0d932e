import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

class MineSettingPage extends StatelessWidget {
  const MineSettingPage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF2F2F2),
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          I18n.of(context)?.translate('cm_user.setting') ?? '设置',
        ),
      ),
      body: Padding(
        padding: EdgeInsets.all(10.sp),
        child: ListView(
          children: [
            InkWell(
              onTap: () {
                Get.toNamed(AppRoutes.UpdatePassword);
              },
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(8.r),
                ),
                padding: EdgeInsets.symmetric(
                  vertical: 15.sp,
                  horizontal: 15.sp,
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.lock_outline,
                      color: AppColors.primaryColor,
                      size: 23.sp,
                    ),
                    SizedBox(width: 8.sp),
                    Text(
                      I18n.of(context)?.translate('cm_user.updatePwd') ??
                          '修改密码',
                      style: TextStyle(
                        fontSize: 16.sp,
                        height: 1.5,
                        color: Colors.black,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
