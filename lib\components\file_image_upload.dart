import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import 'package:dio/dio.dart';

import 'dart:typed_data';

class FileImageUpload extends StatefulWidget {
  final double? width;
  final double? height;
  final List<String> images;
  final Function(List<String>) onChanged;

  const FileImageUpload(
      {super.key,
      required this.images,
      this.width = 80,
      this.height = 80,
      required this.onChanged});

  @override
  State<FileImageUpload> createState() => _FileImageUploadState();
}

class _FileImageUploadState extends State<FileImageUpload> {
  final ImagePicker _picker = ImagePicker();
  List<File> _images = [];
  final double _itemSize = 80.0; // 统一设置图片和按钮尺寸

  @override
  void initState() {
    super.initState();
    setState(() {
      _images = widget.images.map((path) => File(path)).toList();
    });
  }

  // 选择图片
  Future<void> _pickImages() async {
    final XFile? pickedFile =
        await _picker.pickImage(source: ImageSource.gallery);
    if (pickedFile != null) {
      // setState(() {
      //   _image = File(pickedFile.path);
      // });
      await _uploadImage(File(pickedFile.path));
    }
  }

  Future<void> _uploadImage(File file) async {
    try {
      final fileStream = file.openRead();
      final multipartFile = MultipartFile(
        fileStream,
        await file.length(),
        filename: 'upload_${DateTime.now().millisecondsSinceEpoch}.jpg',
      );

      dynamic res = await BasisAPI.useUploadStream(
          FormData.fromMap({'file': multipartFile}));
      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          _images.add(File(res['data']));
          widget.onChanged(_images.map((file) => file.path).toList());
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 删除图片
  void _removeImage(int index) {
    setState(() {
      _images.removeAt(index);
    });
  }

  // 构建单个图片项
  Widget _buildImageItem(File file, int index) {
    return Padding(
      padding: const EdgeInsets.all(4.0),
      child: Stack(
        children: [
          Container(
            width: _itemSize,
            height: _itemSize,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              color: Colors.grey[200],
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                file.path,
                width: widget.width,
                height: widget.height,
                fit: BoxFit.cover,
              ),
            ),
          ),
          Positioned(
            right: 4,
            top: 4,
            child: GestureDetector(
              onTap: () => _removeImage(index),
              child: Container(
                decoration: BoxDecoration(
                  color: Colors.black54,
                  shape: BoxShape.circle,
                ),
                child: Icon(Icons.close, size: 20, color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 构建上传按钮
  Widget _buildUploadButton() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4.0),
      child: GestureDetector(
        onTap: _pickImages,
        child: Container(
          width: _itemSize,
          height: _itemSize,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey),
          ),
          child: Icon(Icons.add_photo_alternate, size: 40, color: Colors.grey),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      child: Wrap(
        spacing: 2.0, // 水平间距
        runSpacing: 2.0, // 垂直间距
        children: [
          ..._images
              .asMap()
              .entries
              .map((entry) => _buildImageItem(entry.value, entry.key)),
          _buildUploadButton(),
        ],
      ),
    );
  }
}
