import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/env.dart';
import 'mine_controller.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';

class MineInvitePage extends StatefulWidget {
  const MineInvitePage({super.key});

  @override
  State<MineInvitePage> createState() => _MineInvitePageState();
}

class _MineInvitePageState extends State<MineInvitePage> {
  late final MineController controller;
  final RxBool isVerified = false.obs;
  final RxInt selectedPeriod = 0.obs;
  final RxList<Map<String, dynamic>> invitedUsers =
      <Map<String, dynamic>>[].obs;
  final ScrollController scrollController = ScrollController();
  final RxBool isMailVerified = false.obs;
  final RxBool verifyLoading = false.obs;
  final RxMap<String, dynamic> pageInfo = {
    'current': 1,
    'size': 10,
    'total': 0,
  }.obs;
  final RxInt verifySelect = 0.obs;
  final RxString selectDate = '0'.obs;
  final RxInt datetime = 0.obs;

  String get inviteLink {
    String baseUrl;
    if (isProd) {
      // 生产环境
      baseUrl = "https://shop.chilat.com/?utm_source=invite_code_";
    } else {
      // 测试环境
      baseUrl =
          "http://chilat.mall.dev231.xpormayor.com.mx/?utm_source=invite_code_";
    }
    return baseUrl + (controller.userInfo.value?["inviteCode"] ?? "");
  }

  @override
  void initState() {
    super.initState();
    controller = Get.isRegistered<MineController>()
        ? Get.find<MineController>()
        : Get.put(MineController());

    scrollController.addListener(_onScrollBottom);
    _fetchUserDetail();
    _onSelectDateUpdate(selectDate.value);
    _fetchInviteList();
    _checkMailVerification();
  }

  @override
  void dispose() {
    scrollController.removeListener(_onScrollBottom);
    scrollController.dispose();
    super.dispose();
  }

  void _onScrollBottom() {
    if (verifyLoading.value ||
        pageInfo['current'] * pageInfo['size'] >= pageInfo['total']) return;
    if (scrollController.position.pixels >=
        scrollController.position.maxScrollExtent - 50) {
      pageInfo['current']++;
      _fetchInviteList(isScroll: true);
    }
  }

  Future<void> _handleMailVerification(BuildContext context) async {
    if (verifyLoading.value) return;
    verifyLoading.value = true;

    try {
      final res = await UserAPI.useSendVerifyMail({
        'verifyMailScene': 'INVITE_FRIEND',
      });

      if (res['result']['code'] == 200) {
        if (res['data']['isMailVerified'] == true) {
          Get.forceAppUpdate();
        } else {
          await navigateToEmail();
        }
      } else {
        Get.snackbar(
          'Error',
          res['result']['message'] ??
              I18n.of(context)?.translate('cm_find.errorMessage') ??
              '发送失败',
          backgroundColor: Colors.red,
          colorText: Colors.white,
        );
      }
    } catch (e) {
      Get.snackbar(
        'Error',
        e.toString(),
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    } finally {
      verifyLoading.value = false;
    }
  }

  Future<void> _fetchUserDetail() async {
    try {
      final res = await UserAPI.useUserDetail({}, handleAuthErrors: false);
      if (res['result']['code'] == 200) {
        controller.userInfo.value = res['data'];
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> _checkMailVerification() async {
    try {
      final res = await UserAPI.useQueryVerifyMailResult({
        'email': controller.userInfo.value?['username'],
        'isNeedCoupon': false,
      });
      if (res['result']['code'] == 200) {
        isMailVerified.value = res['data']['isMailVerified'] ?? false;
      }
    } catch (e) {
      print(e);
    }
  }

  Future<void> _fetchInviteList({bool isScroll = false}) async {
    try {
      int dateValue = 0;
      try {
        dateValue = int.parse(selectDate.value);
      } catch (e) {
        print('Error parsing date value: $e');
      }
      final Map<String, dynamic> params = {
        'datetime': datetime.value,
        'isMailVerified': verifySelect.value == 0,
        'page': {
          'current': pageInfo['current'],
          'size': pageInfo['size'],
        },
      };
      print('-------------params:${params}');
      final res = await UserAPI.useInvitedUserMailStatus(params);
      print('-------------res:${res}');

      if (res['result']['code'] == 200) {
        if (isScroll) {
          if (res['data']?.isNotEmpty) {
            invitedUsers.addAll(List<Map<String, dynamic>>.from(res['data']));
          }
        } else {
          invitedUsers.value =
              List<Map<String, dynamic>>.from(res['data'] ?? []);
          print('-------------invitedUsers:${invitedUsers.value}');
          pageInfo['total'] = res['page']['total'] ?? 0;
        }
      }
    } catch (e) {
      print(e);
    }
  }

  int _getDatetime(int period) {
    if (period == 0) return 0; // 全部

    final now = DateTime.now();
    final currentDate = DateTime(now.year, now.month, now.day, 0, 0, 0, 0);
    final pastDate = DateTime(currentDate.year, currentDate.month - period,
        currentDate.day, 0, 0, 0, 0);
    return pastDate.millisecondsSinceEpoch;
  }

  void _handleCopyText(String text) {
    Clipboard.setData(ClipboardData(text: text));
    Get.snackbar(
      'Success',
      I18n.of(context)?.translate('cm_common.copied') ?? '已复制到剪贴板',
      backgroundColor: Colors.green,
      colorText: Colors.white,
    );
  }

  void _onVerifyClick(int index) {
    if (verifySelect.value != index) {
      verifySelect.value = index;
      pageInfo['current'] = 1;
      _fetchInviteList();
    }
  }

  void _onSelectDateUpdate(String value) {
    print('-------------value:${value}');
    selectDate.value = value;
    pageInfo['current'] = 1;

    if (value == '0') {
      datetime.value = _getDatetime(1);
    } else if (value == '1') {
      datetime.value = _getDatetime(3);
    } else if (value == '2') {
      datetime.value = _getDatetime(6);
    } else if (value == '3') {
      datetime.value = _getDatetime(12);
    } else {
      datetime.value = 0;
    }
    print('-------------datetime.value:${datetime.value}');

    _fetchInviteList();
  }

  @override
  Widget build(BuildContext context) {
    final List<Map<String, dynamic>> dateOpts = [
      {
        "label": I18n.of(context)?.translate("cm_invite.lastMonth"),
        "value": "0",
      },
      {
        "label": I18n.of(context)?.translate("cm_invite.pastThreeMonths"),
        "value": "1",
      },
      {
        "label": I18n.of(context)?.translate("cm_invite.pastSixMonths"),
        "value": "2",
      },
      {
        "label": I18n.of(context)?.translate("cm_invite.withinOneYear"),
        "value": "3",
      },
      {
        "label": I18n.of(context)?.translate("cm_invite.previous"),
        "value": "4",
      },
    ];
    return AppScaffold(
      backgroundColor: const Color(0xFFFFFFFF),
      appBar: AppBar(
        backgroundColor: Colors.white,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.black),
          onPressed: () => Navigator.pop(context),
        ),
        title: Text(
          I18n.of(context)?.translate('cm_user.invite') ?? 'Invitar amigos',
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: EdgeInsets.all(8.0.sp),
        child: SingleChildScrollView(
          child: Obx(
            () => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 标题和描述
                Padding(
                  padding: EdgeInsets.only(
                      left: 16.0.sp, right: 16.0.sp, bottom: 12.0.sp),
                  child: Container(
                    width: double.infinity,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.center,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          I18n.of(context)
                                  ?.translate('cm_invite.inviteFriendTitle') ??
                              '¡Compártelo con tus amigos y disfruta de más descuentos!',
                          style: TextStyle(
                              fontSize: 20.sp,
                              color: Color(0xFF333333),
                              fontWeight: FontWeight.w500),
                          textAlign: TextAlign.center,
                        ),
                        SizedBox(height: 8.sp),
                        Text(
                          I18n.of(context)
                                  ?.translate('cm_invite.inviteFriendDesc') ??
                              '¡Cuando su amigo se registre a través de su enlace exclusivo y verifique su correo electrónico, ¡ambos recibiréis un cupón!',
                          style: TextStyle(
                            fontSize: 14.sp,
                            color: Color(0xFF333333),
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ],
                    ),
                  ),
                ),

                // 自己获得的奖励
                Container(
                  padding: EdgeInsets.symmetric(vertical: 6.sp),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 10.sp),
                        child: SvgPicture.asset(
                          'assets/images/mine/inviteCouponSelf.svg',
                          width: 40.sp,
                          height: 40.sp,
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              I18n.of(context)
                                      ?.translate('cm_invite.selfGain') ??
                                  'Su puedes obtener un cupón de comisión ilimitada de US \$10.',
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(height: 4.sp),
                            Text(
                              I18n.of(context)?.translate(
                                      'cm_invite.selfGainUseDesc') ??
                                  'Los cupones de descuento solo se pueden deducir de las comisiones, que puede utilizar en pedidos posteriores.',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // 好友获得的奖励
                Container(
                  padding: EdgeInsets.symmetric(vertical: 6.sp),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        margin: EdgeInsets.only(right: 10.sp),
                        child: SvgPicture.asset(
                          'assets/images/mine/inviteCouponOther.svg',
                          width: 40.sp,
                          height: 40.sp,
                        ),
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              I18n.of(context)
                                      ?.translate('cm_invite.friendGain') ??
                                  'Ellos recibirán un cupón de comisión sin mínimo de US \$15.',
                              style: TextStyle(
                                  fontSize: 16.sp, fontWeight: FontWeight.w500),
                            ),
                            SizedBox(height: 4.sp),
                            Text(
                              I18n.of(context)?.translate(
                                      'cm_invite.friendGainUseDesc') ??
                                  'Los cupones de descuento solo se pueden deducir de las comisiones, que pueden utilizar en su primer pedido.',
                              style: TextStyle(
                                fontSize: 14.sp,
                                color: Colors.grey[600],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),

                // 邮箱验证提示或邀请链接
                if (!isMailVerified.value)
                  Container(
                    margin: EdgeInsets.symmetric(vertical: 16.0.sp),
                    decoration: BoxDecoration(
                      color: const Color(0xFFF2F2F2),
                      borderRadius: BorderRadius.circular(8.sp),
                    ),
                    child: Padding(
                      padding: EdgeInsets.all(12.sp),
                      child: Column(
                        children: [
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SvgPicture.asset(
                                'assets/images/mine/inviteWarning.svg',
                                width: 36.sp,
                                height: 36.sp,
                              ),
                              SizedBox(width: 6.sp),
                              Expanded(
                                child: Text(
                                  I18n.of(context)?.translate(
                                          'cm_invite.verificationMailbox') ??
                                      'Verifica su correo electrónico registrada para invitar a clientes, su correo electrónico registrada no ha sido verificada.',
                                  style: TextStyle(fontSize: 14.sp),
                                ),
                              ),
                            ],
                          ),
                          SizedBox(height: 10.sp),
                          ElevatedButton(
                            onPressed: () => _handleMailVerification(context),
                            style: ElevatedButton.styleFrom(
                              fixedSize: Size.fromHeight(34.sp),
                              backgroundColor: AppColors.primaryColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(20.sp),
                              ),
                              padding: EdgeInsets.symmetric(horizontal: 30.sp),
                            ),
                            child: Text(
                              I18n.of(context)
                                      ?.translate('cm_invite.viewNow') ??
                                  'Verificar ahora',
                              style: TextStyle(
                                  fontSize: 16.sp,
                                  color: AppColors.primaryBackground),
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                else
                  Column(
                    children: [
                      // 邀请链接
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 8.0.sp),
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.0.sp, vertical: 14.0.sp),
                        decoration: BoxDecoration(
                          color: const Color(0xFFF2F2F2),
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              '${I18n.of(context)?.translate('cm_user.inviteLink') ?? 'Enlace de invitación'}:',
                              style: TextStyle(
                                fontSize: 16.sp,
                                height: 1.0,
                                color: Colors.grey[600],
                              ),
                            ),
                            SizedBox(height: 2.sp),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Expanded(
                                  child: Text(
                                    inviteLink,
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black87,
                                    ),
                                    softWrap: true,
                                    maxLines: null,
                                  ),
                                ),
                                SizedBox(
                                  width: 68.sp,
                                  height: 30.sp,
                                  child: ElevatedButton(
                                    onPressed: () =>
                                        _handleCopyText(inviteLink),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: const Color(0xFFF6D2D4),
                                      foregroundColor: AppColors.primaryColor,
                                      shape: RoundedRectangleBorder(
                                        borderRadius:
                                            BorderRadius.circular(20.sp),
                                      ),
                                      minimumSize: Size(double.infinity, 28.sp),
                                      padding: EdgeInsets.symmetric(
                                          horizontal: 16.sp, vertical: 4.sp),
                                    ),
                                    child: Text(
                                      I18n.of(context)
                                              ?.translate('cm_user.copy') ??
                                          '复制',
                                      style: TextStyle(fontSize: 12.sp),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ),

                      // 邀请用户列表
                      Container(
                        margin: EdgeInsets.symmetric(vertical: 12.sp),
                        padding: EdgeInsets.symmetric(horizontal: 16.sp),
                        decoration: BoxDecoration(
                          color: const Color(0xFFFAFAFA),
                          borderRadius: BorderRadius.circular(8.sp),
                        ),
                        child: Column(
                          children: [
                            // 验证状态筛选
                            Row(
                              children: [
                                TextButton(
                                  onPressed: () => _onVerifyClick(0),
                                  child: Text(
                                    I18n.of(context)
                                            ?.translate('cm_invite.verify') ??
                                        'verificado',
                                    style: TextStyle(
                                      fontSize: verifySelect.value == 0
                                          ? 16.sp
                                          : 14.sp,
                                      fontWeight: verifySelect.value == 0
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                      color: Colors.black,
                                      decoration: verifySelect.value == 0
                                          ? TextDecoration.underline
                                          : TextDecoration.none,
                                      decorationThickness: 2.sp,
                                      decorationColor: Colors.black,
                                    ),
                                  ),
                                ),
                                const VerticalDivider(
                                  width: 1,
                                  thickness: 1,
                                  indent: 8,
                                  endIndent: 8,
                                  color: Colors.grey,
                                ),
                                TextButton(
                                  onPressed: () => _onVerifyClick(1),
                                  child: Text(
                                    I18n.of(context)!
                                        .translate('cm_invite.notVerify'),
                                    style: TextStyle(
                                      fontSize: verifySelect.value == 1
                                          ? 16.sp
                                          : 14.sp,
                                      fontWeight: verifySelect.value == 1
                                          ? FontWeight.w600
                                          : FontWeight.normal,
                                      color: Colors.black,
                                      decoration: verifySelect.value == 1
                                          ? TextDecoration.underline
                                          : TextDecoration.none,
                                      decorationThickness: 2.sp,
                                      decorationColor: Colors.black,
                                    ),
                                  ),
                                ),
                              ],
                            ),

                            // 时间筛选
                            Container(
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(4.sp),
                              ),
                              child: DropdownButton<String>(
                                value: selectDate.value,
                                isExpanded: true,
                                isDense: true,
                                padding: EdgeInsets.symmetric(
                                    horizontal: 10.sp, vertical: 6.sp),
                                underline: const SizedBox(),
                                onChanged: (value) {
                                  if (value != null) {
                                    _onSelectDateUpdate(value);
                                  }
                                },
                                items: dateOpts.map((item) {
                                  return DropdownMenuItem<String>(
                                    value: item['value'],
                                    child: Text(item['label']),
                                  );
                                }).toList(),
                              ),
                            ),

                            // 用户列表
                            Container(
                              margin: EdgeInsets.symmetric(vertical: 8.sp),
                              height: 200.sp,
                              decoration: BoxDecoration(
                                border: Border.all(color: Colors.grey[300]!),
                                borderRadius: BorderRadius.circular(4.sp),
                              ),
                              child: Column(
                                children: [
                                  // 表头
                                  Container(
                                    padding: EdgeInsets.symmetric(
                                        vertical: 8.sp, horizontal: 8.sp),
                                    decoration: BoxDecoration(
                                      color: Colors.grey[100],
                                      border: Border(
                                          bottom: BorderSide(
                                              color: Colors.grey[300]!)),
                                    ),
                                    child: Row(
                                      children: [
                                        Expanded(
                                          flex: 3,
                                          child: Text(
                                            I18n.of(context)?.translate(
                                                    'cm_invite.registerTime') ??
                                                'Fecha de registro',
                                            style: TextStyle(fontSize: 13.sp),
                                          ),
                                        ),
                                        Expanded(
                                          flex: 4,
                                          child: Text(
                                            I18n.of(context)?.translate(
                                                    'cm_invite.friendEmail') ??
                                                'Correo electrónico del amigo',
                                            style: TextStyle(fontSize: 13.sp),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ),
                                  // 数据行
                                  Expanded(
                                    child: invitedUsers.isEmpty
                                        ? Center(
                                            child: Text(
                                              I18n.of(context)?.translate(
                                                      'cm_invite.notVerify') ??
                                                  'No Data',
                                              style: TextStyle(
                                                  color: Colors.grey[600]),
                                            ),
                                          )
                                        : ListView.builder(
                                            controller: scrollController,
                                            itemCount: invitedUsers.length +
                                                (verifyLoading.value ? 1 : 0),
                                            itemBuilder: (context, index) {
                                              if (index ==
                                                  invitedUsers.length) {
                                                return Center(
                                                  child: Padding(
                                                    padding:
                                                        EdgeInsets.all(16.sp),
                                                    child:
                                                        CircularProgressIndicator(
                                                      strokeWidth: 2,
                                                      valueColor:
                                                          AlwaysStoppedAnimation<
                                                                  Color>(
                                                              AppColors
                                                                  .primaryColor),
                                                    ),
                                                  ),
                                                );
                                              }

                                              final user = invitedUsers[index];
                                              return Container(
                                                padding: EdgeInsets.symmetric(
                                                    vertical: 12.sp,
                                                    horizontal: 12.sp),
                                                decoration: BoxDecoration(
                                                  color: index % 2 == 0
                                                      ? Colors.white
                                                      : Colors.grey[50],
                                                  border: Border(
                                                    bottom: BorderSide(
                                                        color:
                                                            Colors.grey[200]!,
                                                        width: 1),
                                                  ),
                                                ),
                                                child: Row(
                                                  children: [
                                                    Expanded(
                                                      flex: 3,
                                                      child: Text(
                                                        user['registerTime'] !=
                                                                null
                                                            ? timeFormatByZone(
                                                                user[
                                                                    'registerTime'],
                                                                showSeconds:
                                                                    false)
                                                            : '',
                                                        style: TextStyle(
                                                            fontSize: 13.sp,
                                                            color: Colors
                                                                .grey[800]),
                                                      ),
                                                    ),
                                                    Expanded(
                                                      flex: 4,
                                                      child: Text(
                                                        user['email'] ?? '',
                                                        style: TextStyle(
                                                            fontSize: 13.sp),
                                                        overflow: TextOverflow
                                                            .ellipsis,
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              );
                                            },
                                          ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
