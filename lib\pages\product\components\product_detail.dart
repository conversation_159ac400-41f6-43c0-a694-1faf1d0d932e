import 'package:flutter/material.dart';
import 'package:flutter_html/flutter_html.dart';

class ProductDetailPage extends StatelessWidget {
  final String htmlContent;

  const ProductDetailPage({super.key, required this.htmlContent});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('商品详情')),
      body: SingleChildScrollView(
        child: Html(
          data: htmlContent,
          // 可定制渲染器
          style: {
            "img": Style(margin: Margins.symmetric(vertical: 10)),
            'h1': Style(
                fontSize: FontSize.larger,
                fontWeight: FontWeight.bold,
                margin: Margins(top: Mar<PERSON>(10), bottom: Margin(10))),
            'h2': Style(
                fontSize: FontSize.large,
                fontWeight: FontWeight.bold,
                margin: Margins(top: Margin(10), bottom: Margin(10))),
            'p': Style(margin: Margins(top: Mar<PERSON>(10), bottom: <PERSON><PERSON>(10))),
            'ul': Style(margin: <PERSON><PERSON>(top: <PERSON><PERSON>(10), bottom: <PERSON><PERSON>(10))),
            'li': Style(margin: Mar<PERSON>(top: <PERSON><PERSON>(5), bottom: Margin(5))),
            'strong': Style(fontWeight: FontWeight.bold),
            'em': Style(fontStyle: FontStyle.italic),
            'table': Style(
              border: Border(bottom: BorderSide(color: Colors.grey, width: 1)),
              margin: Margins(top: Margin(10), bottom: Margin(10)),
            ),
            'th': Style(
              border: Border(
                  right: BorderSide(color: Colors.grey, width: 1),
                  top: BorderSide(color: Colors.grey, width: 1)),
              padding: HtmlPaddings.all(8),
              backgroundColor: Colors.grey[100],
            ),
            'td': Style(
              border: Border(
                  right: BorderSide(color: Colors.grey, width: 1),
                  top: BorderSide(color: Colors.grey, width: 1)),
              padding: HtmlPaddings.all(8),
            ),
          },
        ),
      ),
    );
  }
}
