import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';

class OrderPayAgree extends StatefulWidget {
  final Function? onHasReadFinished;
  final Function(bool)? onUpdateAcceptTerms;

  const OrderPayAgree({
    Key? key,
    this.onHasReadFinished,
    this.onUpdateAcceptTerms,
  }) : super(key: key);

  static void show(BuildContext context, Function? onHasReadFinished,
      Function(bool)? onUpdateAcceptTerms) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.white,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(10.r),
          topRight: Radius.circular(10.r),
        ),
      ),
      builder: (context) {
        // 使用addPostFrameCallback确保在构建完成后调用回调
        WidgetsBinding.instance.addPostFrameCallback((_) {
          onHasReadFinished?.call();
        });
        return SizedBox(
          height: MediaQuery.of(context).size.height * 0.8,
          child: OrderPayAgree(
            onHasReadFinished: onHasReadFinished,
            onUpdateAcceptTerms: onUpdateAcceptTerms,
          ),
        );
      },
    );
  }

  @override
  State<OrderPayAgree> createState() => _OrderPayAgreeState();
}

class _OrderPayAgreeState extends State<OrderPayAgree> {
  bool hasReadFinished = true;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels >=
        _scrollController.position.maxScrollExtent) {
      setState(() {
        hasReadFinished = true;
      });
      widget.onHasReadFinished?.call();
    }
  }

  void _onCloseAgree() {
    // TODO: 埋点
    // MyStat.addPageEvent('payment_close_terms', '关闭支付协议对话框');
    Navigator.pop(context);
  }

  void _onConfirmAgree() {
    Navigator.pop(context);
    widget.onUpdateAcceptTerms?.call(true);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // 头部
        Container(
          padding: EdgeInsets.only(top: 16.sp, bottom: 8.sp),
          child: Stack(
            children: [
              Center(
                child: Text(
                  I18n.of(context)?.translate('cm_news.termsOfService') ?? '',
                  style: TextStyle(
                      height: 1, fontSize: 22.sp, fontWeight: FontWeight.w500),
                ),
              ),
              Positioned(
                right: 12.sp,
                top: -12.sp,
                child: IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: _onCloseAgree,
                ),
              ),
            ],
          ),
        ),
        // 内容区域
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: EdgeInsets.symmetric(horizontal: 10.sp),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTitle('Condiciones de Uso'),
                _buildParagraph(
                    'Le damos la bienvenida a Chilatshop (en lo sucesivo denominado, la “Plataforma”, “nosotros” o “nuestro”). Para proteger sus derechos y garantizar un uso adecuado de los servicios ofrecidos, le solicitamos que lea detenidamente los presentes Términos y Condiciones (en adelante, los “Términos”) antes de registrarse o utilizar los servicios de la Plataforma. Una vez que accede o utiliza los servicios de la plataforma, se considera que ha dado su consentimiento y ha aceptado todos los contenidos de estos términos y se ha convertido en un usuario registrado de la plataforma. Si no está de acuerdo con alguna de las disposiciones aquí establecidas, deberá abstenerse de utilizar los servicios de la Plataforma.'),
                _buildTitle(
                    'Derechos y Responsabilidades de los Usuarios y de la Plataforma'),
                _buildSubtitle('Derechos del Usuario'),
                _buildListItem('1. Garantía de Servicios:',
                    'El usuario tiene derecho a completar, con la ayuda de la plataforma, los procesos relacionados con la adquisición de mercancías, el transporte y la despacho aduanero.'),
                _buildListItem('2. Resolución de Disputas:',
                    'En caso de presentarse inconvenientes durante las transacciones, el usuario podrá presentar reclamaciones dentro del plazo estipulado en los presentes Términos y recibir asistencia para su resolución.'),
                _buildListItem('3. Protección de Datos Personales:',
                    'La Plataforma protegerá toda la información personal proporcionada por el usuario, conforme a su política de privacidad, garantizando que no será utilizada o divulgada de manera ilegal.'),
                _buildListItem('4. Transparencia en las Transacciones:',
                    'El usuario tiene derecho a recibir una descripción clara y detallada sobre los precios de las mercancías y los costos de transporte.'),
                _buildListItem('5. Asistencia Postventa:',
                    'En caso de que los productos presenten daños u otros problemas, el usuario podrá presentar una reclamación dentro del plazo estipulado, y la Plataforma proporcionará asistencia en la resolución de la situación.'),
                _buildSubtitle('Responsabilidades del Usuario'),
                _buildListItem('1. Uso Legal:',
                    'El usuario debe garantizar que toda la información proporcionada sea veraz, lícita y conforme a las leyes y regulaciones locales. Asimismo, se compromete a no realizar actividades ilícitas a través de la Plataforma.'),
                _buildListItem('2. Comunicación Oportuna:',
                    'Durante los procesos de transacción y transporte, el usuario deberá mantenerse en contacto con la Plataforma. La Plataforma no será responsable por retrasos o inconvenientes ocasionados por falta de comunicación.'),
                _buildListItem('3. Cumplimiento de Normativas de Importación:',
                    'El usuario es responsable de conocer y cumplir con las normativas de importación de su país de residencia. Cualquier consecuencia derivada de la adquisición de productos prohibidos o restringidos será asumida exclusivamente por el usuario.'),
                _buildSubtitle('Responsabilidades de la Plataforma'),
                _buildListItem('1. Prestación de Servicios:',
                    'La Plataforma se compromete a asistir al usuario en los procesos de adquisición, transporte y despacho aduanero de los productos, sin asumir responsabilidad directa sobre la calidad de los productos ni las acciones de los vendedores.'),
                _buildListItem('2. Asistencia en Disputas:',
                    'Ante disputas razonables planteadas por el usuario, la Plataforma mediará activamente entre el usuario y el vendedor, aunque la decisión final corresponderá al vendedor.'),
                _buildListItem('3. Notificaciones Informativas:',
                    'La Plataforma proporcionará recordatorios relacionados con la conformidad normativa durante el proceso de transacción; sin embargo, es responsabilidad del usuario verificar y confirmar el cumplimiento de las leyes aplicables.'),
                _buildListItem('4. Información sobre Riesgos:',
                    'La Plataforma informará al usuario sobre posibles riesgos asociados al transporte y al despacho aduanero de los productos.'),
                _buildTitle(
                    'Alcance de exención de responsabilidad de la plataforma'),
                _buildSubSectionTitle(
                    '1. Calidad de los Productos y Transporte:'),
                _buildParagraph(
                    '• La Plataforma no se hace responsable de los daños sufridos por productos frágiles (incluidos, entre otros, cerámica, vidrio, madera, plástico, mármol, cosméticos, electrodomésticos, muebles, etc.) durante el transporte, salvo que los daños sean causados por factores humanos. En estos casos, la Plataforma podrá asistir al usuario en las negociaciones con el vendedor; sin embargo, la decisión final corresponderá al vendedor.'),
                _buildParagraph(
                    '• La Plataforma no se hace responsable por pérdidas ocasionadas por el envejecimiento natural de los productos (como moho, óxido o decoloración) ni por causas de fuerza mayor, incluyendo pero no limitándose a desastres naturales, conflictos armados, epidemias, huelgas, actos de terrorismo o modificaciones en normativas legales.'),
                _buildSubSectionTitle('2. Retrasos en la Entrega:'),
                _buildParagraph(
                    '• La Plataforma no asumirá responsabilidad por retrasos en la entrega ocasionados por festivos, condiciones climáticas, inspecciones aduaneras u otras causas de fuerza mayor (incluidos, entre otros, terremotos, incendios, desastres nevados, tormentas, nieblas y otros malos tiempos; huelgas, atentados terroristas, accidentes de tráfico inesperados, modificaciones de las leyes y políticas, acciones, decisiones o órdenes de gobiernos y organismos judiciales; delitos violentos como robos, asaltos a mano armada, etc.).'),
                _buildSubSectionTitle(
                    '3. Diferencias entre Imágenes y Productos Reales:'),
                _buildParagraph(
                    '• Si los productos recibidos difieren de las imágenes proporcionadas por el vendedor, la Plataforma podrá asistir al usuario en la solicitud de reembolsos, pero no garantiza el resultado. Además, si el embalaje presenta daños menores o arrugas que no afectan el uso del producto, la Plataforma no será responsable.'),
                _buildSubSectionTitle('4. Culpa del usuario:'),
                _buildParagraph(
                    '• Si el usuario no cumple con las leyes locales y adquiere productos prohibidos (como joyas, monedas, líquidos, animales vivos, armas, entre otros), la Plataforma podrá emitir advertencias, pero el usuario asumirá toda la responsabilidad.'),
                _buildParagraph(
                    '• Si el usuario proporciona información incorrecta o no mantiene contacto durante el proceso de transporte, será responsable de cualquier consecuencia derivada.'),
                _buildTitle('Definición y Criterios de “Producto Entregado”'),
                _buildParagraph(
                    'Para aclarar el estado de entrega de los productos, la Plataforma define el término “Producto Entregado” bajo los siguientes criterios:'),
                _buildSubSectionTitle('1. Confirmación de Entrega:'),
                _buildParagraph(
                    '• Se considerará que el producto ha sido entregado cuando el destinatario o la persona designada para recibir el paquete firme el comprobante de entrega proporcionado por la empresa de mensajería o confirme electrónicamente la recepción en el dispositivo del mensajero.'),
                _buildSubSectionTitle('2. Registro Logístico:'),
                _buildParagraph(
                    '• Si la información logística o el registro del sistema de la empresa de mensajería indica que el estado de las mercancías es “Entregado” y el usuario no presenta una objeción dentro de las 24 horas posteriores a la generación del registro de recepción, se considerará que el usuario acepta el estado de entrega.'),
                _buildSubSectionTitle(
                    '3. La recepción por la persona designada:'),
                _buildParagraph(
                    '• En caso de que la entrega sea recibida por una persona designada en la dirección proporcionada (incluidos familiares, amigos, colegas o personal de administración), tanto la plataforma como la empresa logística considerarán que la recepción se ha completado. El usuario debe asegurarse por sí mismo de que la persona designada sea de confianza. La plataforma no asume responsabilidad por las consecuencias causadas por las acciones de la persona designada.'),
                _buildSubSectionTitle('4. Entrega en Puntos de Recogida:'),
                _buildParagraph(
                    '• Si el usuario no contacta a la empresa de mensajería o al mensajero a tiempo y el paquete es depositado en un punto de recogida, casillero o lugar de almacenamiento temporal, y el sistema registra el estado como “Entregado”, la Plataforma y la empresa de mensajería considerarán que la entrega ha sido completada. Es responsabilidad del usuario recoger el paquete y verificar su estado.'),
                _buildSubSectionTitle(
                    '5. Tratamiento especial de la recepción anómala de las mercancías:'),
                _buildParagraph(
                    '• Si el embalaje presenta daños evidentes o deformaciones graves, el usuario debe rechazar la entrega, tomar fotografías como evidencia y contactar de inmediato al servicio de atención al cliente de la Plataforma. Si el usuario no rechaza la entrega o no proporciona evidencia válida, se considerará que el paquete ha sido entregado correctamente.'),
                _buildSubSectionTitle('6. Reportes Posteriores a la Entrega:'),
                _buildParagraph(
                    '• Si, tras la recepción del paquete, el usuario detecta problemas como daños, pérdida de contenido u otros inconvenientes, deberá informar a la Plataforma en un plazo máximo de 48 horas, adjuntando evidencia relevante (como fotografías o el número de seguimiento). Transcurrido este plazo, se considerará que el usuario acepta el estado del producto recibido y no se admitirán más reclamaciones.'),
                _buildSubSectionTitle('Aviso Especial:'),
                _buildParagraph(
                    'Una vez que el producto es considerado como "Entregado", la responsabilidad de la Plataforma y de la empresa de mensajería por el transporte termina. Cualquier pérdida o daño posterior por manejo inadecuado será asumido exclusivamente por el usuario.'),
                _buildTitle('Reclamaciones y Resolución de Disputas'),
                _buildParagraph(
                    'Durante la ejecución de los presentes Términos, cualquier disputa surgida entre las partes deberá resolverse mediante negociaciones amistosas. En caso de no llegar a un acuerdo, ambas partes aceptan someter la disputa a arbitraje ante la Comisión de Arbitraje de Yiwu, provincia de Zhejiang, cuya decisión será definitiva y vinculante para ambas partes.'),
                _buildParagraph(
                    'Además, usted acepta cumplir con Política de Privacidad publicada en el sitio web oficial de Chilatshop y autoriza a Chilatshop a procesar la información de su envío de acuerdo con las leyes y reglamentos aplicables y Política de Privacidad.'),
                _buildListItem('1. Plazo para Presentar Reclamaciones:',
                    'El usuario debe presentar reclamaciones relacionadas con problemas de calidad o servicio en un plazo máximo de 15 días tras la entrega del producto. Después de este período, la Plataforma no aceptará reclamaciones.'),
                _buildListItem('2. Documentación Necesaria para Reclamaciones:',
                    'El usuario deberá proporcionar la siguiente documentación:'),
                _buildParagraph(
                    '• Fotografías y videos que evidencien los daños del producto.'),
                _buildParagraph(
                    '• Capturas de pantalla de correos electrónicos relacionados con la disputa, que incluyan nombres, fechas y contenido.'),
                _buildSubSectionTitle('3. Métodos de Resolución de Disputas:'),
                _buildParagraph(
                    '• La Plataforma proporcionará canales de negociación amistosa, y el usuario deberá colaborar activamente en la resolución.'),
                _buildParagraph(
                    '• En caso de no llegar a un acuerdo, el usuario acepta someter la disputa a arbitraje ante la Comisión de Arbitraje de Yiwu, provincia de Zhejiang, cuya decisión será definitiva y vinculante para ambas partes.'),
                _buildParagraph(
                    '• Cada pedido estará sujeto a una única reclamación.'),
                _buildTitle(
                    'Obligaciones del Usuario para Garantizar la Entrega Segura de los Productos'),
                _buildParagraph(
                    'Para asegurar una entrega segura y oportuna, el usuario deberá cumplir con las siguientes obligaciones:'),
                _buildSubSectionTitle(
                    '1. Proporcionar Información de Entrega Precisa:'),
                _buildParagraph(
                    '• El usuario deberá garantizar que la dirección de entrega, la información de contacto y el nombre del destinatario sean precisos y completos. Cualquier retraso, pérdida o imposibilidad de entrega debido a información incorrecta será responsabilidad exclusiva del usuario.'),
                _buildSubSectionTitle(
                    '2. Cumplir con las Normativas de Importación:'),
                _buildParagraph(
                    '• El usuario será responsable de asegurarse de que los productos adquiridos cumplan con las leyes y reglamentos de importación del país de destino. En caso de retrasos, devoluciones o sanciones relacionadas con el incumplimiento de dichas normativas, el usuario asumirá todas las consecuencias.'),
                _buildSubSectionTitle(
                    '3. Pagar las Tarifas Relacionadas a Tiempo:'),
                _buildParagraph(
                    '• El usuario deberá abonar puntualmente los costos relacionados con el producto, el transporte internacional, los aranceles y otras tarifas aplicables. Los retrasos en el pago que resulten en problemas de envío o despacho aduanero serán responsabilidad del usuario.'),
                _buildSubSectionTitle(
                    '4. Inspeccionar los Productos antes de la Recepción:'),
                _buildParagraph(
                    '• Al recibir los productos, el usuario deberá verificar cuidadosamente si el embalaje está en buen estado y seguir los procedimientos establecidos por la empresa de mensajería para confirmar la entrega. En caso de daños, pérdida de contenido o defectos evidentes, el usuario deberá tomar fotografías como evidencia y comunicarse con la Plataforma de inmediato.'),
                _buildSubSectionTitle(
                    '5. Colaborar con los Procesos de Despacho Aduanero:'),
                _buildParagraph(
                    '• El usuario deberá proporcionar de manera oportuna los documentos o información necesarios para el despacho aduanero (como identificación o documentos de autorización). La falta de colaboración que resulte en retrasos o sanciones será responsabilidad exclusiva del usuario.'),
                _buildSubSectionTitle(
                    '6. Evitar el Envío de Productos Prohibidos:'),
                _buildParagraph(
                    '• El usuario no podrá adquirir ni enviar a través de la Plataforma productos prohibidos o restringidos por la ley, como materiales inflamables, líquidos, medicamentos, armas, entre otros. Las consecuencias derivadas de la confiscación, retención o sanción por este tipo de productos serán asumidas exclusivamente por el usuario.'),
                _buildSubSectionTitle(
                    '7. Conservar los Comprobantes de Entrega:'),
                _buildParagraph(
                    '• El usuario deberá conservar cuidadosamente los números de pedido, números de seguimiento y registros de transacción relacionados con el transporte, para facilitar el seguimiento o la resolución de disputas.'),
                _buildSubSectionTitle(
                    '8. Informar Problemas Anormales de Manera Oportuna:'),
                _buildParagraph(
                    '• Si el usuario detecta irregularidades en el transporte o interrupciones en las actualizaciones de información logística, deberá ponerse en contacto con el servicio de atención al cliente de la Plataforma para recibir asistencia oportuna.'),
                _buildSubSectionTitle('Aviso Especial:'),
                _buildParagraph(
                    'Si el usuario no cumple con las obligaciones mencionadas anteriormente, cualquier problema relacionado con la entrega o pérdida de los productos será responsabilidad exclusiva del usuario, y la Plataforma quedará eximida de cualquier responsabilidad.'),
                _buildTitle('Notas Importantes'),
                _buildSubSectionTitle('1. Comunicación Oportuna:'),
                _buildParagraph(
                    '• El usuario deberá asegurarse de mantener una comunicación efectiva con la Plataforma. Cualquier riesgo derivado de la falta de contacto será responsabilidad exclusiva del usuario.'),
                _buildSubSectionTitle(
                    '2. Cláusulas de Exoneración de la Plataforma:'),
                _buildParagraph(
                    '• La Plataforma no será responsable por interrupciones del servicio o pérdidas sufridas por el usuario debido a ataques cibernéticos, fallos del sistema o eventos de fuerza mayor. No obstante, la Plataforma hará todo lo posible por minimizar cualquier impacto negativo.'),
                _buildSubSectionTitle('3. Gestión de Infracciones:'),
                _buildParagraph(
                    '• En caso de que el usuario infrinja las reglas de la Plataforma o los presentes Términos, la Plataforma tendrá derecho a suspender o finalizar los servicios prestados al usuario.'),
                _buildTitle(
                    'Independencia y Derecho de Interpretación de los Términos'),
                _buildSubSectionTitle('1. Validez Parcial:'),
                _buildParagraph(
                    '• Si alguna disposición de los presentes Términos es declarada inválida o inaplicable, las demás disposiciones seguirán siendo plenamente válidas y efectivas.'),
                _buildSubSectionTitle('2. Derecho de Interpretación Final:'),
                _buildParagraph(
                    '• La interpretación final de los presentes Términos corresponde exclusivamente a Chilatshop.'),
              ],
            ),
          ),
        ),
        // 底部按钮
        Container(
          padding: EdgeInsets.all(8.w),
          child: ElevatedButton(
            onPressed: _onConfirmAgree,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppColors.primaryColor,
              minimumSize: Size(double.infinity, 40.h),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20.r),
              ),
            ),
            child: Text(
              !hasReadFinished
                  ? I18n.of(context)?.translate('cm_order.agreeView') ?? ''
                  : I18n.of(context)?.translate('cm_order.agreeClause') ?? '',
              style: TextStyle(
                fontSize: 15.sp,
                color: Colors.white,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTitle(String text) {
    return Padding(
      padding: EdgeInsets.only(top: 24.sp),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 20.sp,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF333333),
          height: 24.sp / 20.sp,
        ),
      ),
    );
  }

  Widget _buildSubtitle(String text) {
    return Padding(
      padding: EdgeInsets.only(top: 24.sp, bottom: 6.sp),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 18.sp,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  Widget _buildSubSectionTitle(String text) {
    return Padding(
      padding: EdgeInsets.only(top: 16.sp),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 16.sp,
          fontWeight: FontWeight.w500,
          color: const Color(0xFF333333),
        ),
      ),
    );
  }

  Widget _buildParagraph(String text) {
    return Padding(
      padding: EdgeInsets.only(top: 12.sp),
      child: Text(
        text,
        style: TextStyle(
          fontSize: 14.sp,
          height: 1.5,
          color: const Color(0xFF333639),
        ),
      ),
    );
  }

  Widget _buildListItem(String title, String content) {
    return Padding(
      padding: EdgeInsets.only(top: 12.sp),
      child: RichText(
        text: TextSpan(
          style: TextStyle(
            fontSize: 14.sp,
            height: 1.5,
            color: const Color(0xFF333639),
          ),
          children: [
            TextSpan(
              text: title,
              style: const TextStyle(
                fontWeight: FontWeight.w500,
                color: Color(0xFF333333),
              ),
            ),
            TextSpan(text: ' $content'),
          ],
        ),
      ),
    );
  }
}
