import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/global.dart';
import 'package:oktoast/oktoast.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';
// import 'package:flutter/foundation.dart';

void main() async {
  // TODO 性能分析
  // if (kDebugMode) {
  //   debugDefaultTargetPlatformOverride =
  //       TargetPlatform.fuchsia; // 启用 DevTools 链接
  // }
  WidgetsFlutterBinding.ensureInitialized();
  await Global.init();
  Map<String, String> trans = await HomeAPI.useLocalizationData();
  runApp(MyApp(trans: trans));
}

class MyApp extends StatelessWidget {
  final Map<String, String> trans;
  const MyApp({super.key, required this.trans});

  @override
  Widget build(BuildContext context) {
    return OKToast(
      child: RefreshConfiguration(
        hideFooterWhenNotFull: true, // Viewport不满一屏时,禁用上拉加载更多功能
        enableBallisticLoad: true, // 可以通过惯性滑动触发加载更多
        child: ScreenUtilInit(
          designSize: const Size(375, 812), // 设计稿的尺寸
          builder: (context, child) {
            return GetMaterialApp(
              title: 'Chilat',
              enableLog: true,
              unknownRoute: AppPages.unknownRoute,
              initialRoute: AppRoutes.HomePage,
              defaultTransition: Transition.native,
              // navigatorObservers: [
              //   GlobalObserver(),
              // ],
              getPages: AppPages.routes,
              localizationsDelegates: [
                AppLocalizationsDelegate(trans),
                RefreshLocalizations.delegate,
                GlobalMaterialLocalizations.delegate,
                GlobalWidgetsLocalizations.delegate,
                GlobalCupertinoLocalizations.delegate,
              ],
              supportedLocales: const [
                Locale('en'),
                Locale('zh'),
              ],
              localeResolutionCallback:
                  (Locale? locale, Iterable<Locale> supportedLocales) {
                return locale;
              },
              theme: AppStyles.getAppTheme(),
            );
          },
        ),
      ),
    );
  }
}
