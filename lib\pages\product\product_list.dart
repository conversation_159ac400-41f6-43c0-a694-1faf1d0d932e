import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/image_search.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/product/components/category_widget.dart';
import 'package:chilat2_mall_app/pages/product/components/product_card.dart';
import 'package:chilat2_mall_app/pages/product/product_model.dart';
import 'package:chilat2_mall_app/pages/search/looking.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:marquee/marquee.dart';

class ProductList extends StatefulWidget {
  final String? type;
  final String? tagId;
  final String? cateName;
  final String? categoryId;
  final String? keyword;
  final String? imageId;
  final String? imageUrl;
  final String? childCategoryId;
  final String? padc;

  const ProductList({
    super.key,
    this.keyword,
    this.tagId,
    this.cateName,
    this.categoryId,
    this.type,
    this.imageId,
    this.imageUrl,
    this.childCategoryId,
    this.padc,
  });

  @override
  State<ProductList> createState() => _ProductListState();
}

class _ProductListState extends State<ProductList> {
  bool _isLoading = true;
  bool _isPinned = false;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  bool _goodsCardShow = false;
  int selectedCateIndex = 0;
  bool showEstimateFreight = false;
  GoodsListPageModel pageData = GoodsListPageModel();
  List<ProductListQueryItem> queryItems = [];
  List<GoodsListCategoryFilterModel> filterCates = [];
  final GoodsListQueryParam _queryForm = GoodsListQueryParam();
  final ScrollController _scrollController = ScrollController();
  final TextEditingController _textController = TextEditingController();

  final double _goodsWidth = 160.0; // 稍微增加卡片宽度

  @override
  void initState() {
    super.initState();

    _queryForm.keyword = Get.arguments?['keyword'] ?? '';
    _textController.text = Get.arguments?['keyword'] ?? '';
    _queryForm.cateName = Get.arguments?['cateName'] ?? '';
    _queryForm.categoryId = Get.arguments?['categoryId'] ?? '';
    _queryForm.imageId = Get.arguments?['imageId'] ?? '';
    _queryForm.childCategoryId = Get.arguments?['childCategoryId'] ?? '';
    _queryForm.imageUrl = Get.arguments?['imageUrl'] ?? '';
    _queryForm.categoryDropdown = true;
    _queryForm.showMoreCate = true;
    if (Get.arguments?['tagId'] != null) {
      _queryForm.tagIds = [Get.arguments?['tagId'] ?? ''];
    } else if (Get.arguments?['type'] != null) {
      _queryForm.tagIds = ['10000100', '10000200'];
    } else {
      _queryForm.tagIds = [];
    }

    onPageData();

    _scrollController.addListener(_checkScrollPosition);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 查询列表页数据
  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      dynamic res = await ProductAPI.useGoodsListPage(_queryForm.toJson());
      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          pageData = GoodsListPageModel.fromJson(res['data']);
          final goodsList = pageData.pageData?.goodsList;
          if (goodsList != null && goodsList.isEmpty) {
            Navigator.push(
              Get.context!,
              MaterialPageRoute(
                builder: (ctx) => SearchLooking(
                  keyword: _queryForm.keyword,
                  cateName: _queryForm.cateName,
                  imageUrl: _queryForm.imageUrl,
                ),
              ),
            );
          }

          queryItems.add(ProductListQueryItem(
              key: 'addToCartCount',
              value: I18n.of(context)?.translate("cm_goods.sortByPopular"),
              selected: false));

          _queryForm.pageInfo = pageData.pageData?.page;

          pageData.pageData?.goodsList =
              pageData.pageData?.goodsList!.map((goods) {
            bool? exists = pageData.cartInfo?.goodsList
                .any((item) => item.goodsId == goods.goodsId);

            goods.selected = exists ?? false;
            return goods;
          }).toList();
          // 检查是否有预估运费
          showEstimateFreight = pageData.pageData?.goodsList
                  ?.any((item) => item.pcsEstimateFreight != null) ??
              false;
          onGetMarketCate();
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 计算滚动位置
  void _checkScrollPosition() {
    final double threshold = 100; // 距离底部 100 像素时触发
    final position = _scrollController.position;
    final currentOffset = _scrollController.offset;
    if (_queryForm.loadingPage == false &&
        position.pixels >= position.maxScrollExtent - threshold) {
      // 执行加载更多操作
      onGetGoodsListData(scroll: true);
    }

    final shouldPin = currentOffset >= 200;
    if (shouldPin != _isPinned) {
      setState(() => _isPinned = shouldPin);
    }
  }

  // 搜索商品数据
  Future<void> onGetGoodsListData({bool? scroll}) async {
    try {
      if (scroll == true && _queryForm.noMoreGoods == true) {
        return;
      }
      if (_queryForm.loadingPage == true) {
        return;
      }

      setState(() {
        _queryForm.loadingPage = true;

        if (scroll == true) {
          _queryForm.pageInfo?.current =
              (_queryForm.pageInfo?.current ?? 1) + 1;
        } else {
          _queryForm.noMoreGoods = false;
          _queryForm.pageInfo?.current = 1;
        }
        if ((_queryForm.activeQueries ?? []).contains("addToCartCount")) {
          _queryForm.sortField = 42;
        } else {
          _queryForm.sortField = null;
        }
      });

      dynamic res = await ProductAPI.useGoodsPageListData(_queryForm.toJson());
      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          GoodsListDataModel data = GoodsListDataModel.fromJson(res?['data']);
          if ((data.goodsList?.length ?? 0) <= 0) {
            _queryForm.noMoreGoods = true;
            return;
          }

          _queryForm.pageInfo?.current = data.page?.current ?? 1;
          _queryForm.pageInfo?.size = data.page?.size ?? 1;

          if (scroll == true) {
            pageData.pageData!.goodsList?.addAll(data.goodsList ?? []);
            // 以图搜图不做去重处理
            if (Get.arguments?['type'] != "imgSearch") {
              pageData.pageData!.goodsList = distinctBy(
                  pageData.pageData!.goodsList!, (item) => item.goodsId);
            }
          } else {
            pageData.pageData!.goodsList = data.goodsList;
          }
        });
      }
      setState(() => _queryForm.loadingPage = false);
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  //
  Future<void> onGetMarketCate() async {
    try {
      filterCates =
          pageData.pageData?.categoryFilters?.map((item) => item).toList() ??
              [];

      filterCates.insert(
          0,
          GoodsListCategoryFilterModel(
              id: "0",
              name: I18n.of(context)?.translate("cm_goods.all"),
              children: pageData.pageData?.categoryFilters
                      ?.map((item) => item)
                      .toList() ??
                  []));
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onCartList() async {
    try {
      MidCartModel? cartData = await Global.getCartData();

      setState(() {
        pageData.pageData?.goodsList =
            pageData.pageData?.goodsList?.map((goods) {
          goods.selected = (cartData?.goodsList ?? [])
              .any((item) => item.goodsId == goods.goodsId);
          return goods;
        }).toList();
      });
      // }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 活动标签事件
  Future<void> onActiveQueryEvent(ProductListQueryItem query) async {
    bool exists = (_queryForm.activeQueries ?? []).contains(query.key);

    if (exists) {
      (_queryForm.activeQueries ?? []).remove(query.key);
    } else if (query.key != null) {
      (_queryForm.activeQueries ?? []).add(query.key ?? '');
    }
    onGetGoodsListData();
  }

  Future<String?> onGetGoodsId(String sourceGoodsId) async {
    try {
      dynamic res = await ProductAPI.useGetGoods({
        "str": sourceGoodsId,
      });
      if (res != null && res?['result']?['code'] == 200) {
        return res['data'];
      } else {
        showErrorMessage(
            I18n.of(context)!.translate("cm_common_addGoodsError"));
        return null;
      }
    } catch (e) {
      showErrorMessage(e.toString());
      return null;
    }
  }

  Future<void> onGotoProductDetail(GoodsListDataItemModel? goods) async {
    try {
      if (goods?.goodsId?.isEmpty ?? true) {
        goods?.goodsId = await onGetGoodsId(goods.sourceGoodsId ?? "");
        if (goods?.goodsId?.isEmpty ?? true) {
          return;
        }
      }

      Get.toNamed(AppRoutes.ProductPage,
          arguments: {'productId': goods?.goodsId ?? ""});
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onAddToCart(BuildContext context, GoodsListDataItemModel? goods,
      double screenHeight) async {
    try {
      if (goods?.goodsId?.isEmpty ?? true) {
        goods?.goodsId = await onGetGoodsId(goods.sourceGoodsId ?? "");
        if (goods?.goodsId?.isEmpty ?? true) {
          return;
        }
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.sizeOf(context).width;
    screenHeight = MediaQuery.of(context).size.height;

    return AppScaffold(
        showScrollToTopButton: true,
        backgroundColor: Colors.white,
        scrollController: _scrollController,
        scrollToTopThreshold: 200,
        scrollToTopButtonBottom: 80,
        onPopInvoked: (result) {
          // TODO 待完善
        },
        body: Stack(children: [
          Column(
            children: [
              _buildTopSection(),
              Expanded(child: _buildGoodsList()),
            ],
          ),
          Visibility(
              visible: _isLoading,
              child: Positioned.fill(
                child: Container(
                  color: Colors.black.withOpacity(0.1),
                  child: Center(
                    child: RotationalLoadingWidget(),
                  ),
                ),
              ))
        ]));
  }

  Widget _buildTopSection() {
    return Container(
      color: Colors.white,
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Visibility(
            visible: _isPinned == false,
            child: _buildSearchBar(),
          ),
          Visibility(
            visible: _queryForm.imageUrl?.isEmpty ?? true,
            child: CategoryWidget(
              screenWidth: screenWidth,
              categoryId: _queryForm.categoryId ?? '0',
              categories: filterCates,
              onCategoryChange: (isAll, categoryId, childCategoryId) {
                if (isAll) {
                  _queryForm.categoryId = categoryId;
                  _queryForm.childCategoryId = childCategoryId;
                  _queryForm.pageInfo?.current = 1;

                  onGetGoodsListData();
                } else {
                  NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                      arguments: {
                        "categoryId": categoryId,
                        "childCategoryId": childCategoryId,
                        "type": widget.type == "recommendSearch"
                            ? "recommendSearch"
                            : null,
                        "tagId": widget.tagId,
                      });
                }
              },
            ),
          ),
          Visibility(
              visible: _queryForm.imageUrl?.isEmpty ?? true,
              child: _buildHotSearch()),
          // 以图搜图
          Visibility(
              visible: _queryForm.imageUrl?.isNotEmpty ?? false,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.end,
                  children: [
                    MyCachedNetworkImage(
                      imageurl: _queryForm.imageUrl ?? '',
                      width: 32.sp,
                      height: 32.sp,
                      borderRadius: BorderRadius.circular(4),
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }

  // 商品列表
  Widget _buildGoodsList() {
    screenWidth = MediaQuery.sizeOf(context).width;
    // 计算每行元素个数
    final crossAxisCount = (screenWidth / _goodsWidth).floor();

    return Visibility(
        visible: pageData.pageData?.goodsList?.length != null,
        child: Container(
          decoration: BoxDecoration(
            color: Colors.grey.shade100,
          ),
          child: GridView.builder(
            controller: _scrollController,
            gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: _goodsCardShow ? crossAxisCount : 1,
              childAspectRatio: _goodsCardShow
                  ? showEstimateFreight
                      ? 0.63
                      : 0.72
                  : 3.75,
              mainAxisSpacing: _goodsCardShow ? 4.sp : 2.sp,
              crossAxisSpacing: 4.sp,
            ),
            itemCount: (pageData.pageData?.goodsList?.length ?? 0) + 1,
            itemBuilder: (context, index) {
              if (index < (pageData.pageData?.goodsList?.length ?? 0)) {
                GoodsListDataItemModel product =
                    pageData.pageData?.goodsList?[index] ??
                        GoodsListDataItemModel();

                return _goodsCardShow
                    ? GoodsListDataCard(goods: product)
                    : GoodsListDataItem(
                        goods: product,
                        screenHeight: screenHeight,
                        onCartUpdate: onCartList,
                      );
              } else {
                return _buildLoadingIndicator();
              }
            },
          ),
        ));
  }

  // 搜索栏
  Widget _buildSearchBar() {
    return Container(
        color: Colors.white,
        margin: EdgeInsets.symmetric(horizontal: 0),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          Container(
            height: 20,
            padding: EdgeInsets.symmetric(vertical: 0),
            child: Marquee(
              text: I18n.of(context)?.translate("cm_common.tip") ?? '',
              style: TextStyle(color: Colors.red.shade300, fontSize: 16),
              scrollAxis: Axis.horizontal,
              crossAxisAlignment: CrossAxisAlignment.start,
              blankSpace: 20.0,
              velocity: 100.0,
              pauseAfterRound: Duration(seconds: 1),
              startPadding: 10.0,
              accelerationDuration: Duration(seconds: 1),
              accelerationCurve: Curves.linear,
              decelerationDuration: Duration(milliseconds: 2000),
              decelerationCurve: Curves.easeOut,
            ),
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  height: 24,
                  margin: EdgeInsets.only(left: 8.sp),
                  child: GestureDetector(
                    onTap: () {
                      if (Navigator.canPop(context)) {
                        Navigator.pop(context);
                      } else {
                        NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
                      }
                    },
                    child: Padding(
                      padding: EdgeInsets.symmetric(horizontal: 4.sp),
                      child: SvgPicture.asset(
                        'assets/images/common/arrow-left.svg',
                        width: 18.sp,
                      ),
                    ),
                  ),
                ),
                Container(
                  height: 28,
                  color: Colors.white,
                  width: screenWidth * 0.68,
                  margin: EdgeInsets.only(left: 12.sp),
                  child: TextField(
                    controller: _textController,
                    onChanged: (value) {
                      _queryForm.keyword = value;
                    },
                    decoration: InputDecoration(
                        hintText: I18n.of(context)
                            ?.translate('cm_search.searchPlaceholder'),
                        hintStyle: TextStyle(fontSize: 12),
                        filled: true,
                        fillColor: Colors.grey.shade100,
                        contentPadding: EdgeInsets.symmetric(vertical: 2),
                        border: OutlineInputBorder(
                          borderRadius: BorderRadius.circular(6),
                          borderSide: BorderSide.none,
                        ),
                        // 未聚焦时的边框
                        enabledBorder: OutlineInputBorder(
                          borderSide: BorderSide(color: Colors.red, width: 0.5),
                        ),
                        // 聚焦时的边框
                        focusedBorder: OutlineInputBorder(
                          borderSide:
                              BorderSide(color: Colors.red, width: 1), // 聚焦时加粗
                        ),
                        prefixIcon: Icon(Icons.search, size: 24),
                        suffixIcon: GestureDetector(
                          onTap: () {
                            setState(() {
                              onGetGoodsListData();
                            });
                          },
                          child: Container(
                            width: 56,
                            margin: EdgeInsets.only(left: 4),
                            padding: EdgeInsets.symmetric(horizontal: 3),
                            decoration: BoxDecoration(
                              color: Colors.red,
                              borderRadius: const BorderRadius.only(
                                topLeft: Radius.circular(4),
                                bottomLeft: Radius.circular(4),
                              ),
                            ),
                            child: Center(
                              child: Text(
                                I18n.of(context)?.translate('cm_home.search') ??
                                    'Search',
                                style: TextStyle(color: Colors.white),
                              ),
                            ),
                          ),
                        )),
                  ),
                ),
                const Spacer(), // 占据剩余空间
                Container(
                  padding: EdgeInsets.only(left: 6.sp),
                  child: ImageSearchWidget(
                    onImageSearch: (imageId, imageUrl) {
                      setState(() {
                        NavigatorUtil.pushNamed(context, AppRoutes.ProductList,
                            arguments: {
                              "imageId": imageId,
                              "imageUrl": imageUrl,
                              "type": "imgSearch",
                            });
                      });
                    },
                  ),
                ),
                GestureDetector(
                  onTap: () {
                    setState(() {
                      _goodsCardShow = !_goodsCardShow;
                    });
                  },
                  child: Container(
                    margin: EdgeInsets.symmetric(horizontal: 6.sp),
                    child: Icon(
                      _goodsCardShow == true
                          ? Icons.format_list_numbered
                          : Icons.grid_view,
                      size: 24,
                    ),
                  ),
                ),
              ],
            ),
          )
        ]));
  }

  // 热搜标签
  Widget _buildHotSearch() {
    return Wrap(
      spacing: 0, // 水平间距
      runSpacing: 0, // 垂直间距
      children: queryItems.map((item) {
        return Container(
          margin: EdgeInsets.only(top: 4, left: 4),
          padding: EdgeInsets.symmetric(horizontal: 4, vertical: 2),
          decoration: BoxDecoration(
            border: Border.all(
                color: item.selected ? Colors.red : Colors.transparent,
                width: 0),
            color: item.selected ? Colors.white : Colors.grey.shade200,
            borderRadius: BorderRadius.circular(4),
          ),
          child: GestureDetector(
            onTap: () {
              setState(() {
                item.selected = !item.selected;
                onActiveQueryEvent(item);
              });
            },
            child: Text(item.value ?? "",
                style: TextStyle(
                    color: item.selected ? Colors.red : Colors.black87)),
          ),
        );
      }).toList(),
    );
  }

  // 构建加载更多指示器
  Widget _buildLoadingIndicator() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6.sp),
      decoration: BoxDecoration(
          color: _queryForm.loadingPage == true
              ? Colors.grey.shade400
              : Colors.white),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Visibility(
              visible: _queryForm.loadingPage == true,
              child: Container(
                width: 48.sp,
                height: 48.sp,
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                ),
                child: Image.asset("assets/images/common/loading.gif",
                    fit: BoxFit.contain),
              ),
            ),
            Visibility(
                visible: _queryForm.noMoreGoods == true,
                child: Text(
                  softWrap: true,
                  I18n.of(context)!.translate("cm_goods.noMoreGoods"),
                  style: TextStyle(color: Colors.black54),
                )),
          ],
        ),
      ),
    );
  }
}
