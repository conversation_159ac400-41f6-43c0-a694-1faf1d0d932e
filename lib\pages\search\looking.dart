import 'package:chilat2_mall_app/components/auth_modal.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:dotted_border/dotted_border.dart';

// 商品项数据模型
class GoodsItem {
  String goodsName;
  List<String> imageList;
  int? count;
  bool isPriceLimited;
  double? minPrice;
  double? maxPrice;

  GoodsItem({
    this.goodsName = '',
    this.imageList = const [],
    this.count,
    this.isPriceLimited = true,
    this.minPrice,
    this.maxPrice,
  });

  // 创建默认商品项
  static GoodsItem createDefault({String? initialName}) {
    return GoodsItem(
      goodsName: initialName ?? '',
      imageList: [],
      count: null,
      isPriceLimited: true,
      minPrice: null,
      maxPrice: null,
    );
  }
}

class SearchLooking extends StatefulWidget {
  final String? keyword;
  final String? cateName;
  final String? imageUrl;
  const SearchLooking({super.key, this.keyword, this.cateName, this.imageUrl});

  @override
  State<SearchLooking> createState() => _SearchLookingState();
}

class _SearchLookingState extends State<SearchLooking> {
  // 页面数据
  String name = ''; // 用户名
  String email = '';
  String whatsapp = '';
  String description = ''; // 备注
  List<GoodsItem> goodsItems = []; // 商品项列表
  bool isAcceptSimilarProduct = true; // 是否接受相似产品
  bool isSubmitLoading = false;

  // 国家相关
  CountryModel? _countryModel;
  String? areaCode;
  String? countryId;
  final List<CountryModel> _countryList = [];
  Map<String, dynamic> countryRegexes = {};

  // UI相关
  double screenWidth = 0.0;
  final bool _isLoading = false;

  // 表单控制器
  final _submitFromKey = GlobalKey<FormState>();
  final _nameCtr = TextEditingController(); // 用户名控制器
  final _emailCtr = TextEditingController();
  final _areaCodeCtr = TextEditingController();
  final _whatsappCtr = TextEditingController();
  final _descriptionCtr = TextEditingController();
  final _whatsappRuleCtr = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  // 为每个商品项创建控制器列表
  final List<TextEditingController> _goodsNameControllers = [];
  final List<TextEditingController> _countControllers = [];
  final List<TextEditingController> _minPriceControllers = [];
  final List<TextEditingController> _maxPriceControllers = [];

  @override
  void initState() {
    super.initState();

    // 初始化商品项列表
    goodsItems = [
      GoodsItem.createDefault(
        initialName: widget.keyword ?? widget.cateName ?? '',
      )
    ];

    // 如果有传入的图片URL，添加到第一个商品项
    if (widget.imageUrl != null && widget.imageUrl!.isNotEmpty) {
      goodsItems[0].imageList = [widget.imageUrl!];
    }

    // 初始化控制器
    _initializeControllers();

    onPageData();
    onGetContactInfo();
  }

  // 初始化控制器
  void _initializeControllers() {
    _goodsNameControllers.clear();
    _countControllers.clear();
    _minPriceControllers.clear();
    _maxPriceControllers.clear();

    for (int i = 0; i < goodsItems.length; i++) {
      _goodsNameControllers
          .add(TextEditingController(text: goodsItems[i].goodsName));
      _countControllers.add(
          TextEditingController(text: goodsItems[i].count?.toString() ?? ''));
      _minPriceControllers.add(TextEditingController(
          text: goodsItems[i].minPrice?.toString() ?? ''));
      _maxPriceControllers.add(TextEditingController(
          text: goodsItems[i].maxPrice?.toString() ?? ''));
    }
  }

  // 添加商品项
  void addGoodsItem() {
    if (goodsItems.length >= 10) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
              I18n.of(context)?.translate("cm_search.maxResultsReached") ??
                  '已达到最大数量'),
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    setState(() {
      goodsItems.add(GoodsItem.createDefault());

      // 为新商品项添加控制器
      _goodsNameControllers.add(TextEditingController());
      _countControllers.add(TextEditingController());
      _minPriceControllers.add(TextEditingController());
      _maxPriceControllers.add(TextEditingController());
    });

    // 添加统计事件（如果有统计系统）
    print('点击按钮添加第${goodsItems.length}个找货商品');
  }

  // 删除商品项
  void removeGoodsItem(int index) {
    if (goodsItems.length > 1) {
      setState(() {
        goodsItems.removeAt(index);

        // 释放并移除对应的控制器
        _goodsNameControllers[index].dispose();
        _countControllers[index].dispose();
        _minPriceControllers[index].dispose();
        _maxPriceControllers[index].dispose();

        _goodsNameControllers.removeAt(index);
        _countControllers.removeAt(index);
        _minPriceControllers.removeAt(index);
        _maxPriceControllers.removeAt(index);
      });

      // 添加统计事件（如果有统计系统）
      print('点击按钮删除第${index + 1}个找货商品');
    }
  }

  @override
  void dispose() {
    onGoodsLookingTemSave();

    // 释放所有控制器
    _nameCtr.dispose();
    _emailCtr.dispose();
    _areaCodeCtr.dispose();
    _whatsappCtr.dispose();
    _descriptionCtr.dispose();
    _whatsappRuleCtr.dispose();
    _scrollController.dispose();

    for (var controller in _goodsNameControllers) {
      controller.dispose();
    }
    for (var controller in _countControllers) {
      controller.dispose();
    }
    for (var controller in _minPriceControllers) {
      controller.dispose();
    }
    for (var controller in _maxPriceControllers) {
      controller.dispose();
    }

    super.dispose();
  }

  Future<String?> onNuxtConfig() async {
    try {
      dynamic res = await BasisAPI.useGetNuxtConfig({
        'requestUri': AppRoutes.SearchLooking,
        'abtestPage': 'homepage2409'
      });

      if (res != null && res?['result']?['code'] == 200) {
        return res?['data']?['defaultCountryCode'];
      } else {
        return null;
      }
    } catch (e) {
      print('获取失败: $e');
    }
    return null;
  }

  // 获取联系人信息
  Future<void> onGetContactInfo() async {
    try {
      dynamic res = await InquiryAPI.useGoodsFindDetail({});
      if (res?.result?.code == 200 && res?.data != null) {
        final contactInfo = res.data;

        setState(() {
          if (contactInfo['name'] != null) {
            name = contactInfo['name'];
            _nameCtr.text = name;
          }

          if (contactInfo['email'] != null) {
            email = contactInfo['email'];
            _emailCtr.text = email;
          }

          if (contactInfo['whatsapp'] != null) {
            whatsapp = contactInfo['whatsapp'];
            _whatsappCtr.text = whatsapp;
          }

          if (contactInfo['countryId'] != null && _countryList.isNotEmpty) {
            final selectedCountry = _countryList.firstWhere(
              (country) => country.id == contactInfo['countryId'],
              orElse: () => CountryModel(),
            );
            if (selectedCountry.id != null) {
              _countryModel = selectedCountry;
              countryId = selectedCountry.id;
              areaCode = selectedCountry.areaCode;
              _areaCodeCtr.text = areaCode ?? '';
              countryRegexes = {
                'phoneCount': selectedCountry.phoneCount,
                'countryCodeTwo': selectedCountry.countryCodeTwo,
                'areaCode': selectedCountry.areaCode,
              };
            }
          }
        });
      }
    } catch (error) {
      print('获取联系人信息失败: $error');
    }
  }

  // 获取页面数据
  Future<void> onPageData() async {
    dynamic res = await BasisAPI.useGetCountry({});
    dynamic config = await BasisAPI.useGetNuxtConfig(
        {'requestUri': AppRoutes.SearchLooking, 'abtestPage': 'homepage2409'});

    if (res != null && res?['result']?['code'] == 200) {
      setState(() {
        res['data']?.forEach((item) {
          _countryList.add(CountryModel.fromJson(item));
        });

        // TODO 测试
        config['data']['defaultCountryCode'] = 'AR';

        if (config?['data']?['defaultCountryCode'] != null) {
          res['data']?.forEach((item) {
            if (item['countryCodeTwo'] ==
                config?['data']?['defaultCountryCode']) {
              countryId = item['id'];
              areaCode = item['areaCode'];
              countryRegexes = item;
              _areaCodeCtr.text = areaCode ?? '';

              if (countryRegexes['phoneCount'] != null) {
                _whatsappRuleCtr.text =
                    '${I18n.of(context)?.translate("cm_submit.whatsappTips")} ${countryRegexes['phoneCount']} ${I18n.of(context)?.translate("cm_submit.whatsapp")}';
              }
            }
          });
        }
      });
    }
  }

  // 提交表单
  void onSubmitGoodsLooking() async {
    if (isSubmitLoading) return;

    if (_submitFromKey.currentState!.validate()) {
      // 更新商品项数据
      for (int i = 0; i < goodsItems.length; i++) {
        goodsItems[i].goodsName = _goodsNameControllers[i].text.trim();
        goodsItems[i].count = int.tryParse(_countControllers[i].text);
        goodsItems[i].minPrice = double.tryParse(_minPriceControllers[i].text);
        goodsItems[i].maxPrice = double.tryParse(_maxPriceControllers[i].text);
      }

      dynamic paramObj = {
        'name': _nameCtr.text.trim(),
        'email': _emailCtr.text.trim(),
        'whatsapp': _whatsappCtr.text.trim(),
        'areaCode': areaCode,
        'countryId': countryId,
        'isAcceptSimilarProduct': isAcceptSimilarProduct,
        'remark': _descriptionCtr.text.trim(),
        'goodsFindLineList': goodsItems
            .map((item) => {
                  'goodsName': item.goodsName,
                  'imageList': item.imageList,
                  'count': item.count,
                  'isPriceLimited':
                      !item.isPriceLimited, // Vue版本中true表示不限制，这里需要反转
                  'minPrice': !item.isPriceLimited && item.minPrice != null
                      ? item.minPrice
                      : null,
                  'maxPrice': !item.isPriceLimited && item.maxPrice != null
                      ? item.maxPrice
                      : null,
                })
            .toList(),
      };

      setState(() {
        isSubmitLoading = true;
      });

      try {
        dynamic res = await InquiryAPI.useSubmitGoodsFind(paramObj);
        if (res?['result']?['code'] == 200) {
          setState(() {
            isSubmitLoading = false;
          });
          Get.toNamed(AppRoutes.SearchSubmitThankYou);
        } else {
          setState(() {
            isSubmitLoading = false;
          });
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(res?['result']?['message'] ??
                  I18n.of(context)?.translate("cm_find.errorMessage") ??
                  '提交失败'),
              duration: Duration(seconds: 3),
            ),
          );
        }
      } catch (error) {
        setState(() {
          isSubmitLoading = false;
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
                I18n.of(context)?.translate("cm_find.errorMessage") ?? '提交失败'),
            duration: Duration(seconds: 3),
          ),
        );
      }
    }
  }

  Future<void> onGoodsLookingTemSave() async {
    try {
      // 更新商品项数据
      for (int i = 0; i < goodsItems.length; i++) {
        if (i < _goodsNameControllers.length) {
          goodsItems[i].goodsName = _goodsNameControllers[i].text.trim();
        }
        if (i < _countControllers.length) {
          goodsItems[i].count = int.tryParse(_countControllers[i].text);
        }
        if (i < _minPriceControllers.length) {
          goodsItems[i].minPrice =
              double.tryParse(_minPriceControllers[i].text);
        }
        if (i < _maxPriceControllers.length) {
          goodsItems[i].maxPrice =
              double.tryParse(_maxPriceControllers[i].text);
        }
      }

      await InquiryAPI.useGoodsLookingTemSave({
        'name': _nameCtr.text.trim(),
        'email': _emailCtr.text.trim(),
        'whatsapp': _whatsappCtr.text.trim(),
        'areaCode': areaCode,
        'countryId': countryId,
        'isAcceptSimilarProduct': isAcceptSimilarProduct,
        'remark': _descriptionCtr.text.trim(),
        'goodsFindLineList': goodsItems
            .map((item) => {
                  'goodsName': item.goodsName,
                  'imageList': item.imageList,
                  'count': item.count,
                  'isPriceLimited': !item.isPriceLimited,
                  'minPrice': !item.isPriceLimited && item.minPrice != null
                      ? item.minPrice
                      : null,
                  'maxPrice': !item.isPriceLimited && item.maxPrice != null
                      ? item.maxPrice
                      : null,
                })
            .toList(),
      });
    } catch (e) {
      print('API 调用出错: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.sizeOf(context).width;

    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.white,
            body: _buildMainLayout(),
          );
  }

  Widget _buildMainLayout() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        SearchHeader(),
        Expanded(
          child: SingleChildScrollView(
            controller: _scrollController,
            physics: const ClampingScrollPhysics(),
            padding: const EdgeInsets.symmetric(vertical: 8),
            child: StatefulBuilder(
              builder: (context, setState) {
                return _buildPageContent(setState);
              },
            ),
          ),
        ),
        _buildFooterBar(),
      ],
    );
  }

  // 页面内容
  Widget _buildPageContent(StateSetter setState) {
    return Container(
      padding: EdgeInsets.all(0),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        // 根据是否有关键词显示不同的头部信息
        if (widget.keyword != null && widget.keyword!.isNotEmpty)
          _buildKeywordHeader()
        else
          _buildDefaultHeader(),

        // 表单内容
        _buildFormContent(setState),
      ]),
    );
  }

  // 有关键词时的头部
  Widget _buildKeywordHeader() {
    return Container(
      margin: EdgeInsets.only(top: 20, left: 20, right: 20),
      child: Column(
        children: [
          // 无数据图标
          Container(
            width: 112,
            margin: EdgeInsets.symmetric(horizontal: 20),
            child: SvgPicture.asset(
              'assets/images/common/no-data.svg',
              width: 112,
              height: 64,
              placeholderBuilder: (context) => Icon(
                Icons.search_off,
                size: 80,
                color: Colors.grey.shade400,
              ),
            ),
          ),
          SizedBox(height: 12),
          // 提示文本
          Text.rich(
            TextSpan(children: [
              TextSpan(
                text:
                    '${I18n.of(context)?.translate("cm_search.tipsPartOne") ?? ""} ',
                style: TextStyle(fontSize: 14, color: Color(0xFF7F7F7F)),
              ),
              TextSpan(
                text:
                    '"${goodsItems.isNotEmpty ? goodsItems[0].goodsName : widget.keyword}"',
                style: TextStyle(
                    fontSize: 14,
                    color: Color(0xFF11263B),
                    fontWeight: FontWeight.w500),
              ),
              TextSpan(
                text:
                    ' ${I18n.of(context)?.translate("cm_search.productNotFound") ?? ""}',
                style: TextStyle(
                    fontSize: 14, height: 21 / 14, color: Color(0xFF7F7F7F)),
              ),
              TextSpan(
                text:
                    ' ${I18n.of(context)?.translate("cm_search.suggestedOptions") ?? ""}',
                style: TextStyle(
                    fontSize: 14, height: 21 / 14, color: Color(0xFF7F7F7F)),
              ),
            ]),
          ),

          // 提示列表
          Container(
            margin: EdgeInsets.only(top: 2, left: 20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildTipItem(
                    I18n.of(context)?.translate("cm_search.tipsPartThree") ??
                        ''),
                _buildTipItem(
                    I18n.of(context)?.translate("cm_search.tipsPartFour") ??
                        ''),
                _buildTipItem(
                    I18n.of(context)?.translate("cm_search.tipsPartFive") ??
                        ''),
              ],
            ),
          ),

          // 定制搜索部分
          Container(
            margin: EdgeInsets.only(top: 22),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  I18n.of(context)?.translate("cm_goods.customSearch") ??
                      "定制搜索",
                  style: TextStyle(
                    fontSize: 26,
                    height: 26 / 26,
                    color: Color(0xFF11263B),
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(top: 12),
                  child: Text.rich(
                    TextSpan(children: [
                      TextSpan(
                        text: I18n.of(context)
                                ?.translate("cm_search.tipsPartSix") ??
                            "",
                        style: TextStyle(
                            fontSize: 14,
                            height: 21 / 14,
                            color: Color(0xFF7F7F7F)),
                      ),
                      WidgetSpan(
                        child: SizedBox(width: 8),
                      ),
                      TextSpan(
                        text:
                            ' ${I18n.of(context)?.translate("cm_search.tipsPartSeven") ?? ""}',
                        style: TextStyle(
                          fontSize: 16,
                          height: 27 / 16,
                          color: Color(0xFF11263B),
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ]),
                  ),
                ),
                // 装饰图片
                Container(
                  alignment: Alignment.centerRight,
                  margin: EdgeInsets.only(top: -10, right: 60),
                  child: SvgPicture.asset(
                    'assets/images/common/find-form.svg',
                    width: 112,
                    height: 46,
                    fit: BoxFit.cover,
                    placeholderBuilder: (context) => Icon(
                      Icons.search,
                      size: 46,
                      color: Colors.grey.shade400,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 默认头部
  Widget _buildDefaultHeader() {
    return Container(
      margin: EdgeInsets.only(top: 28, left: 20, right: 20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            I18n.of(context)?.translate("cm_goods.customSearch") ??
                "Búsqueda personalizada",
            style: TextStyle(
              fontSize: 52.sp,
              height: 58 / 52,
              color: Color(0xFF11263B),
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 28),
          // 副标题
          Text.rich(
            TextSpan(children: [
              TextSpan(
                text:
                    I18n.of(context)?.translate("cm_search.tipsPartSix") ?? "",
                style: TextStyle(
                    fontSize: 16.sp, height: 24 / 16, color: Color(0xFF7F7F7F)),
              ),
              TextSpan(text: " "),
              TextSpan(
                text: I18n.of(context)?.translate("cm_search.tipsPartSeven") ??
                    "Dentro 24 horas!",
                style: TextStyle(
                  fontSize: 18.sp,
                  height: 30 / 18,
                  color: Color(0xFF11263B),
                  fontWeight: FontWeight.w500,
                ),
              ),
            ]),
          ),
          // 装饰图片
          Container(
            alignment: Alignment.centerRight,
            margin: EdgeInsets.only(right: 60),
            child: SvgPicture.asset(
              'assets/images/common/find-form.svg',
              width: 112,
              height: 46,
              fit: BoxFit.cover,
              placeholderBuilder: (context) => Icon(
                Icons.search,
                size: 46,
                color: Colors.grey.shade400,
              ),
            ),
          ),
        ],
      ),
    );
  }

  // 表单内容
  Widget _buildFormContent(StateSetter setState) {
    return Container(
      padding: EdgeInsets.only(top: 0, right: 20, left: 20, bottom: 20),
      child: Form(
        key: _submitFromKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 用户名字段
            TextFormField(
              controller: _nameCtr,
              decoration: _buildUnderlineInputDecoration(
                label: _addRedAsterisk(
                    I18n.of(context)?.translate("cm_submit.username") ?? '用户名'),
                hintText: I18n.of(context)
                        ?.translate("cm_submit.usernamePlaceholder") ??
                    '请输入用户名',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return I18n.of(context)
                          ?.translate("cm_submit.usernamePlaceholder") ??
                      '请输入用户名';
                }
                return null;
              },
            ),

            SizedBox(height: 16),

            // 邮箱字段
            TextFormField(
              controller: _emailCtr,
              decoration: _buildUnderlineInputDecoration(
                label: _addRedAsterisk(
                    I18n.of(context)?.translate("cm_search.email") ?? '邮箱'),
                hintText:
                    I18n.of(context)?.translate("cm_search.pleaseInputEmail") ??
                        '请输入邮箱',
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return I18n.of(context)?.translate("cm_search.emailTips") ??
                      '请输入邮箱';
                }
                if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                    .hasMatch(value)) {
                  return I18n.of(context)?.translate("cm_search.emailTips") ??
                      '请输入正确的邮箱格式';
                }
                return null;
              },
            ),

            SizedBox(height: 16),

            // 国家选择
            DropdownButtonFormField<CountryModel>(
              value: _countryModel,
              items: _countryList.map((CountryModel option) {
                return DropdownMenuItem<CountryModel>(
                  key: Key(option.id ?? ''),
                  value: option,
                  child: Text(option.countryEsName ?? ''),
                );
              }).toList(),
              decoration: _buildUnderlineInputDecoration(
                label: _addRedAsterisk(
                    I18n.of(context)?.translate("cm_submit.country") ?? '国家'),
              ),
              validator: (value) {
                if (value == null) {
                  return I18n.of(context)
                          ?.translate("cm_submit.countryPlaceholder") ??
                      '请选择国家';
                }
                return null;
              },
              onChanged: (CountryModel? country) {
                setState(() {
                  _countryModel = country;
                  countryId = country?.id;
                  areaCode = country?.areaCode;
                  _areaCodeCtr.text = areaCode ?? '';
                  // 设置国家相关信息
                  if (country != null) {
                    countryRegexes = {
                      'phoneCount': country.phoneCount,
                      'countryCodeTwo': country.countryCodeTwo,
                      'areaCode': country.areaCode,
                    };
                  }
                });
              },
            ),

            SizedBox(height: 16),

            // WhatsApp字段 - 使用统一的表单样式
            TextFormField(
              controller: _whatsappCtr,
              keyboardType: TextInputType.phone,
              decoration: _buildUnderlineInputDecoration(
                label: _addRedAsterisk('WhatsApp'),
                hintText: I18n.of(context)
                        ?.translate("cm_search.pleaseInputWhatsapp") ??
                    '请输入WhatsApp号码',
              ).copyWith(
                // 添加前缀显示区号
                prefixIcon: Container(
                  width: 88,
                  padding: EdgeInsets.symmetric(horizontal: 12), // 统一的水平内边距
                  child: Row(
                    mainAxisAlignment:
                        MainAxisAlignment.spaceBetween, // 确保元素分布均匀
                    children: [
                      Text(
                        _areaCodeCtr.text.isNotEmpty
                            ? _areaCodeCtr.text
                            : '+000',
                        style: TextStyle(
                          fontSize: 16,
                          color: _areaCodeCtr.text.isNotEmpty
                              ? Colors.black
                              : Color(0xFFC2C2C2),
                        ),
                      ),
                      Container(
                        height: 14,
                        width: 1,
                        color: Color(0xFFE6E6E6),
                        margin: EdgeInsets.symmetric(horizontal: 8), // 左右各8px间距
                      ),
                      SizedBox(width: 0), // 占位符，确保spaceBetween正常工作
                    ],
                  ),
                ),
                prefixIconConstraints: BoxConstraints(
                  minWidth: 88,
                  maxWidth: 88,
                ),
              ),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return I18n.of(context)
                          ?.translate("cm_submit.whatsappRequired") ??
                      '请输入WhatsApp号码';
                }
                // 添加手机号长度验证
                if (countryRegexes['phoneCount'] != null) {
                  List<String> validCounts =
                      countryRegexes['phoneCount'].split(',');
                  bool isValidLength = validCounts.any(
                      (count) => value.length == int.tryParse(count.trim()));
                  if (!isValidLength) {
                    return '${I18n.of(context)?.translate("cm_submit.whatsappTips") ?? ""} ${countryRegexes['phoneCount']} ${I18n.of(context)?.translate("cm_submit.whatsapp") ?? ""}';
                  }
                }
                return null;
              },
              onChanged: (value) {
                // 自动添加区号前缀逻辑
                if (areaCode != null && areaCode!.isNotEmpty) {
                  if (value.length < areaCode!.length &&
                      value == areaCode!.substring(0, value.length)) {
                    _whatsappCtr.text = areaCode!;
                    _whatsappCtr.selection = TextSelection.collapsed(
                      offset: areaCode!.length,
                    );
                  }
                }
              },
            ),

            SizedBox(height: 16),

            Container(
              margin: EdgeInsets.only(bottom: 12),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    I18n.of(context)?.translate("cm_search.productInfo") ??
                        "商品信息",
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                  ),
                  SizedBox(height: 4),
                  Text(
                    "(${I18n.of(context)?.translate("cm_search.expandable") ?? "可展开"})",
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade500),
                  ),
                ],
              ),
            ),

            // 商品项列表
            ...goodsItems.asMap().entries.map((entry) {
              int index = entry.key;
              return _buildGoodsItem(index, setState);
            }),

            // 添加商品按钮
            if (goodsItems.length < 10)
              Container(
                margin: EdgeInsets.only(bottom: 16),
                child: GestureDetector(
                  onTap: addGoodsItem,
                  child: DottedBorder(
                    color: Color(0xFF034AA6),
                    strokeWidth: 2,
                    borderType: BorderType.RRect,
                    radius: Radius.circular(8),
                    dashPattern: [6, 4],
                    child: Container(
                      height: 40,
                      width: double.infinity,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add,
                            size: 16,
                            color: Color(0xFF034AA6),
                          ),
                          SizedBox(width: 6),
                          RichText(
                            text: TextSpan(
                              children: [
                                TextSpan(
                                  text:
                                      '${I18n.of(context)?.translate("cm_search.addProduct") ?? "添加商品"} ',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Color(0xFF034AA6),
                                  ),
                                ),
                                TextSpan(
                                  text: '(${goodsItems.length}/10)',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Color(0xFF666666),
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              ),

            // 备注字段
            TextFormField(
              minLines: 1,
              maxLines: 5,
              controller: _descriptionCtr,
              decoration: _buildUnderlineInputDecoration(
                label: Text(I18n.of(context)
                        ?.translate("cm_search.searchDescription") ??
                    '描述'),
                hintText: I18n.of(context)
                        ?.translate("cm_search.pleaseInputGoodsDescription") ??
                    '请输入商品描述',
              ),
            ),

            SizedBox(height: 16),

            // 是否接受相似产品
            Row(
              children: [
                Checkbox(
                  value: isAcceptSimilarProduct,
                  onChanged: (bool? newValue) {
                    setState(() {
                      isAcceptSimilarProduct = newValue ?? true;
                    });
                  },
                ),
                Expanded(
                  child: Text(
                    'Productos similares aceptados',
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  // 构建单个商品项
  Widget _buildGoodsItem(int index, StateSetter setState) {
    return Container(
      margin: EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        border: Border.all(color: Color(0xFFE5E5E5), width: 1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Stack(
        children: [
          Container(
            padding: EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 商品名称
                TextFormField(
                  controller: _goodsNameControllers[index],
                  decoration: _buildUnderlineInputDecoration(
                    label: _addRedAsterisk(
                        I18n.of(context)?.translate("cm_search.goodsName") ??
                            '商品名称'),
                    hintText: I18n.of(context)
                            ?.translate("cm_search.pleaseInputGoodsName") ??
                        '请输入商品名称',
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)
                              ?.translate("cm_search.goodsNameTips") ??
                          '请输入商品名称';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16),

                // 商品图片上传
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 使用统一的标签样式
                    _addRedAsterisk(
                        I18n.of(context)?.translate('cm_search.goodsImages') ??
                            '商品图片'),
                    SizedBox(height: 8),
                    FileImageUpload(
                      images: goodsItems[index].imageList,
                      onChanged: (List<String> images) {
                        setState(() {
                          goodsItems[index].imageList = images;
                        });
                      },
                    ),
                    SizedBox(height: 4),
                    Text(
                      I18n.of(context)
                              ?.translate("cm_search.limitedCapacity") ??
                          '限制容量',
                      style:
                          TextStyle(fontSize: 12, color: Colors.grey.shade500),
                    ),
                  ],
                ),

                SizedBox(height: 16),

                // 数量
                TextFormField(
                  controller: _countControllers[index],
                  keyboardType: TextInputType.number,
                  decoration: _buildUnderlineInputDecoration(
                    label: _addRedAsterisk(
                        I18n.of(context)?.translate('cm_search.quantity') ??
                            '数量'),
                    hintText: I18n.of(context)
                            ?.translate('cm_search.pleaseQuantity') ??
                        '请输入数量',
                  ),
                  validator: (value) {
                    if (value == null ||
                        value.isEmpty ||
                        int.tryParse(value) == null) {
                      return I18n.of(context)
                              ?.translate('cm_search.pleaseQuantity') ??
                          '请输入正确的数量';
                    }
                    return null;
                  },
                ),

                SizedBox(height: 16),

                // 价格限制
                _buildPriceSection(index, setState),
              ],
            ),
          ),

          // 删除按钮 - 绝对定位在右上角
          if (goodsItems.length > 1)
            Positioned(
              top: 12,
              right: 12,
              child: GestureDetector(
                onTap: () => removeGoodsItem(index),
                child: Container(
                  width: 24,
                  height: 24,
                  child: Icon(
                    Icons.delete_forever_outlined,
                    size: 24,
                    color: AppColors.primaryColor,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  // 构建价格部分
  Widget _buildPriceSection(int index, StateSetter setState) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          I18n.of(context)?.translate('cm_search.searchPriceLimit') ?? '价格限制',
          style: TextStyle(
              fontSize: 14,
              color: Color(0xFF11263B),
              fontWeight: FontWeight.w500),
        ),

        SizedBox(height: 8),

        // 不限制价格选项
        Row(
          children: [
            Radio<bool>(
              value: true,
              groupValue: goodsItems[index].isPriceLimited,
              onChanged: (value) {
                setState(() {
                  goodsItems[index].isPriceLimited = value ?? true;
                });
              },
            ),
            Expanded(
              child: Text(
                  I18n.of(context)?.translate('cm_search.priceUnlimit') ??
                      '不限制价格'),
            ),
          ],
        ),

        // 有价格限制选项 - 将输入框放在单选按钮右侧
        Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Radio<bool>(
              value: false,
              groupValue: goodsItems[index].isPriceLimited,
              onChanged: (value) {
                setState(() {
                  goodsItems[index].isPriceLimited = value ?? true;
                });
              },
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 最低价输入框
                  TextFormField(
                    controller: _minPriceControllers[index],
                    enabled: !goodsItems[index].isPriceLimited, // 根据选择状态启用/禁用
                    keyboardType:
                        TextInputType.numberWithOptions(decimal: true),
                    decoration: _buildUnderlineInputDecoration(
                      label: _addRedAsterisk(
                        I18n.of(context)?.translate('cm_search.minPrice') ??
                            '最低价',
                      ),
                      hintText: I18n.of(context)
                              ?.translate('cm_search.pleaseInputMinPrice') ??
                          '请输入最低价',
                      enabled: !goodsItems[index].isPriceLimited,
                    ),
                  ),
                  SizedBox(height: 16),
                  // 最高价输入框
                  TextFormField(
                    controller: _maxPriceControllers[index],
                    enabled: !goodsItems[index].isPriceLimited, // 根据选择状态启用/禁用
                    keyboardType:
                        TextInputType.numberWithOptions(decimal: true),
                    decoration: _buildUnderlineInputDecoration(
                      label: _addRedAsterisk(
                        I18n.of(context)?.translate('cm_search.maxPrice') ??
                            '最高价',
                      ),
                      hintText: I18n.of(context)
                              ?.translate('cm_search.pleaseInputMaxPrice') ??
                          '请输入最高价',
                      enabled: !goodsItems[index].isPriceLimited,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildFooterBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white.withOpacity(0.95),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: 4,
            offset: Offset(0, -2),
          ),
        ],
      ),
      padding: EdgeInsets.fromLTRB(8, 12, 8, 12),
      child: SafeArea(
        child: ElevatedButton(
          onPressed: isSubmitLoading ? null : onSubmitGoodsLooking,
          style: ElevatedButton.styleFrom(
            backgroundColor: Color(0xFFE50113),
            foregroundColor: Colors.white,
            minimumSize: Size(double.infinity, 44),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(12),
            ),
            elevation: 0,
          ),
          child: isSubmitLoading
              ? SizedBox(
                  width: 20,
                  height: 20,
                  child: CircularProgressIndicator(
                    strokeWidth: 2,
                    valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                  ),
                )
              : Text(
                  I18n.of(context)?.translate("cm_search.submitLooking") ??
                      'Enviar solicitud de compra',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w500),
                ),
        ),
      ),
    );
  }

  // 构建提示项
  Widget _buildTipItem(String text) {
    return Container(
      margin: EdgeInsets.only(bottom: 2),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(top: 8),
            child: Icon(Icons.circle, size: 4, color: Color(0xFF7F7F7F)),
          ),
          SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                  fontSize: 14, height: 21 / 14, color: Color(0xFF7F7F7F)),
            ),
          ),
        ],
      ),
    );
  }

  // 必填字段展示星号
  Widget _addRedAsterisk(String label) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: label,
            style: const TextStyle(color: Colors.black),
          ),
          const TextSpan(
            text: ' *',
            style: TextStyle(color: AppColors.primaryColor),
          ),
        ],
      ),
    );
  }

  // 统一的下划线输入框装饰样式
  InputDecoration _buildUnderlineInputDecoration({
    Widget? label,
    String? hintText,
    bool enabled = true,
  }) {
    return InputDecoration(
      label: label,
      hintText: hintText,
      hintStyle: TextStyle(
        fontSize: 13.sp,
        color: enabled ? Color(0xFFC2C2C2) : Color(0xFFC2C2C2),
        fontWeight: FontWeight.w400,
      ),
      enabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Color(0xFFF2F2F2),
          width: 1.0,
        ),
      ),
      disabledBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Color(0xFFE0E0E0),
          width: 1.0,
        ),
      ),
      focusedBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: Color.fromARGB(255, 102, 102, 102),
          width: 1.0,
        ),
      ),
      errorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: AppColors.primaryColor,
          width: 1.0,
        ),
      ),
      focusedErrorBorder: UnderlineInputBorder(
        borderSide: BorderSide(
          color: AppColors.primaryColor,
          width: 1.0,
        ),
      ),
    );
  }
}
