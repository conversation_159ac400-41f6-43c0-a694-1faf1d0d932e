// 询盘提交页

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_step_progress.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class InquirySubmit extends StatefulWidget {
  final bool? fromCart;
  const InquirySubmit({super.key, this.fromCart = false});

  @override
  State<InquirySubmit> createState() => _InquirySubmitState();
}

class _InquirySubmitState extends State<InquirySubmit> {
  bool _isLoading = false;
  final dynamic _pageData = {};
  double screenWidth = 0.0;
  String? _countryId;
  CountryModel? _countryModel;
  final _submitFromKey = GlobalKey<FormState>();
  final _submitNameCtr = TextEditingController();
  final _areaCodeCtr = TextEditingController();
  final _whatsappCtr = TextEditingController();
  final _emailCtr = TextEditingController();
  final _postcodeCtr = TextEditingController();
  final _addressCtr = TextEditingController();
  final _remarkCtr = TextEditingController();
  final _whatsappRuleCtr = TextEditingController(); //whatsapp控制器
  final List<CountryModel> _countryList = [];
  final List<MidCartGoodsModel> _goodsList = [];
  final ScrollController _scrollController = ScrollController();
  // final MainController mainController = Get.put(MainController());

  @override
  void initState() {
    super.initState();
    setState(() {
      _isLoading = true;
    });
    onPageData();
    onGetCountry();
    setState(() {
      _isLoading = false;
    });
    Future.delayed(Duration(milliseconds: 200), () {
      // 确保组件仍然挂载
      if (mounted) {
        onSelectCountry();
      }
    });
  }

  @override
  void dispose() {
    _submitNameCtr.dispose();
    _areaCodeCtr.dispose();
    _whatsappCtr.dispose();
    _emailCtr.dispose();
    _postcodeCtr.dispose();
    _addressCtr.dispose();
    _scrollController.dispose();

    super.dispose();
  }

  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      AuthLoginModel? user = await Global.getUserInfo();
      dynamic data = await Global.getInquiryInfo();
      setState(() {
        _pageData['totalEstimateFreight'] = data?['totalEstimateFreight'];
        _pageData['stat'] = data?['stat'];
        print("==>>TODO 2253: ${_pageData['stat']}");
        print("==>>TODO 2254: ${_pageData?['stat']?['selectTotalSalePrice']}");

        if (data?['lastInquiry'] != null) {
          _countryId = data?['lastInquiry']?['countryId'];
          _submitNameCtr.text = data?['lastInquiry']?['submitName'];
          _areaCodeCtr.text = data?['lastInquiry']?['areaCode'];
          _emailCtr.text = data?['lastInquiry']?['email'];
          _postcodeCtr.text = data?['lastInquiry']?['postcode'] ?? '';
          _addressCtr.text = data?['lastInquiry']?['address'] ?? '';

          if (data?['lastInquiry']?['whatsapp']?.contains('+')) {
            _whatsappCtr.text = data?['lastInquiry']?['whatsapp'];
          } else {
            _whatsappCtr.text = data?['lastInquiry']?['areaCode'] +
                data?['lastInquiry']?['whatsapp'];
          }
        } else {
          _emailCtr.text = user?.username ?? '';
        }

        data?['goodsList'].forEach((item) {
          _goodsList.add(MidCartGoodsModel.fromJson(item));
        });
      });
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  // 选择国家
  Future<void> onSelectCountry() async {
    SiteListModel? siteData = await Global.getSiteData();
    if (siteData != null) {
      return;
    }

    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        isDismissible: false, // 点击蒙层关闭
        enableDrag: false, // 拖动关闭

        builder: (BuildContext context) {
          return SizedBox(
            height: MediaQuery.sizeOf(context).height * 0.7,
            child: CountrySelectDrawer(
              onCountrySelect: (SiteListModel? siteInfo) {
                Global.setSiteData(siteInfo);
              },
              onCloseDrawer: () {
                Get.back();
              },
            ),
          );
        });
  }

  Future<void> onGetCountry() async {
    try {
      dynamic res = await BasisAPI.useGetCountry({});
      dynamic config = await BasisAPI.useGetNuxtConfig({
        'requestUri': AppRoutes.SearchLooking,
        'abtestPage': 'homepage2409'
      });

      if (res != null && res?['result']?['code'] == 200) {
        setState(() {
          res['data']?.forEach((item) {
            _countryList.add(CountryModel.fromJson(item));
          });

          _countryModel =
              _countryList.firstWhereOrNull((item) => item.id == _countryId);
          if (config?['data']?['defaultCountryCode'] != null) {
            res['data']?.forEach((item) {
              if (item['countryCodeTwo'] ==
                  config?['data']?['defaultCountryCode']) {
                _countryId = item['id'];
                _areaCodeCtr.text = item['areaCode'];
                _pageData['countryRegexes'] = item;
                _pageData['defaultCountry'] = item;
                if (_pageData['countryRegexes']?['phoneCount'] != null) {
                  _whatsappRuleCtr.text =
                      '${I18n.of(context)?.translate("cm_submit.whatsappTips")} ${_pageData['countryRegexes']?['phoneCount']} ${I18n.of(context)?.translate("cm_submit.whatsapp")}';
                }
              }
            });
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onSubmitInquiry() async {
    try {
      SiteListModel? siteData = await Global.getSiteData();

      if (_submitFromKey.currentState!.validate()) {
        List<dynamic> params = [];
        for (var goods in _goodsList) {
          for (var sku in (goods.skuList ?? [])) {
            params.add(
              {
                'skuId': sku?.skuId,
                'quantity': sku?.buyQty,
                'spm': sku?.spm,
              },
            );
          }
        }

        dynamic paramObj = {
          'submitName': _submitNameCtr.text.trim(),
          'countryId': _countryId?.trim(),
          'whatsapp': _whatsappCtr.text.trim(),
          'email': _emailCtr.text.trim(),
          'remark': _remarkCtr.text.trim(),
          "isTemporary": false,
          'areaCode': _areaCodeCtr.text.trim(),
          'postcode': _postcodeCtr.text.trim(),
          'fromCart': widget.fromCart == false ? false : true,
          'address': _addressCtr.text.trim(),
          'siteId': siteData?.id,
          'params': params
        };

        dynamic res = await InquiryAPI.useSubmitInquiry(paramObj);
        if (res?['result']?['code'] == 200) {
          Global.setCartList();
          NavigatorUtil.pushNamed(context, AppRoutes.FindSubmitThankyou,
              arguments: {
                'email': _emailCtr.text,
                'whatsapp': _whatsappCtr.text,
                'firstSubmit': res['data']['isFirstSubmit']
              });
        }
      } else {
        _scrollController.animateTo(
          30,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 商品
  void _onUnfoldGoods(String goodsId) async {
    try {
      setState(() {
        for (var goods in _goodsList) {
          if (goods.goodsId == goodsId) {
            setState(() {
              goods.unfold = goods.unfold ? false : true;
            });
            break;
          }
        }
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.sizeOf(context).width;
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            onPopInvoked: (result) {
              NavigatorUtil.pushNamed(context, AppRoutes.CartPage);
            },
            body: Column(children: [
              _buildHeaderBar(),
              Expanded(
                  child: Container(
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                ),
                child: SingleChildScrollView(
                    child: Column(children: [
                  _buildContactMessage(),
                  _buildInquiryForm(),
                  _buildCartData(),
                  _buildProductList(),
                ])),
              )),
              _buildFooterBar(),
            ]),
          );
  }

  // 顶部标题栏
  Widget _buildHeaderBar() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2))
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          OrderProgressBar(
            currentStep: 1,
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 8.sp, vertical: 8.sp),
            child: Row(
              children: [
                GestureDetector(
                  onTap: () {
                    Get.back();
                  },
                  child: Icon(
                    Icons.arrow_back_ios,
                    size: 22.sp,
                  ),
                ),
                Expanded(
                  child: Text(
                    textAlign: TextAlign.center, // 文本自身居中
                    I18n.of(context)?.translate("cm_submit.sendInquire") ?? '',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
                // 右侧占位（宽度为 Icon 的宽度，使文本真正居中）
                SizedBox(width: 24), // Icon 默认大小为 24px
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildContactMessage() {
    return Container(
      margin: EdgeInsets.only(top: 6.sp, left: 6.sp, right: 6.sp),
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.sp),
      ),
      child: Text(I18n.of(context)?.translate("cm_submit.messageTitle") ?? '',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
          )),
    );
  }

  // 表单
  Widget _buildInquiryForm() {
    return Container(
      margin: EdgeInsets.only(top: 6.sp, left: 6.sp, right: 6.sp),
      padding: EdgeInsets.symmetric(horizontal: 6.sp, vertical: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.sp),
      ),
      child: Form(
        key: _submitFromKey,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 2, vertical: 2),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              TextFormField(
                controller: _submitNameCtr,
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate("cm_submit.username") ??
                            ''),
                    I18n.of(context)
                            ?.translate("cm_submit.countryPlaceholder") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(Icons.person_outline,
                          color: Colors.grey, size: 20.sp),
                    )),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return I18n.of(context)
                            ?.translate("cm_submit.countryPlaceholder") ??
                        '';
                  }
                  return null;
                },
              ),
              SizedBox(height: 12),
              // 选择国家
              DropdownButtonFormField<CountryModel>(
                value: _countryModel,
                items: _countryList.map((CountryModel option) {
                  return DropdownMenuItem<CountryModel>(
                    key: Key(option.id ?? ''),
                    value: option,
                    child: Text(option.countryEsName ?? ''),
                  );
                }).toList(),
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate("cm_submit.country") ?? ''),
                    I18n.of(context)
                            ?.translate("cm_submit.usernamePlaceholder") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(Icons.location_on_outlined,
                          color: Colors.grey, size: 20.sp),
                    )),
                validator: (value) {
                  if (value == null) {
                    return I18n.of(context)
                            ?.translate("cm_submit.countryPlaceholder") ??
                        '';
                  }
                  return null;
                },
                onChanged: (CountryModel? country) {
                  setState(() {
                    _countryId = country?.id;
                    _countryModel = country;
                    _areaCodeCtr.text = country?.areaCode ?? '';
                  });
                },
              ),
              SizedBox(height: 12),
              TextFormField(
                controller: _whatsappCtr,
                decoration: _buildInputDecoration(
                    _addRedAsterisk('Whatsapp'),
                    I18n.of(context)
                            ?.translate("cm_search.pleaseInputWhatsapp") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                      ),
                      child: SvgPicture.asset(
                        'assets/images/common/whatsapp.svg',
                        width: 18.sp, // 图标宽度
                        height: 18.sp, // 图标高度
                      ),
                    )),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return I18n.of(context)
                            ?.translate("cm_submit.whatsappRequired") ??
                        '';
                  }
                  // 添加更详细的手机号验证逻辑
                  List<String> validCountries =
                      _pageData?['countryRegexes']?['phoneCount']?.split(',') ??
                          [];
                  if (value.isNotEmpty && validCountries.isNotEmpty) {
                    for (var country in validCountries) {
                      if (value.length == int.parse(country)) {
                        return null;
                      }
                    }
                  } else {
                    if (value.isNotEmpty) {
                      return null;
                    }
                  }
                  return '${I18n.of(context)?.translate("cm_submit.whatsappTips") ?? ''} ${_pageData?['countryRegexes']?['phoneCount'] ?? ''} ${I18n.of(context)?.translate("cm_submit.whatsapp") ?? ''}';
                },
                onChanged: (value) {
                  setState(() {
                    if (value.length < _areaCodeCtr.text.length &&
                        value == _areaCodeCtr.text.substring(0, value.length)) {
                      _whatsappCtr.text = _areaCodeCtr.text;
                      _whatsappCtr.selection = TextSelection.collapsed(
                        offset: _areaCodeCtr.text.length,
                      );
                    } else {
                      _whatsappCtr.text = value;
                    }
                  });
                },
              ),
              SizedBox(height: 12),
              TextFormField(
                controller: _emailCtr,
                keyboardType: TextInputType.emailAddress,
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate("cm_search.email") ?? ''),
                    I18n.of(context)?.translate("cm_search.pleaseInputEmail") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(Icons.email_outlined,
                          color: Colors.grey, size: 20.sp),
                    )),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return I18n.of(context)?.translate("cm_search.emailTips") ??
                        '';
                  }
                  if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$')
                      .hasMatch(value)) {
                    return I18n.of(context)?.translate("cm_search.emailTips") ??
                        '';
                  }
                  return null;
                },
              ),
              SizedBox(height: 12),
              // 邮编
              TextFormField(
                controller: _postcodeCtr,
                keyboardType: TextInputType.text,
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate("cm_search.postcode") ?? '',
                        displayed: false),
                    I18n.of(context)?.translate("cm_search.pleaseInputTip") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: 8,
                      ),
                      child: SvgPicture.asset(
                        'assets/images/common/postcode.svg',
                        width: 18.sp, // 图标宽度
                        height: 18.sp, // 图标高度
                      ),
                    )),
              ),
              SizedBox(height: 12),
              // 地址
              TextFormField(
                controller: _addressCtr,
                keyboardType: TextInputType.text,
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate("cm_search.address") ?? '',
                        displayed: false),
                    I18n.of(context)?.translate("cm_search.pleaseInputTip") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(Icons.home, color: Colors.grey, size: 20.sp),
                    )),
              ),
              SizedBox(height: 12),
              // 备注
              TextFormField(
                minLines: 1,
                maxLines: 5, // 允许输入多行文本
                controller: _remarkCtr,
                keyboardType: TextInputType.text,
                decoration: _buildInputDecoration(
                    _addRedAsterisk(
                        I18n.of(context)?.translate(
                                "cm_submit.requirementDescription") ??
                            '',
                        displayed: false),
                    I18n.of(context)
                            ?.translate("cm_submit.requirementPlaceholder") ??
                        '',
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 8),
                      child: Icon(Icons.edit_square,
                          color: Colors.grey, size: 20.sp),
                    )),
              ),
              const SizedBox(height: 12.0),
            ],
          ),
        ),
      ),
    );
  }

  // 购物车统计
  Widget _buildCartData() {
    return Container(
      width: screenWidth,
      margin: EdgeInsets.only(top: 6.sp, left: 6.sp, right: 6.sp),
      padding: EdgeInsets.symmetric(horizontal: 6.sp, vertical: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.sp),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start, // 关键属性
        children: [
          Text(
            I18n.of(context)?.translate("cm_find_inquireSummary") ?? '',
            style: TextStyle(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.sp, right: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 元素间和两端间距相等
              children: [
                Text(
                  I18n.of(context)?.translate("cm_find_quantityOfModels") ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      _pageData?['stat']?['goodsCount']?.toString() ?? '',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Visibility(
                      visible: (_pageData?['stat']?['goodsCount'] ?? 0) > 1,
                      child: Container(
                        padding: EdgeInsets.only(left: 6.sp),
                        child: Text(
                          I18n.of(context)
                                  ?.translate("cm_find_productModels") ??
                              '',
                        ),
                      ),
                    ),
                    Visibility(
                      visible: (_pageData?['stat']?['goodsCount'] ?? 0) <= 1,
                      child: Container(
                        padding: EdgeInsets.only(left: 6.sp),
                        child: Text(
                          I18n.of(context)?.translate("cm_find_productModel") ??
                              '',
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.sp, right: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 元素间和两端间距相等
              children: [
                Text(
                  I18n.of(context)?.translate("cm_find_quantityOfUnits") ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      _pageData?['stat']?['selectSkuTotalQuantity']
                              ?.toString() ??
                          '',
                      style: TextStyle(
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    Visibility(
                      visible:
                          (_pageData?['stat']?['selectSkuTotalQuantity'] ?? 0) >
                              1,
                      child: Container(
                        padding: EdgeInsets.only(left: 6.sp),
                        child: Text(
                          I18n.of(context)
                                  ?.translate("cm_find_totalSkuUnits") ??
                              '',
                        ),
                      ),
                    ),
                    Visibility(
                      visible: (_pageData?['stat']?['selectSkuTotalQuantity'] ??
                              0) <=
                          1,
                      child: Container(
                        padding: EdgeInsets.only(left: 6.sp),
                        child: Text(
                          I18n.of(context)?.translate("cm_find_totalSkuUnit") ??
                              '',
                        ),
                      ),
                    ),
                  ],
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.sp, right: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 元素间和两端间距相等
              children: [
                Text(
                  I18n.of(context)?.translate("cm_find_itemsCost") ?? '',
                  style: TextStyle(
                    fontSize: 14.sp,
                    color: Colors.black87,
                  ),
                ),
                Visibility(
                  visible:
                      (_pageData?['stat']?['selectTotalSalePrice'] ?? 0) >= 0,
                  child: Container(
                    padding: EdgeInsets.only(left: 6.sp),
                    child: Text(
                        setUnit(_pageData?['stat']?['selectTotalSalePrice']),
                        style: TextStyle(
                            fontWeight: FontWeight.w500,
                            color: AppColors.primaryColor)),
                  ),
                ),
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(top: 6.sp, right: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween, // 元素间和两端间距相等
              children: [
                Row(
                  children: [
                    Tooltip(
                      message: I18n.of(context)?.translate(
                              "cm_goods.freightAdjustmentPending") ??
                          '',
                      preferBelow: false,
                      verticalOffset: 12,
                      triggerMode: TooltipTriggerMode.tap,
                      showDuration: Duration(seconds: 5), // 显示持续时间
                      textStyle: TextStyle(
                        fontSize: 14.sp,
                        color: Colors.grey.shade800,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.amber.shade100, // 设置背景色
                        borderRadius: BorderRadius.circular(4), // 圆角
                        border: Border.all(color: Colors.black12, width: 1),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.amber.withOpacity(0.2),
                            blurRadius: 4,
                            offset: Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Icon(
                        Icons.error_outline_sharp,
                        size: 16.sp,
                        color: Colors.orange.shade600,
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      child: Text(
                        '${I18n.of(context)?.translate("cm_goods.estimatedShippingCost") ?? ''}: ',
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                  ],
                ),
                Row(
                  children: [
                    Visibility(
                      visible: _pageData['totalEstimateFreight'] != null,
                      child: Container(
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        child: Text(
                          setUnit(_pageData['totalEstimateFreight']),
                          style:
                              TextStyle(fontSize: 14.sp, color: Colors.black87),
                        ),
                      ),
                    ),
                    Visibility(
                      visible: _pageData['totalEstimateFreight'] == null,
                      child: Text(
                        I18n.of(context)
                                ?.translate("cm_goods.pendingConfirmation") ??
                            '',
                        style:
                            TextStyle(fontSize: 14.sp, color: Colors.black87),
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 构建输入框装饰
  InputDecoration _buildInputDecoration(
      Widget label, String hintText, Widget prefixIcon) {
    return InputDecoration(
      label: label,
      labelStyle: TextStyle(fontSize: 16.sp),
      contentPadding: EdgeInsets.symmetric(vertical: 18.sp),
      border: OutlineInputBorder(
        borderRadius: BorderRadius.circular(5),
        borderSide: BorderSide.none,
      ),
      filled: true,
      fillColor: const Color.fromRGBO(245, 247, 247, 1),
      hintText: hintText,
      hintStyle: TextStyle(fontSize: 13.sp, color: Colors.black54),
      prefixIcon: prefixIcon,
      prefixIconConstraints: BoxConstraints(
        minWidth: 32.sp, // 确保容器不会过大
        minHeight: 32.sp,
      ),
      errorStyle: TextStyle(fontSize: 14.sp), // 设置错误消息的字体大小
      errorMaxLines: 3,
    );
  }

  // 商品列表
  Widget _buildProductList() {
    return Container(
      margin: EdgeInsets.only(top: 6.sp, left: 6.sp, right: 6.sp, bottom: 8.sp),
      padding: EdgeInsets.symmetric(horizontal: 6.sp, vertical: 12.sp),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(6.sp),
      ),
      child: ListView.builder(
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemCount: _goodsList.length,
          itemBuilder: (context, index) {
            MidCartGoodsModel goods = _goodsList[index];
            return _buildGoodsItem(setState, goods, index);
          }),
    );
  }

  Widget _buildGoodsItem(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 4.sp),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        _buildGoodsCard(setState, goods, index),
        _buildSkuItems(setState, goods, index),
        Container(
          height: 1, // 分割线粗细
          color: Colors.grey.shade200, // 分割线颜色
          margin: EdgeInsets.symmetric(vertical: 6.sp),
        ),
      ]),
    );
  }

  // 商品卡片
  Widget _buildGoodsCard(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return GestureDetector(
      onTap: () {
        Get.toNamed(AppRoutes.ProductPage,
            arguments: {'productId': goods.goodsId ?? ""});
      },
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 3,
            child: CachedNetworkImage(
              imageUrl: goods.mainImageUrl ?? '',
              fit: BoxFit.cover,
              width: 75,
              height: 75,
            ),
          ),
          Expanded(
            flex: 6,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 6.sp),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(goods.goodsName ?? '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 12)),
                  Text('Código: ${goods.goodsNo}',
                      style: TextStyle(color: Colors.grey, fontSize: 12)),
                  Text.rich(TextSpan(
                      style: TextStyle(color: Colors.grey, fontSize: 12),
                      children: [
                        TextSpan(
                          text:
                              '${I18n.of(context)?.translate("cm_goods.minOrder") ?? ''}: ',
                        ),
                        TextSpan(
                          text: '${goods.minBuyQuantity} ',
                        ),
                        TextSpan(
                          text: '${goods.goodsPriceUnitName} ',
                        ),
                      ])),
                  Visibility(
                      visible: goods.estimateFreight != null,
                      child: Text.rich(TextSpan(
                          style: TextStyle(color: Colors.grey, fontSize: 12),
                          children: [
                            TextSpan(
                              text: I18n.of(context)
                                      ?.translate("cm_goods.shippingCost") ??
                                  '',
                            ),
                            TextSpan(
                              text: setUnit(goods.estimateFreight),
                            ),
                          ]))),
                ],
              ),
            ),
          ),
          Expanded(
            flex: 1,
            child: Center(
              child: GestureDetector(
                  onTap: () {
                    _onUnfoldGoods(goods.goodsId ?? "");
                  },
                  child: Icon(
                    Icons.keyboard_arrow_down,
                    color: Colors.grey,
                    size: 32.sp,
                  )),
            ),
          )
        ],
      ),
    );
  }

  Widget _buildSkuItems(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return ListView.builder(
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: goods.unfold ? goods.skuList.length : 0,
        itemBuilder: (context, index) {
          return _buildSkuCard(setState, goods, goods.skuList[index], index);
        });
  }

  Widget _buildSkuCard(StateSetter setState, MidCartGoodsModel goods,
      MidCartSkuModel sku, int index) {
    return Container(
      margin: EdgeInsets.only(top: 6.sp),
      padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 4.sp),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        borderRadius: BorderRadius.circular(6),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(bottom: 6),
            child: Row(
              children: [
                Expanded(
                    flex: 1,
                    child: CachedNetworkImage(
                      imageUrl: sku.skuImage ?? '',
                      fit: BoxFit.cover,
                      width: 32,
                      height: 32,
                      placeholder: (context, url) =>
                          const CircularProgressIndicator(),
                    )),
                Expanded(
                    flex: 7,
                    child: Container(
                      padding: EdgeInsets.only(left: 6.sp),
                      child: Text(
                        sku.skuName,
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                        style: TextStyle(fontSize: 12),
                      ),
                    ))
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(bottom: 6),
            child: Row(children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  setUnit(sku.salePrice),
                  style: TextStyle(
                      color: Colors.black, fontWeight: FontWeight.w500),
                ),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  "/ ${goods.goodsPriceUnitName}",
                  style: TextStyle(
                      color: Colors.black87, fontWeight: FontWeight.w300),
                ),
              ),
              Spacer(),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(Icons.remove, color: Colors.white),
                    Container(
                      width: 60,
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      child: Center(
                        child: Text(sku.buyQty.toString()),
                      ),
                    ),
                    Icon(Icons.add, color: Colors.white),
                  ],
                ),
              ),
            ]),
          ),
          Container(
            height: 0.5,
            color: Colors.grey.shade300,
            margin: const EdgeInsets.only(bottom: 6),
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6),
            child: Row(
              children: [
                Spacer(),
                Text(setUnit(sku.subtotalSalePrice),
                    style: TextStyle(
                        color: AppColors.primaryColor,
                        fontWeight: FontWeight.w500)),
              ],
            ),
          )
        ],
      ),
      // child: Row(
      //   children: [
      //     Container(
      //       width: screenWidth - 10,
      //       padding: EdgeInsets.symmetric(horizontal: 6, vertical: 6),
      //       decoration: BoxDecoration(
      //         color: Colors.grey.shade100,
      //         borderRadius: BorderRadius.circular(6),
      //       ),
      //       child: Column(
      //         children: [

      //         ],
      //       ),
      //     )
      //   ],
      // ),
    );
  }

  // 底部结算栏
  Widget _buildFooterBar() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 8, offset: Offset(0, -2))
        ],
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 6.sp),
          alignment: Alignment.center,
          child: Text(
            I18n.of(context)?.translate("cm_find_confirmWithoutPay") ?? '',
            style: TextStyle(color: Colors.black54),
          ),
        ),
        Container(
          padding: EdgeInsets.symmetric(vertical: 14.sp),
          width: screenWidth,
          decoration: BoxDecoration(
            color: AppColors.primaryColor,
            borderRadius: BorderRadius.circular(4.sp),
          ),
          child: GestureDetector(
              onTap: () {
                onSubmitInquiry();
              },
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    SvgPicture.asset(
                      'assets/images/common/cart-submit-white.svg',
                      width: 24.sp, // 图标宽度
                      height: 24.sp, // 图标高度
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 6.sp),
                      child: Text(
                        I18n.of(context)?.translate("cm_submit.sendInquire") ??
                            '',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 18.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    )
                  ],
                ),
              )),
        ),
      ]),
    );
  }

  // 必填字段展示星号
  Widget _addRedAsterisk(String label, {bool displayed = true}) {
    return RichText(
      text: TextSpan(
        children: [
          TextSpan(
            text: label,
            style: const TextStyle(color: Colors.black),
          ),
          TextSpan(
            text: displayed ? ' *' : '',
            style: const TextStyle(color: Colors.red),
          ),
        ],
      ),
    );
  }
}
