import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_popover.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/pages/product/components/goods_spec.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:popover/popover.dart';

class CartPage extends StatefulWidget {
  const CartPage({super.key});

  @override
  State<CartPage> createState() => _CartPageState();
}

class _CartPageState extends State<CartPage> {
  bool _isLoading = false;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  SiteListModel? siteData;
  MidCartModel _pageData = MidCartModel(goodsList: []);

  @override
  void initState() {
    super.initState();
    if (Global.isLogin.value == false) {
      Future.delayed(Duration(milliseconds: 200), () {
        onGotoLogin();
      });
    } else {
      onSiteData();
      onPageData();
    }
  }

  @override
  void dispose() {
    super.dispose();
  }

  Future<void> onPageData() async {
    try {
      setState(() {
        _isLoading = true;
      });
      dynamic res = await InquiryAPI.useGetCart({});
      if (res?['result']?['code'] == 200) {
        setState(() {
          _pageData = MidCartModel.fromJson(res['data']);
          _pageData.goodsList = _pageData.goodsList.map((goods) {
            goods.skuTotalQuantity = 0;
            goods.skuSelectedQuantity = 0;
            goods.selected = goods.skuList.every((sku) => sku.selected == true);
            goods.skuList = goods.skuList.map((sku) {
              sku.minIncreaseQuantity ??= goods.minIncreaseQuantity;
              goods.skuTotalQuantity =
                  (goods.skuTotalQuantity ?? 0) + (sku.buyQty ?? 0);

              if (sku.selected == true) {
                goods.skuSelected = true;
                goods.skuSelectedQuantity =
                    (goods.skuSelectedQuantity ?? 0) + (sku.buyQty ?? 0);
              }
              return sku;
            }).toList();

            return goods;
          }).toList();

          _pageData.selectAll =
              _pageData.goodsList.every((goods) => goods.selected == true);

          // mainController.onCartDataUpdate(data: res['data']);
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> onGotoLogin() async {
    try {
      await AuthHelper.showLoginModal(context, onAuthSuccess: () {
        onSiteData();
        onPageData();
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 获取国家
  Future<void> onSiteData() async {
    try {
      SiteListModel? res = await Global.getSiteData();
      if (res != null) {
        setState(() {
          siteData = res;
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 修改购物车商品
  Future<void> onUpdateCart(dynamic params) async {
    try {
      dynamic res = await InquiryAPI.useUpdateCart(params);
      if (res?['result']?['code'] == 200) {
        setState(() {
          _pageData = MidCartModel.fromJson(res['data']);
          _pageData.goodsList = _pageData.goodsList.map((goods) {
            goods.skuTotalQuantity = 0;
            goods.skuSelectedQuantity = 0;
            goods.selected = goods.skuList.every((sku) => sku.selected == true);
            goods.skuList = goods.skuList.map((sku) {
              sku.minIncreaseQuantity ??= goods.minIncreaseQuantity;
              goods.skuTotalQuantity =
                  (goods.skuTotalQuantity ?? 0) + (sku.buyQty ?? 0);

              if (sku.selected == true) {
                goods.skuSelected = true;
                goods.skuSelectedQuantity =
                    (goods.skuSelectedQuantity ?? 0) + (sku.buyQty ?? 0);
              }
              return sku;
            }).toList();

            return goods;
          }).toList();

          _pageData.selectAll =
              _pageData.goodsList.every((goods) => goods.selected == true);

          // mainController.onCartDataUpdate(data: res['data']);
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  // 删除购物车商品
  Future<void> onRemoveCart(dynamic params) async {
    try {
      dynamic res = await InquiryAPI.useRemoveCart(params);
      if (res?['result']?['code'] == 200) {
        setState(() {
          _pageData = MidCartModel.fromJson(res['data']);
          _pageData.goodsList = _pageData.goodsList.map((goods) {
            goods.skuTotalQuantity = 0;
            goods.skuSelectedQuantity = 0;
            goods.selected = goods.skuList.every((sku) => sku.selected == true);
            goods.skuList = goods.skuList.map((sku) {
              sku.minIncreaseQuantity ??= goods.minIncreaseQuantity;
              goods.skuTotalQuantity =
                  (goods.skuTotalQuantity ?? 0) + (sku.buyQty ?? 0);

              if (sku.selected == true) {
                goods.skuSelected = true;
                goods.skuSelectedQuantity =
                    (goods.skuSelectedQuantity ?? 0) + (sku.buyQty ?? 0);
              }
              return sku;
            }).toList();

            return goods;
          }).toList();

          _pageData.selectAll =
              _pageData.goodsList.every((goods) => goods.selected == true);

          // mainController.onCartDataUpdate(data: res['data']);
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<bool?> showConfirmationDialog(BuildContext context) {
    return showDialog<bool>(
      context: context,
      builder: (BuildContext context) {
        return Center(
          child: Container(
            height: screenHeight * 0.3,
            width: screenWidth * 0.95,
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 16.sp),
            decoration: BoxDecoration(
                color: Colors.white, borderRadius: BorderRadius.circular(8)),
            child: Column(
              children: [
                Text.rich(
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.black87,
                      decoration: TextDecoration.none,
                      height: 1.5,
                      fontFamily: "Roboto", // 指定字体家族
                    ),
                    TextSpan(children: [
                      TextSpan(
                        text:
                            'El importe de su producto es inferior a US\$ 2000,',
                      ),
                      TextSpan(
                          text:
                              ' los gastos de envío pueden ser más caros que el coste del producto',
                          style: TextStyle(
                            color: AppColors.primaryColor,
                          )),
                      TextSpan(
                        text: ', se recomienda aumentar la compra a US\$ 2000.',
                      ),
                    ])),
                Container(
                  margin: EdgeInsets.only(top: 24.sp),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      FancyButton(
                        onTap: () {
                          Navigator.of(context).pop();
                          onInquiryConfirm();
                        },
                        width: 164,
                        color: Colors.grey.shade300,
                        borderColor: Colors.grey.shade300,
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.sp, vertical: 10.sp),
                        borderRadius: BorderRadius.circular(24.0),
                        child: Text(
                            I18n.of(context)
                                    ?.translate("cm_common.buySubmit") ??
                                "",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.black87,
                              decoration: TextDecoration.none,
                              fontFamily: "Roboto",
                            )),
                      ),
                      FancyButton(
                        onTap: () {
                          Navigator.of(context).pop();
                        },
                        width: 148,
                        color: AppColors.primaryColor,
                        borderColor: Colors.grey.shade300,
                        padding: EdgeInsets.symmetric(
                            horizontal: 12.sp, vertical: 10.sp),
                        borderRadius: BorderRadius.circular(24.0),
                        child: Text(
                            I18n.of(context)?.translate("cm_common.buyAgain") ??
                                "",
                            style: TextStyle(
                              fontSize: 14.sp,
                              color: Colors.white,
                              decoration: TextDecoration.none,
                              fontFamily: "Roboto",
                            )),
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
        );
      },
    );
  }

  //全选
  void _onSelectAll(bool value) async {
    await onUpdateCart({
      'selectedAll': value,
    });
  }

  // 商品选中
  void onSelectGoods(MidCartGoodsModel goods, bool value) async {
    await onUpdateCart({
      'goodsId': goods.goodsId,
      'selected': value,
      'padc': goods.padc,
    });
  }

  // SKU选中
  void onSelectSku(
      MidCartGoodsModel goods, MidCartSkuModel sku, bool value) async {
    await onUpdateCart({
      'skuId': sku.skuId,
      'selected': value,
      'padc': goods.padc,
    });
  }

  // SKU数量修改
  void onCartQtyUpdate(
      MidCartGoodsModel goods, MidCartSkuModel sku, int value) async {
    await onUpdateCart({
      'skuId': sku.skuId,
      'selected': sku.selected,
      'quantity': value,
      'padc': goods.padc,
    });
  }

  // 删除商品
  void onDeleteGoods(MidCartGoodsModel goods) async {
    await onRemoveCart({
      'goodsId': goods.goodsId,
      'padc': goods.padc,
    });
  }

  // 删除SKU
  void onDeleteSku(MidCartGoodsModel goods, MidCartSkuModel sku) async {
    await onRemoveCart({
      'goodsId': goods.goodsId,
      'skuId': sku.skuId,
      'padc': goods.padc,
    });
  }

  void onGoFindSubmit() async {
    if ((_pageData.stat?.selectTotalSalePrice ?? 0) < 2000) {
      await showConfirmationDialog(context);
    } else {
      await onInquiryConfirm();
    }
  }

  // 询盘确认
  Future<void> onInquiryConfirm() async {
    try {
      SiteListModel? siteData = await Global.getSiteData();

      InquiryParam param = InquiryParam(
        siteId: siteData?.id,
        params: [],
      );
      for (MidCartGoodsModel goods in _pageData.goodsList) {
        for (MidCartSkuModel sku in goods.skuList) {
          if (sku.selected == true) {
            param.params?.add(InquirySkuParam(
              skuId: sku.skuId,
              quantity: sku.buyQty,
              spm: sku.spm,
              padc: goods.padc,
              routeId: goods.routeId,
            ));
          }
        }
      }

      dynamic res = await InquiryAPI.useGetInquiry(param.toJson());
      if (res?['result']?['code'] == 200) {
        await Global.setInquiryInfo(data: res['data']);
        Get.toNamed(AppRoutes.FindSubmit);
      } else {
        showErrorMessage(res?["result"]?["message"]);
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  String onFilterPriceRange(SkuStepPrice? price) {
    if (price?.end == -1) {
      return '>=${price?.start}';
    } else {
      if (price?.start == price?.end) {
        return '${price?.start}';
      } else {
        return '${price?.start}-${price?.end}';
      }
    }
  }

  // 判断当前所属价格区间
  Color? onStepPriceLast(MidCartGoodsModel goods, SkuStepPrice? stepPrice) {
    int total = goods.skuList.fold(0, (sum, sku) {
      return sum + sku.buyQty!;
    });
    if (stepPrice?.end == -1) {
      return Colors.red.shade300;
    } else {
      if (total >= (stepPrice?.start ?? 0) && total <= (stepPrice?.end ?? 0)) {
        return Colors.red.shade300;
      } else {
        return Colors.grey.shade500;
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;

    return AppScaffold(
        onPopInvoked: (result) {
          print("==>>TODO 5522: $result");
        },
        body: Stack(children: [
          Column(
            children: [
              _buildHeaderBar(),
              Expanded(child: SingleChildScrollView(
                child: StatefulBuilder(
                  builder: (context, setState) {
                    if (_pageData.goodsList.isNotEmpty) {
                      return _buildProductList(setState);
                    } else {
                      return _buildEmpty();
                    }
                  },
                ),
              )),
              Column(
                children: [
                  StatefulBuilder(
                    builder: (context, setState) {
                      // 底部固定结算栏
                      return _buildFooterBar(setState);
                    },
                  ),
                  Container(
                    padding: EdgeInsets.symmetric(vertical: 2),
                    child: AppTabbar(currentIndex: 3), // 购物车是第4个tab (index=3)
                  )
                ],
              )
            ],
          ),
          if (_isLoading)
            Positioned.fill(
              child: Container(
                color: Colors.black.withOpacity(0.1),
                child: Center(
                  child: RotationalLoadingWidget(),
                ),
              ),
            ),
        ]));
  }

  // 顶部标题栏
  Widget _buildHeaderBar() {
    return Container(
      padding: const EdgeInsets.only(bottom: 6),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(color: Colors.black12, blurRadius: 4, offset: Offset(0, 2))
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            margin: EdgeInsets.only(left: 12, top: 2),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Visibility(
                      visible: Get.arguments?['back'] != null,
                      child: GestureDetector(
                        onTap: () {
                          Get.back();
                        },
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 8),
                          child: Icon(
                            Icons.arrow_back_ios,
                            size: 22,
                          ),
                        ),
                      ),
                    ),
                    Text.rich(TextSpan(children: [
                      TextSpan(
                          text:
                              '${I18n.of(context)?.translate("cm_find.inquiryList") ?? ''} ',
                          style: TextStyle(
                            color: Colors.black87,
                          )),
                      TextSpan(
                          text: '(${_pageData.stat?.skuCount ?? 0})',
                          style: TextStyle(
                            color: Colors.black87,
                          )),
                    ])),
                  ],
                ),
                Spacer(),
                Container(
                  padding: EdgeInsets.only(right: 12),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.only(left: 4),
                        child: Icon(Icons.location_on_outlined, size: 20),
                      ),
                      Container(
                        padding: EdgeInsets.only(left: 2),
                        child: GestureDetector(
                            onTap: () {
                              showModalBottomSheet(
                                  context: context,
                                  isScrollControlled: true,
                                  isDismissible: true, // 点击蒙层关闭
                                  enableDrag: false, // 拖动关闭
                                  builder: (BuildContext context) {
                                    return SizedBox(
                                      height: screenHeight * 0.7,
                                      child: CountrySelectDrawer(
                                        onCountrySelect:
                                            (SiteListModel? siteInfo) {
                                          setState(() {
                                            siteData = siteInfo;
                                            Global.setSiteData(siteInfo);
                                          });
                                        },
                                        onCloseDrawer: () {
                                          Get.back();
                                        },
                                      ),
                                    );
                                  });
                            },
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                Text(I18n.of(context)
                                        ?.translate("cm_common.deliveryTo") ??
                                    ''),
                                Container(
                                  padding: EdgeInsets.only(left: 4),
                                  child: CachedNetworkImage(
                                    height: 14,
                                    width: 20,
                                    fit: BoxFit.cover,
                                    imageUrl: siteData?.logo ?? '',
                                    placeholder: (context, url) =>
                                        const CircularProgressIndicator(),
                                  ),
                                ),
                                Container(
                                  padding: EdgeInsets.only(left: 4),
                                  child: Text(siteData?.code ?? ''),
                                )
                              ],
                            )),
                      ),
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  Widget _buildProductList(StateSetter setState) {
    return ListView.builder(
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: _pageData.goodsList.length,
        itemBuilder: (context, index) {
          MidCartGoodsModel goods = _pageData.goodsList[index];
          return _buildGoodsItem(setState, goods, index);
        });
  }

  // 商品信息
  Widget _buildGoodsItem(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return Container(
      padding: EdgeInsets.all(2),
      margin: EdgeInsets.only(bottom: 2),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade300, width: 0.5),
      ),
      child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
        _buildGoodsDismissible(setState, goods, index),
        _buildSkuItems(setState, goods, index),
      ]),
    );
  }

  // 商品卡片
  Widget _buildGoodsDismissible(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return Dismissible(
        key: ValueKey(goods.goodsId ?? ""),
        direction: DismissDirection.endToStart,
        onDismissed: (direction) {
          setState(() {
            onDeleteGoods(goods);
          });
        },
        background: Container(
          color: Colors.red.shade500,
          alignment: Alignment.centerRight,
          child: const Padding(
            padding: EdgeInsets.only(right: 20),
            child: Icon(
              Icons.delete,
              color: Colors.white,
            ),
          ),
        ),
        movementDuration: const Duration(milliseconds: 200), // 删除动画速度
        dismissThresholds: {
          DismissDirection.endToStart: 0.5, // 滑动到 50% 时触发删除
        },
        child: _buildGoodsCard(setState, goods, index));
  }

  // 商品卡片
  Widget _buildGoodsCard(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return Container(
      padding: EdgeInsets.all(2),
      child: GestureDetector(
        onTap: () {
          Get.toNamed(AppRoutes.ProductPage, arguments: {
            'productId': goods.goodsId ?? "",
            "padc": goods.padc
          });
        },
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            SizedBox(
              width: 25, // 设置宽度
              child: Checkbox(
                value: goods.selected,
                activeColor: Colors.red,
                side: WidgetStateBorderSide.resolveWith(
                  (Set<WidgetState> states) {
                    if (states.contains(WidgetState.selected)) {
                      return const BorderSide(color: Colors.red);
                    }
                    return const BorderSide(color: Colors.grey);
                  },
                ),
                onChanged: (bool? newValue) {
                  setState(() {
                    onSelectGoods(goods, newValue == true ? true : false);
                  });
                },
              ),
            ),
            Container(
              padding: EdgeInsets.symmetric(horizontal: 2),
              child: CachedNetworkImage(
                imageUrl: goods.mainImageUrl ?? '',
                fit: BoxFit.cover,
                width: 75,
                height: 75,
              ),
            ),
            Container(
              width: screenWidth * 0.65,
              padding: EdgeInsets.only(left: 2),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(goods.goodsName ?? '',
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                      style: const TextStyle(fontSize: 12)),
                  Row(
                    children: [
                      Expanded(
                        flex: 6,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text('Código: ${goods.goodsNo}',
                                  style: TextStyle(
                                      color: Colors.grey, fontSize: 12)),
                              Visibility(
                                  visible: goods.paName != null,
                                  child: GestureDetector(
                                    onTap: () {
                                      NavigatorUtil.pushNamed(
                                          context, AppRoutes.ProductList,
                                          arguments: {
                                            "padc": goods.padc,
                                          });
                                    },
                                    child: Container(
                                      padding:
                                          EdgeInsets.symmetric(horizontal: 4),
                                      decoration: BoxDecoration(
                                        border: Border.all(
                                          color: Colors.red, // 边框颜色设为红色
                                          width: 0.5, // 边框宽度
                                        ),
                                        borderRadius: BorderRadius.circular(
                                            4.0), // 设置圆角大小
                                      ),
                                      child: Text(goods.paName ?? '',
                                          style: TextStyle(
                                              fontSize: 12, color: Colors.red)),
                                    ),
                                  )),
                              Text.rich(TextSpan(
                                  style: TextStyle(
                                      color: Colors.grey, fontSize: 12),
                                  children: [
                                    TextSpan(
                                      text:
                                          '${I18n.of(context)?.translate("cm_goods.minOrder") ?? ''}: ',
                                    ),
                                    TextSpan(
                                      text: '${goods.minBuyQuantity} ',
                                    ),
                                    TextSpan(
                                      text: '${goods.goodsPriceUnitName} ',
                                    ),
                                  ])),

                              // 预估运费
                              Row(
                                crossAxisAlignment:
                                    CrossAxisAlignment.start, // 子组件顶部对齐
                                children: [
                                  Text(
                                    I18n.of(context)!
                                        .translate("cm_goods.shippingCost"),
                                    style: TextStyle(
                                        fontSize: 12,
                                        color: Colors.grey.shade600),
                                  ),
                                  Visibility(
                                    visible: goods.estimateFreight != null,
                                    child: Text(setUnit(goods.estimateFreight),
                                        style: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade600)),
                                  ),
                                  Visibility(
                                    visible: goods.estimateFreight == null,
                                    child: Row(
                                      children: [
                                        Text(
                                          I18n.of(context)!.translate(
                                              "cm_goods.pendingConfirmation"),
                                          style: TextStyle(
                                              fontSize: 12,
                                              color: Colors.grey.shade600),
                                        ),
                                        SizedBox(width: 2), // 图标与文本的间距
                                        Tooltip(
                                          message: I18n.of(context)?.translate(
                                              "cm_goods.freightConfirmation"),
                                          preferBelow: false,
                                          verticalOffset: 12,
                                          triggerMode: TooltipTriggerMode.tap,
                                          showDuration:
                                              Duration(seconds: 5), // 显示持续时间
                                          textStyle: TextStyle(
                                            fontSize: 12,
                                            color: Colors.grey.shade800,
                                          ),
                                          decoration: BoxDecoration(
                                            color:
                                                Colors.amber.shade100, // 设置背景色
                                            borderRadius:
                                                BorderRadius.circular(4), // 圆角
                                            border: Border.all(
                                                color: Colors.black12,
                                                width: 1),
                                            boxShadow: [
                                              BoxShadow(
                                                color: Colors.amber
                                                    .withOpacity(0.2),
                                                blurRadius: 4,
                                                offset: Offset(0, 2),
                                              ),
                                            ],
                                          ),
                                          child: Icon(
                                            Icons.error_outline_sharp,
                                            size: 16,
                                            color: Colors.amber.shade600,
                                          ),
                                        )
                                      ],
                                    ),
                                  ),
                                ],
                              ),
                              // Visibility(
                              //     visible: goods.estimateFreight != null,
                              //     child: Text.rich(TextSpan(
                              //         style: TextStyle(
                              //             color: Colors.grey, fontSize: 12),
                              //         children: [
                              //           TextSpan(
                              //             text: I18n.of(context)?.translate(
                              //                     "cm_goods.shippingCost") ??
                              //                 '',
                              //           ),
                              //           TextSpan(
                              //             text: setUnit(goods.estimateFreight),
                              //           ),
                              //         ]))),
                              // Visibility(
                              //     visible: goods.estimateFreight == null,
                              //     child: Row())
                            ]),
                      ),
                      Expanded(
                          flex: 1,
                          child: InkWell(
                            onTap: () {
                              print('图标被点击: ${goods.goodsId}');
                              setState(() {
                                onDeleteGoods(goods);
                              });
                            },
                            splashColor: Colors.transparent, // 可选：自定义点击效果
                            highlightColor: Colors.transparent,
                            borderRadius: BorderRadius.circular(30), // 可选：圆角效果
                            child: Container(
                              padding: EdgeInsets.all(16), // 控制点击区域大小
                              child: Icon(Icons.delete,
                                  size: 24, color: Colors.grey.shade600),
                            ),
                          ))
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSkuItems(
      StateSetter setState, MidCartGoodsModel goods, int index) {
    return ListView.builder(
        padding: EdgeInsets.zero,
        physics: const NeverScrollableScrollPhysics(),
        shrinkWrap: true,
        itemCount: goods.skuList.length,
        itemBuilder: (context, index) {
          return _buildSkuDismissible(
              setState, goods, goods.skuList[index], index);
        });
  }

  Widget _buildSkuDismissible(StateSetter setState, MidCartGoodsModel goods,
      MidCartSkuModel sku, int index) {
    return Dismissible(
        key: ValueKey(sku.skuId ?? ""),
        direction: DismissDirection.endToStart,
        onDismissed: (direction) {
          setState(() {
            onDeleteSku(goods, sku);
          });
        },
        background: Container(
          color: Colors.red,
          alignment: Alignment.centerRight,
          child: const Padding(
            padding: EdgeInsets.only(right: 20),
            child: Icon(
              Icons.delete,
              color: Colors.white,
            ),
          ),
        ),
        movementDuration: const Duration(milliseconds: 200), // 删除动画速度
        dismissThresholds: {
          DismissDirection.endToStart: 0.5, // 滑动到 50% 时触发删除
        },
        child: _buildSkuCard(setState, goods, sku, index));
  }

  // SKU卡片
  Widget _buildSkuCard(StateSetter setState, MidCartGoodsModel goods,
      MidCartSkuModel sku, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 2, right: 2),
      child: Row(
        children: [
          SizedBox(
            width: 25,
            child: Checkbox(
              value: sku.selected,
              activeColor: Colors.red,
              side: WidgetStateBorderSide.resolveWith(
                (Set<WidgetState> states) {
                  if (states.contains(WidgetState.selected)) {
                    return const BorderSide(color: Colors.red);
                  }
                  return const BorderSide(color: Colors.grey);
                },
              ),
              onChanged: (bool? newValue) {
                setState(() {
                  // TODO : 更新商品选中状态
                  onSelectSku(goods, sku, newValue == true ? true : false);
                });
              },
            ),
          ),
          Container(
            width: screenWidth - 36,
            padding: EdgeInsets.symmetric(horizontal: 6, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(6),
            ),
            child: Column(
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 6),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        child: CachedNetworkImage(
                          imageUrl: sku.skuImage ?? '',
                          fit: BoxFit.cover,
                          width: 25,
                          height: 25,
                        ),
                      ),
                      Container(
                          width: screenWidth - 145,
                          padding: EdgeInsets.symmetric(horizontal: 2),
                          child: Text(
                            sku.skuName,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(fontSize: 12),
                          )),
                      Spacer(),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        child: InkWell(
                          onTap: () {
                            showModalBottomSheet(
                                context: context,
                                isScrollControlled: true,
                                isDismissible: true, // 点击蒙层关闭
                                enableDrag: false, // 拖动关闭
                                builder: (BuildContext context) {
                                  return SizedBox(
                                    height: screenHeight * 0.75,
                                    // child: Text("hllo"),
                                    child: GoodsSkuChangeDrawer(
                                        context: context,
                                        sku: sku,
                                        goodsId: goods.goodsId ?? '',
                                        onChangeSkuConfirm: (dynamic param) {
                                          onUpdateCart(param);
                                          Navigator.pop(context);
                                        }),
                                  );
                                });
                          },
                          child: const Icon(
                            Icons.arrow_forward_ios,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                      Container(
                        padding: EdgeInsets.symmetric(horizontal: 2),
                        child: GestureDetector(
                          onTap: () {
                            setState(() {
                              onDeleteSku(goods, sku);
                            });
                          },
                          child: const Icon(
                            Icons.delete,
                            color: Colors.grey,
                            size: 18,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  margin: EdgeInsets.only(bottom: 6),
                  child: Row(children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      child: Text(
                        setUnit(sku.salePrice),
                        style: TextStyle(
                            color: AppColors.primaryColor,
                            fontWeight: FontWeight.w500),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      child: Text(
                        "/ ${goods.goodsPriceUnitName}",
                        style: TextStyle(color: Colors.grey.shade500),
                      ),
                    ),
                    PopupMenuButton(
                      offset: Offset(-30, 18), // 调整弹出位置
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(10), // 圆角边框
                      ),
                      itemBuilder: (context) => [
                        PopupMenuItem(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Row(
                                children: [
                                  Icon(
                                    Icons.error_outline_sharp,
                                    size: 16,
                                  ),
                                  Container(
                                    padding: EdgeInsets.only(left: 4),
                                    child: Text(
                                      I18n.of(context)!
                                          .translate("cm_find.stepPrices"),
                                      style: TextStyle(
                                        fontSize: 14,
                                        color: Colors.black87,
                                      ),
                                    ),
                                  )
                                ],
                              ),
                              for (int i = 0; i < sku.stepPrices!.length; i++)
                                Container(
                                  padding: EdgeInsets.only(top: 6),
                                  child: Row(
                                    mainAxisAlignment:
                                        MainAxisAlignment.spaceBetween,
                                    children: [
                                      RichText(
                                        text: TextSpan(
                                          children: [
                                            TextSpan(
                                              text: onFilterPriceRange(
                                                  sku.stepPrices![i]),
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  color: onStepPriceLast(goods,
                                                      sku.stepPrices?[i])),
                                            ),
                                            TextSpan(
                                              text:
                                                  ' ${goods.goodsPriceUnitName}',
                                              style: TextStyle(
                                                  fontSize: 12,
                                                  color: onStepPriceLast(goods,
                                                      sku.stepPrices?[i])),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Container(
                                        padding: EdgeInsets.only(right: 6),
                                        child: Text(
                                          setUnit(sku.stepPrices?[i].price),
                                          style: TextStyle(
                                              fontSize: 12,
                                              color: onStepPriceLast(
                                                  goods, sku.stepPrices?[i])),
                                        ),
                                      )
                                    ],
                                  ),
                                )
                            ],
                          ),
                        ),
                      ],
                      child: Icon(
                        Icons.error_outline_sharp,
                        size: 16,
                        color: Colors.amber.shade600,
                      ), // 触发按钮
                    ),
                    Spacer(),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(6),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                int qty = sku.buyQty! >=
                                        (goods.minIncreaseQuantity ?? 1)
                                    ? sku.buyQty! - goods.minIncreaseQuantity!
                                    : 0;
                                onCartQtyUpdate(goods, sku, qty);
                              });
                            },
                            child: Icon(Icons.remove),
                          ),
                          Container(
                            width: 60,
                            padding: EdgeInsets.symmetric(horizontal: 4),
                            child: Center(
                              child: Text(sku.buyQty.toString()),
                            ),
                          ),
                          GestureDetector(
                            onTap: () {
                              setState(() {
                                int qty =
                                    sku.buyQty! + goods.minIncreaseQuantity!;
                                onCartQtyUpdate(goods, sku, qty);
                              });
                            },
                            child: Icon(Icons.add),
                          ),
                        ],
                      ),
                    ),
                  ]),
                ),
                Container(
                  height: 0.5,
                  color: Colors.grey.shade300,
                  margin: const EdgeInsets.only(bottom: 6),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 6),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Text(setUnit(sku.subtotalSalePrice),
                          style: TextStyle(
                              color: AppColors.primaryColor,
                              fontWeight: FontWeight.w500)),
                    ],
                  ),
                )
              ],
            ),
          )
        ],
      ),
    );
  }

  // 底部结算栏
  Widget _buildFooterBar(StateSetter setState) {
    double fontSize = 13.sp;

    return Container(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Column(
        children: [
          Container(
            padding: EdgeInsets.symmetric(horizontal: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    TriStateCheckbox(
                        value: _pageData.selectAll,
                        activeColor: Colors.red,
                        onChanged: (value) {
                          setState(() {
                            _onSelectAll(value == true ? true : false);
                          });
                        }),
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 4),
                      child: Text(
                        I18n.of(context)?.translate("cm_find_selectAll") ?? "",
                        style: TextStyle(
                            fontSize: fontSize,
                            color: Colors.black87,
                            fontWeight: FontWeight.w600),
                      ),
                    )
                  ],
                ),
                Row(
                  children: [
                    Container(
                      padding: EdgeInsets.symmetric(horizontal: 2),
                      child: Text(
                        '${I18n.of(context)?.translate("cm_find_itemsCost") ?? ''}: ',
                        style: TextStyle(
                          fontSize: fontSize,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                    Container(
                      padding: EdgeInsets.only(right: 6.sp),
                      child: Text(
                        setUnit(_pageData.stat?.selectTotalSalePrice ?? 0),
                        style: TextStyle(
                            fontSize: 14.sp,
                            color: Colors.black87,
                            fontWeight: FontWeight.w600),
                      ),
                    )
                  ],
                )
              ],
            ),
          ),
          Container(
            padding: EdgeInsets.only(right: 6.sp),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.end, // 子元素靠右排列
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 4),
                  child: Tooltip(
                    message: I18n.of(context)
                        ?.translate("cm_goods.freightAdjustmentPending"),
                    preferBelow: false,
                    verticalOffset: 12,
                    triggerMode: TooltipTriggerMode.tap,
                    showDuration: Duration(seconds: 5), // 显示持续时间
                    textStyle: TextStyle(
                      fontSize: fontSize,
                      color: Colors.black54,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.amber.shade100, // 设置背景色
                      borderRadius: BorderRadius.circular(4), // 圆角
                      border: Border.all(color: Colors.black12, width: 1),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.amber.withOpacity(0.2),
                          blurRadius: 4,
                          offset: Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Icon(
                      Icons.error_outline_sharp,
                      size: 16,
                      color: Colors.amber.shade600,
                    ),
                  ),
                ),
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 2, vertical: 4),
                  child: Text(
                    '${I18n.of(context)?.translate("cm_goods.estimatedShippingCost") ?? ''}: ',
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: fontSize,
                        color: Colors.black54,
                        fontWeight: FontWeight.w200),
                  ),
                ),
                Container(
                  padding: EdgeInsets.only(right: 6.sp),
                  child: Text(
                    _pageData.totalEstimateFreight != null
                        ? setUnit(_pageData.totalEstimateFreight ?? 0)
                        : I18n.of(context)
                                ?.translate("cm_goods.pendingConfirmation") ??
                            '',
                    overflow: TextOverflow.ellipsis,
                    style: TextStyle(
                        fontSize: fontSize,
                        color: Colors.black54,
                        fontWeight: FontWeight.w200),
                  ),
                )
              ],
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 2.sp),
            child: FancyButton(
                onTap: () {
                  print("==>>TODO 3442:");
                  onGoFindSubmit();
                },
                width: screenWidth,
                color: AppColors.primaryColor,
                borderRadius: BorderRadius.circular(6),
                child: Column(
                  children: [
                    Text(
                      I18n.of(context)?.translate("cm_find.inquireNow") ?? "",
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w500),
                    ),
                    Text(
                        I18n.of(context)
                                ?.translate("cm_find_confirmWithoutPay") ??
                            "",
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 14.sp,
                        )),
                  ],
                )),
          )
        ],
      ),
    );
  }

  // 空白页面
  Widget _buildEmpty() {
    return Container(
      margin: EdgeInsets.only(top: screenHeight * 0.2),
      child: Column(
        children: [
          Icon(
            Icons.airplane_ticket_outlined,
            size: 64,
            color: Colors.grey.shade300,
          ),
          Container(
            padding: EdgeInsets.symmetric(vertical: 12.sp),
            child: Text(
              I18n.of(context)?.translate("cm_find.noData") ?? '',
              style: TextStyle(fontSize: 18, color: Colors.grey),
            ),
          ),
          FancyButton(
              onTap: () {
                NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
              },
              padding: EdgeInsets.symmetric(horizontal: 32.sp, vertical: 8.sp),
              borderRadius: BorderRadius.circular(6.sp),
              child: Text(
                I18n.of(context)?.translate("cm_find.goHome") ?? '',
                style: TextStyle(color: Colors.white),
              ))
        ],
      ),
    );
  }
}

class CartItem {
  String name;
  double price;
  int quantity;
  String image;

  CartItem({
    required this.name,
    required this.price,
    required this.quantity,
    required this.image,
  });
}

class ListItems extends StatelessWidget {
  const ListItems({super.key});

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: ListView(
        padding: const EdgeInsets.all(8),
        children: [
          InkWell(
            onTap: () {
              Navigator.of(context)
                ..pop()
                ..push(
                  MaterialPageRoute<SecondRoute>(
                    builder: (context) => SecondRoute(),
                  ),
                );
            },
            child: Container(
              height: 50,
              color: Colors.amber[100],
              child: const Center(child: Text('Entry A')),
            ),
          ),
          const Divider(),
          Container(
            height: 50,
            color: Colors.amber[200],
            child: const Center(child: Text('Entry B')),
          ),
          const Divider(),
          Container(
            height: 50,
            color: Colors.amber[300],
            child: const Center(child: Text('Entry C')),
          ),
        ],
      ),
    );
  }
}

class SecondRoute extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Second Route'),
        automaticallyImplyLeading: false,
      ),
      body: Center(
        child: ElevatedButton(
          onPressed: () => Navigator.pop(context),
          child: const Text('Go back!'),
        ),
      ),
    );
  }
}

class Button extends StatelessWidget {
  const Button({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 80,
      height: 40,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.all(Radius.circular(5)),
        boxShadow: [BoxShadow(color: Colors.black26, blurRadius: 5)],
      ),
      child: GestureDetector(
        child: const Center(child: Text('Click Me')),
        onTap: () {
          showPopover(
            context: context,
            bodyBuilder: (context) => const ListItems(),
            onPop: () => print('Popover was popped!'),
            direction: PopoverDirection.bottom,
            backgroundColor: Colors.white,
            width: 200,
            height: 400,
            arrowHeight: 15,
            arrowWidth: 30,
          );
        },
      ),
    );
  }
}
