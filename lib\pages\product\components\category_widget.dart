import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CategoryWidget extends StatefulWidget {
  final String? categoryId;
  final List<GoodsListCategoryFilterModel> categories;
  final double screenWidth;
  final double itemHeight;
  final Duration animationDuration;
  final Function(bool, String, String) onCategoryChange;

  const CategoryWidget({
    super.key,
    required this.categories,
    this.screenWidth = 100.0,
    this.itemHeight = 24.0,
    this.animationDuration = const Duration(milliseconds: 300),
    required this.onCategoryChange,
    this.categoryId,
  });

  @override
  State<CategoryWidget> createState() => _CategoryWidgetState();
}

class _CategoryWidgetState extends State<CategoryWidget> {
  String? _expandedCategoryId;
  String? _selectedCategoryId;
  late ScrollController _scrollController;
  // 静态常量，只创建一次
  static const TextStyle _textStyle = TextStyle(color: Colors.black);
  // "全部"选项的特殊 ID
  static const String _allCateId = '0';

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    // 默认选中"全部"
    _selectedCategoryId = _allCateId;
    if (widget.categoryId != null && widget.categoryId != "") {
      _selectedCategoryId = widget.categoryId;
    }
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  // 全部/二级类目
  void _toggleCategory(GoodsListCategoryFilterModel? cate) {
    setState(() {
      if (_selectedCategoryId == _allCateId) {
        _selectCategory(cate?.id ?? "",
            isAll: _selectedCategoryId == _allCateId);

        widget.onCategoryChange(true, _allCateId, cate?.id ?? "");
      } else {
        if (cate != null) {
          widget.onCategoryChange(_selectedCategoryId == _allCateId,
              _selectedCategoryId ?? "", cate.id ?? "");
        }
      }
    });
  }

  // 一级
  void _selectCategory(String id, {bool isAll = false}) {
    setState(() {
      if (_selectedCategoryId == id) {
        _expandedCategoryId = _expandedCategoryId == null ? id : null;
      } else {
        GoodsListCategoryFilterModel? cateItem = _getCategoryById(id);
        if (cateItem?.children != null) {
          _expandedCategoryId = id;
        } else {
          _expandedCategoryId = null;
        }
      }
      _selectedCategoryId = id;
      if (isAll) {
        _expandedCategoryId = null;
      }

      // 如果选择了非"全部"的类目，滚动到该类目
      if (id != _allCateId) {
        _scrollToCategory(id);
      }
    });
  }

  GoodsListCategoryFilterModel? _getCategoryById(String categoryId) {
    bool exists =
        widget.categories.where((cat) => cat.id == categoryId).isNotEmpty;

    return exists
        ? widget.categories.firstWhere((cat) => cat.id == categoryId)
        : null;
  }

  void _scrollToCategory(String categoryId) {
    double targetOffset = 0;
    double targetWidth = 0;
    final index = widget.categories.indexWhere((cat) => cat.id == categoryId);
    if (index == -1) return;

    for (int i = 0; i < index; i++) {
      if (i <= index) {
        targetWidth = onCategoryWidthCal(widget.categories[i].name ?? "");
        targetOffset += onCategoryWidthCal(widget.categories[i].name ?? "");
      }
    }

    // 计算目标位置并滚动
    _scrollController.animateTo(
      targetOffset - (widget.screenWidth - targetWidth) / 2,
      duration: Duration(milliseconds: 300),
      curve: Curves.easeOut,
    );
  }

  double onCategoryWidthCal(String name) {
    if (name.length <= 5) {
      return name.length.toDouble() * 10.sp + 20.sp;
    } else if (name.length <= 10) {
      return name.length.toDouble() * 10.sp + 12.sp;
    } else if (name.length <= 15) {
      return name.length.toDouble() * 9.sp + 16.sp;
    } else if (name.length <= 20) {
      return name.length.toDouble() * 8.sp + 24.sp;
    } else if (name.length <= 25) {
      return name.length.toDouble() * 8.sp + 16.sp;
    } else if (name.length <= 30) {
      return name.length.toDouble() * 8.sp + 4.sp;
    } else if (name.length <= 35) {
      return name.length.toDouble() * 7.sp + 32.sp;
    } else {
      return name.length.toDouble() * 7.sp + 32.sp;
    }
  }

  @override
  Widget build(BuildContext context) {
    // screenWidth = MediaQuery.of(context).size.width;

    return Column(
      children: [
        // 一级类目水平滚动区域
        SizedBox(
          height: widget.itemHeight,
          child: ListView.builder(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            itemCount: widget.categories.length,
            itemBuilder: (context, index) {
              final category = widget.categories[index];
              final bool hasSubcategories =
                  category.children != null && category.children!.isNotEmpty;
              final bool isSelected = _selectedCategoryId == category.id;
              final bool isExpanded = _expandedCategoryId == category.id;
              double itemWidth = onCategoryWidthCal(category.name ?? "");

              return Container(
                width: itemWidth,
                margin: const EdgeInsets.only(left: 4),
                child: Row(
                  children: [
                    Expanded(
                      child: GestureDetector(
                        onTap: () => _selectCategory(category.id ?? ""),
                        child: Container(
                          decoration: BoxDecoration(
                            color: isSelected
                                ? AppColors.primaryColor
                                : Colors.grey[200],
                            borderRadius: BorderRadius.circular(4),
                          ),
                          padding: const EdgeInsets.symmetric(horizontal: 4),
                          alignment: Alignment.center,
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Text(
                                category.name ?? "",
                                style: TextStyle(
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.black54,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                              if (hasSubcategories)
                                Icon(
                                  isExpanded
                                      ? Icons.arrow_drop_up
                                      : Icons.arrow_drop_down,
                                  color: isSelected
                                      ? Colors.white
                                      : Colors.black54,
                                  size: 16,
                                ),
                            ],
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              );
            },
          ),
        ),
        // 一级类目展开的二级类目区域（抽屉式）

        Visibility(
            visible: _expandedCategoryId != null,
            child: Container(
              height: 200, // 抽屉高度
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: ListView.builder(
                itemCount: _getCategoryById(_expandedCategoryId ?? "")
                        ?.children
                        ?.length ??
                    0,
                itemBuilder: (context, index) {
                  final subcategory =
                      _getCategoryById(_expandedCategoryId ?? "")
                          ?.children?[index];
                  if (subcategory == null) return Container();

                  return GestureDetector(
                    onTap: () {
                      _toggleCategory(subcategory); // 选中后关闭展开
                    },
                    child: Container(
                      margin: EdgeInsets.only(bottom: 4),
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      alignment: Alignment.centerLeft,
                      child: Text(
                        subcategory.name ?? "",
                        softWrap: true, // 允许自动换行
                        maxLines: null, // 不限制行数
                        overflow: TextOverflow.fade, // 超出部分渐变消失
                        style: Global.textColor101,
                      ),
                    ),
                  );
                },
              ),
            )),
        Visibility(
            visible: _expandedCategoryId == "----",
            child: Container(
              height: 200, // 抽屉高度
              padding: EdgeInsets.symmetric(horizontal: 4, vertical: 8),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.grey.withOpacity(0.3),
                    blurRadius: 4,
                    offset: Offset(0, 2),
                  ),
                ],
              ),
              child: GridView.builder(
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: 2, // 每行3个
                  crossAxisSpacing: 6,
                  mainAxisSpacing: 6,
                  childAspectRatio: 2.5, // 宽高比
                ),
                itemCount: _getCategoryById(_expandedCategoryId ?? "")
                        ?.children
                        ?.length ??
                    0,
                itemBuilder: (context, index) {
                  final subcategory =
                      _getCategoryById(_expandedCategoryId ?? "")
                          ?.children?[index];
                  if (subcategory == null) return Container();

                  return GestureDetector(
                    onTap: () {
                      _toggleCategory(subcategory); // 选中后关闭展开
                    },
                    child: Container(
                      padding: const EdgeInsets.all(4),
                      decoration: BoxDecoration(
                        color: Colors.grey[100],
                        borderRadius: BorderRadius.circular(4),
                      ),
                      alignment: Alignment.center,
                      child: Text(
                        subcategory.name ?? "",
                        style: _textStyle,
                      ),
                    ),
                  );
                },
              ),
            ))
      ],
    );
  }
}
