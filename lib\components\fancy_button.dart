// 优雅的按钮
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:flutter/material.dart';

class FancyButton extends StatefulWidget {
  final Widget child; //子组件
  final Color? color; //背景色
  final double? width; //宽度
  final VoidCallback? onTap; //单击事件
  final double? borderWidth; //边框宽度
  final Color? borderColor; //边框颜色
  final EdgeInsetsGeometry? padding; //内边距
  final BorderRadiusGeometry? borderRadius; //圆角半径

  const FancyButton({
    super.key,
    required this.child,
    this.onTap,
    this.padding,
    this.color = AppColors.primaryColor,
    this.borderWidth,
    this.borderColor,
    this.borderRadius,
    this.width = 256,
  });

  @override
  State<FancyButton> createState() => _FancyButtonState();
}

class _FancyButtonState extends State<FancyButton>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );

    _opacityAnimation = Tween<double>(begin: 1.0, end: 0.7).animate(
      CurvedAnimation(
        parent: _controller,
        curve: Curves.easeOut,
      ),
    );
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTapDown: (_) => _controller.forward(),
      onTapUp: (_) {
        _controller.reverse();
        if (widget.onTap != null) widget.onTap!();
      },
      onTapCancel: () => _controller.reverse(),
      child: Container(
        width: widget.width ?? 128,
        decoration: BoxDecoration(
          color: widget.color ?? Colors.white, // 白色背景
          border: Border.all(
            width: widget.borderWidth ?? 0, // 边框宽度
            color: widget.borderColor ?? Colors.white, // 边框颜色
          ),
          borderRadius: widget.borderRadius ?? BorderRadius.circular(2.0), // 圆角
        ),
        alignment: Alignment.center, // 内容居中
        padding: widget.padding ?? EdgeInsets.all(2),
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: Opacity(
            opacity: _opacityAnimation.value,
            child: widget.child,
          ),
        ),
      ),
    );
  }
}
