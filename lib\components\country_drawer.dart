// 国家选择

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/services/home.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CountrySelectDrawer extends StatefulWidget {
  final SiteListModel? siteInfo;
  final Function() onCloseDrawer;
  final Function(SiteListModel? siteInfo) onCountrySelect;

  const CountrySelectDrawer({
    super.key,
    this.siteInfo,
    required this.onCloseDrawer,
    required this.onCountrySelect,
  });

  @override
  State<CountrySelectDrawer> createState() => _CountrySelectDrawerState();
}

class _CountrySelectDrawerState extends State<CountrySelectDrawer> {
  SiteListModel? siteInfo;
  double screenWidth = 0.0;
  List<SiteListModel>? siteList = [];

  @override
  void initState() {
    super.initState();
    siteInfo = widget.siteInfo;
    onSiteInfo();
  }

  // 国家列表
  Future<void> onSiteInfo() async {
    try {
      PageClickIncomingModel incomingRes = await Global.getIncomingConfig();
      setState(() {
        for (var item in incomingRes.siteList ?? []) {
          siteList?.add(item.copyWith());
        }

        for (var siteItem in siteList ?? []) {
          if (siteItem.id == siteInfo?.id) {
            siteInfo = siteItem;
            siteItem.selected = true;
          }
        }
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onSiteSelected(int siteId) async {
    try {
      setState(() {
        for (var siteItem in siteList ?? []) {
          if (siteItem.id == siteId) {
            siteInfo = siteItem;
            siteItem.selected = true;
          } else {
            siteItem.selected = false;
          }
        }
      });
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  Future<void> onSaveCountry() async {
    try {
      dynamic res = await HomeAPI.useSetCurrentSite({
        'id': siteInfo?.id,
      });
      if (res != null && res?['result']?['code'] == 200) {
        await Global.setSiteData(siteInfo);
        widget.onCountrySelect(siteInfo);
        widget.onCloseDrawer();
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    screenWidth = MediaQuery.of(context).size.width;

    return Container(
      decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(8)),
          boxShadow: [
            BoxShadow(
              color: Colors.black26,
              blurRadius: 10,
              spreadRadius: 0.5,
            )
          ]),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 顶部固定部分
          _buildFixedHeader(),
          // 中间可滚动部分
          Expanded(
            child: SingleChildScrollView(
              child: StatefulBuilder(builder: (context, setState) {
                return Container(
                  child: _buildSiteList(),
                );
              }),
            ),
          ),
          // 底部固定部分
          _buildFixedFooter(),
        ],
      ),
    );
  }

  Widget _buildFixedHeader() {
    return Container(
      padding: EdgeInsets.symmetric(vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2),
                child: Icon(Icons.location_on_outlined, size: 16),
              ),
              Container(
                padding: EdgeInsets.symmetric(horizontal: 2),
                child: Text(
                  I18n.of(context)!.translate("cm_common.selectYourLocation"),
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade800,
                  ),
                ),
              )
            ],
          ),
          Container(
            padding: EdgeInsets.only(top: 2),
            child: Center(
              child: Text(
                I18n.of(context)!.translate("cm_common.locationDesc"),
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade600,
                ),
              ),
            ),
          )
        ],
      ),
    );
  }

  // 国家列表
  Widget _buildSiteList() {
    if (siteList == null) {
      return Container();
    }

    return Column(
        children: siteList!.map((item) => _buildSiteItem(item)).toList());
  }

  Widget _buildSiteItem(SiteListModel siteItem) {
    return GestureDetector(
      onTap: () async {
        await onSiteSelected(siteItem.id);
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8, vertical: 8),
        decoration: BoxDecoration(
          border: Border(
            bottom: BorderSide(
              color: Colors.grey.shade200,
              width: 1.0,
            ),
          ),
          color: siteItem.selected ? Colors.grey.shade200 : Colors.white,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            CachedNetworkImage(
              height: 16,
              width: 24,
              fit: BoxFit.cover,
              imageUrl: siteItem.logo ?? '',
              placeholder: (context, url) => const CircularProgressIndicator(),
            ),
            Container(
              padding: EdgeInsets.only(left: 8),
              child: Text(
                siteItem.name ?? '',
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildFixedFooter() {
    return Container(
      margin: EdgeInsets.symmetric(horizontal: 6, vertical: 6),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text.rich(TextSpan(children: [
            TextSpan(
                text:
                    '${I18n.of(context)?.translate("cm_common.selectedCountry") ?? ''} ',
                style: TextStyle(
                  color: Colors.grey.shade600,
                )),
            TextSpan(
                text: siteInfo?.name ?? '',
                style: TextStyle(
                  color: Colors.red.shade600,
                )),
          ])),
          SizedBox(height: 6.sp),
          FancyButton(
            onTap: () {
              onSaveCountry();
            },
            width: screenWidth,
            color: AppColors.primaryColor,
            padding: EdgeInsets.symmetric(horizontal: 12.sp, vertical: 10.sp),
            borderRadius: BorderRadius.circular(24.0),
            child: Text(
                I18n.of(context)?.translate("cm_common.confirmLocation") ?? "",
                style: TextStyle(
                  color: Colors.white,
                )),
          ),
          // GestureDetector(
          //   onTap: () {
          //     onSaveCountry();
          //   },
          //   child: Container(
          //     margin: EdgeInsets.only(top: 8),
          //     padding: EdgeInsets.symmetric(vertical: 12.sp),
          //     decoration: BoxDecoration(
          //       color: Colors.red.shade800, // 背景色
          //       borderRadius: BorderRadius.circular(12), // 圆角半径
          //     ),
          //     alignment: Alignment.center, // 内容居中
          //     child: Text(
          //         I18n.of(context)?.translate("cm_common.confirmLocation") ??
          //             "",
          //         style: TextStyle(color: Colors.white)),
          //   ),
          // )
        ],
      ),
    );
  }
}
