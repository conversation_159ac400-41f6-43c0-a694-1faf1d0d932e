import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:flutter/material.dart';

class NoticeCard extends StatelessWidget {
  const NoticeCard({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
        height: 68,
        color: Colors.grey.shade200,
        margin: EdgeInsets.only(top: 2),
        padding: EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(10),
          child: Row(
            children: [
              Center(
                child: Icon(
                  Icons.contactless,
                  size: 32.0,
                ),
              ),
              Expanded(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                  child: Text(
                    I18n.of(context)?.translate('cm_guestHome.noticeTitle') ??
                        '',
                    style: TextStyle(fontSize: 12, color: Colors.red),
                    textAlign: TextAlign.left,
                  ),
                ),
              ),
            ],
          ),
        ));
  }
}
