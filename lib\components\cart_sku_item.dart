import 'package:chilat2_mall_app/models/basis.dart';
import 'package:flutter/material.dart';

class CartSkuItem extends StatefulWidget {
  final GoodsSkuInfoModel skuItem;
  final Function(GoodsSkuInfoModel, int) updateSkuQuantity;
  final Function(GoodsSkuInfoModel) decreaseQuantity;
  final Function(GoodsSkuInfoModel) increaseQuantity;

  const CartSkuItem({
    super.key,
    required this.skuItem, // 强制调用者传入 context
    required this.updateSkuQuantity,
    required this.decreaseQuantity,
    required this.increaseQuantity,
  });

  @override
  State<CartSkuItem> createState() => _CartSkuItemState();
}

class _CartSkuItemState extends State<CartSkuItem> {
  double skuNameWidth = 0.0;
  double skuPriceWidth = 0.0;
  double cartQtyWidth = 0.0;
  double screenWidth = 0.0;
  double screenHeight = 0.0;
  late FocusNode _focusNode;

  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _focusNode = FocusNode();

    _focusNode.addListener(() {
      if (!_focusNode.hasFocus) {
        onCartQtyUpdate();
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void onCartQtyUpdate() {
    final text = _controller.text.trim();
    if (text.isNotEmpty) {
      final newQuantity = int.tryParse(text) ?? 1;
      print("==>>TODO 1621: ${widget.skuItem.skuName}, $newQuantity");
      if (newQuantity > 0) {
        widget.updateSkuQuantity(widget.skuItem, newQuantity);
      } else {
        // 数量不能小于1，恢复原值
        _controller.text = widget.skuItem.cartQty.toString();
      }
    } else {
      // 输入为空，恢复原值
      _controller.text = widget.skuItem.cartQty.toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    _controller =
        TextEditingController(text: widget.skuItem.cartQty.toString());

    screenWidth = MediaQuery.of(context).size.width;
    screenHeight = MediaQuery.of(context).size.height;
    skuNameWidth = screenWidth * 0.43;
    skuPriceWidth = screenWidth * 0.22;
    cartQtyWidth = screenWidth * 0.3;

    return Container(
      padding: EdgeInsets.only(left: 6, top: 4, bottom: 4),
      child: Row(mainAxisAlignment: MainAxisAlignment.start, children: [
        SizedBox(
          width: skuNameWidth,
          child: Text(
            widget.skuItem.skuName!,
            maxLines: 2,
            softWrap: true,
            overflow: TextOverflow.ellipsis,
            style: TextStyle(fontSize: 14),
          ),
        ),
        SizedBox(
          width: skuPriceWidth,
          child: Center(
            child: Text(
              '${widget.skuItem.price ?? 0}',
              style: TextStyle(fontSize: 14),
            ),
          ),
        ),
        SizedBox(
          height: 32,
          width: cartQtyWidth,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white, // 设置背景颜色为白色
              borderRadius: BorderRadius.circular(6), // 设置圆角半径为 15
            ),
            child: Row(
              children: [
                // Container(
                //   width: cartQtyWidth * 0.2,
                //   padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                //   child: GestureDetector(
                //     onTap: () {
                //       setState(() {
                //         widget.decreaseQuantity(skuItem);
                //       });
                //     },
                //     child: Icon(
                //       Icons.remove,
                //       size: 16,
                //     ),
                //   ),
                // ),

                SizedBox(
                  width: cartQtyWidth * 0.25,
                  child: IconButton(
                    onPressed: () {
                      // print(
                      //     "==>>TODO 1621: ${widget.skuItem.skuName}, ${skuItem.skuName}");
                      widget.decreaseQuantity(widget.skuItem);
                    },
                    icon: const Icon(Icons.remove, color: Colors.grey),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),
                // 数量显示
                Container(
                  width: cartQtyWidth * 0.5,
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(
                    _controller.text,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                // SizedBox(
                //   width: cartQtyWidth * 0.6,
                //   child: TextField(
                //     controller: _controller,
                //     focusNode: _focusNode,
                //     textAlign: TextAlign.center,
                //     cursorColor: Colors.green, // 设置光标颜色
                //     decoration: InputDecoration(
                //       contentPadding: EdgeInsets.symmetric(vertical: 6),
                //       // 未聚焦时的边框样式
                //       enabledBorder: OutlineInputBorder(
                //         borderSide: BorderSide(color: Colors.white),
                //       ),
                //       // 聚焦时的边框样式
                //       focusedBorder: OutlineInputBorder(
                //         borderSide: BorderSide(color: Colors.white, width: 0.5),
                //       ),
                //     ),
                //     keyboardType: TextInputType.number,
                //     inputFormatters: [
                //       FilteringTextInputFormatter.digitsOnly,
                //       LengthLimitingTextInputFormatter(3),
                //     ],
                //     onTap: () {
                //       setState(() {});
                //     },
                //     onSubmitted: (value) => onCartQtyUpdate(),
                //     onEditingComplete: () {
                //       // TODO: 待完善
                //     },
                //   ),
                // ),
                // Container(
                //   width: cartQtyWidth * 0.2,
                //   padding: EdgeInsets.symmetric(horizontal: 4, vertical: 4),
                //   child: GestureDetector(
                //     onTap: () {
                //       setState(() {
                //         widget.increaseQuantity(skuItem);
                //       });
                //     },
                //     child: Icon(
                //       Icons.add,
                //       size: 16,
                //     ),
                //   ),
                // ),
                // IconButton(
                //   onPressed: () {
                //     widget.increaseQuantity(skuItem);
                //   },
                //   icon: const Icon(Icons.add_circle_outline, color: Colors.red),
                //   padding: EdgeInsets.zero,
                //   constraints: const BoxConstraints(),
                // ),
                SizedBox(
                  width: cartQtyWidth * 0.25,
                  child: IconButton(
                    onPressed: () {
                      widget.increaseQuantity(widget.skuItem);
                    },
                    icon: const Icon(Icons.add, color: Colors.grey),
                    padding: EdgeInsets.zero,
                    constraints: const BoxConstraints(),
                  ),
                ),
              ],
            ),
          ),
        ),
      ]),
    );
  }
}
