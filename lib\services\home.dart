import 'package:chilat2_mall_app/models/localization.dart';
import 'package:chilat2_mall_app/utils/request.dart';

/// 首页
class HomeAPI {
  static Future<Map<String, String>> useLocalizationData() async {
    var response = await RequestUtil().post('main/MallConfig/setLanguage',
        params: {"language": "es", "prefix": "cm_"});
    var tmp = LangConfigResp.fromJson(response);
    if (tmp.result.code == 200) {
      return tmp.data.trans;
    }
    return {};
  }

  // 首页数据查询
  static Future<dynamic> useHomePageData(dynamic data) async {
    var response =
        await RequestUtil().post('pages/HomePage/getPageData', params: data);

    return response;
  }

  // 推荐商品
  static Future<dynamic> useRecommendGoodsV2(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/HomePage/recommendGoodsV2', params: data);

    return response;
  }

  // 首页商品
  static Future<dynamic> useHomePageGoods(dynamic data) async {
    var response =
        await RequestUtil().post('pages/HomePage/homePageGoods', params: data);

    return response;
  }

  // 获取首页最新商品分页数据 & 获取商品列表分页数据
  static Future<dynamic> useGoodsPageListData(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/GoodsListPage/searchGoods', params: data);

    return response;
  }

  // 计算预估运费
  static Future<dynamic> useCalculateEstimateFreight(dynamic data) async {
    var response = await RequestUtil()
        .post('main/MallConfig/calculateEstimateFreight', params: data);

    return response;
  }

  // 设置当前站点的选择（传参为siteId，即配送国家ID）
  static Future<dynamic> useSetCurrentSite(dynamic data) async {
    var response = await RequestUtil()
        .post('main/MallConfig/setCurrentSite', params: data);

    return response;
  }
}
