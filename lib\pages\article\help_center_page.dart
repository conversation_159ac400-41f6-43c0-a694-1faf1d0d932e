import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:flutter/material.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';

class HelpCenterPage extends StatefulWidget {
  const HelpCenterPage({Key? key}) : super(key: key);

  @override
  State<HelpCenterPage> createState() => _HelpCenterPageState();
}

class _HelpCenterPageState extends State<HelpCenterPage> {
  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    return AppScaffold(
      showScrollToTopButton: true,
      backgroundColor: Colors.white,
      scrollController: scrollController,
      body: <PERSON><PERSON><PERSON>(
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 搜索栏
              const SearchHeader(
                showBackIcon: true,
              ),
              // 顶部标题块
              Container(
                width: double.infinity,
                padding:
                    EdgeInsets.symmetric(horizontal: 50.sp, vertical: 58.sp),
                color: const Color(0xFFF2F6F7),
                child: const Center(
                  child: Text(
                    'Centro de ayuda de Chilat shop',
                    style: TextStyle(
                      fontSize: 26,
                      fontWeight: FontWeight.w600,
                      height: 1.3,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ),
              // 三个卡片
              Padding(
                padding: EdgeInsets.only(right: 20.sp, left: 20.sp, top: 20.sp),
                child: Column(
                  children: [
                    _HelpCenterCard(
                      title: 'Preguntas frecuentes',
                      iconWidget: Container(
                        child: const Icon(Icons.question_answer_outlined,
                            color: AppColors.primaryColor, size: 36),
                      ),
                      onTap: () {
                        Get.toNamed(AppRoutes.FrequentlyQuestions);
                      },
                    ),
                    SizedBox(height: 20.sp),
                    _HelpCenterCard(
                      title: 'Garantías y Posventa',
                      iconWidget: Container(
                        child: const Icon(Icons.verified_user_rounded,
                            color: AppColors.primaryColor, size: 36),
                      ),
                      onTap: () {
                        Get.toNamed(AppRoutes.ArticlePage,
                            arguments: {'articleCode': '10002'});
                      },
                    ),
                    SizedBox(height: 20.sp),
                    _HelpCenterCard(
                      title: 'Formas de pago',
                      iconWidget: Container(
                        padding: const EdgeInsets.all(12),
                        child: const Icon(Icons.credit_card,
                            color: AppColors.primaryColor, size: 36),
                      ),
                      onTap: () {
                        Get.toNamed(AppRoutes.PaymentMethods);
                      },
                    ),
                  ],
                ),
              ),
              SizedBox(height: 20.sp),
              // 联系我们区块
              Container(
                width: double.infinity,
                color: Color(0xFFF6F6F8),
                padding:
                    EdgeInsets.symmetric(vertical: 50.sp, horizontal: 25.sp),
                child: Column(
                  children: const [
                    Text(
                      '¿No encuentras las respuestas que estás buscando?',
                      style: TextStyle(
                        color: Color(0xFF3C4043),
                        fontSize: 26,
                        fontWeight: FontWeight.w600,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                    SizedBox(height: 12),
                    Text(
                      'Contacta con nosotros, aquí estamos para ayudarte.',
                      style: TextStyle(
                        fontSize: 20,
                        height: 1.3,
                      ),
                      textAlign: TextAlign.center,
                    ),
                  ],
                ),
              ),

              // 底部信息栏
              const MyFooter(),
            ],
          ),
        ),
      ),
    );
  }
}

class _HelpCenterCard extends StatelessWidget {
  final String title;
  final VoidCallback onTap;
  final Widget iconWidget;
  const _HelpCenterCard({
    required this.title,
    required this.onTap,
    required this.iconWidget,
    Key? key,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        height: 180.sp,
        padding: EdgeInsets.symmetric(vertical: 28.sp, horizontal: 8.sp),
        decoration: BoxDecoration(
          color: const Color(0xFFFAFCFC),
          borderRadius: BorderRadius.circular(6.sp),
          border: Border.all(color: Color(0xFFF2F2F2)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.06),
              blurRadius: 10,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            iconWidget,
            SizedBox(height: 12.sp),
            Text(
              title,
              style: TextStyle(
                fontSize: 20.sp,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF2a2a2a),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
