import 'package:chilat2_mall_app/pages/mine/mine_controller.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

import 'package:chilat2_mall_app/services/user.dart';
import 'package:chilat2_mall_app/config/config.dart';
import 'package:chilat2_mall_app/utils/utils.dart';

class UpdatePasswordPage extends StatefulWidget {
  const UpdatePasswordPage({Key? key}) : super(key: key);

  @override
  State<UpdatePasswordPage> createState() => _UpdatePasswordPageState();
}

class _UpdatePasswordPageState extends State<UpdatePasswordPage> {
  late final MineController mineController;

  // 表单控制器
  final _formKey = GlobalKey<FormState>();
  final _oldPasswordController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  bool _showOldPassword = false;
  bool _showNewPassword = false;
  bool _showConfirmPassword = false;

  @override
  void initState() {
    super.initState();
    mineController = Get.isRegistered<MineController>()
        ? Get.find<MineController>()
        : Get.put(MineController());
  }

  @override
  void dispose() {
    _oldPasswordController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  // 验证密码格式
  String? _validateNewPassword(String? value) {
    if (value == null || value.isEmpty) {
      return I18n.of(context)?.translate('cm_common.inputNewPwd') ??
          "Por favor ingrese su nueva contraseña";
    }

    // 特殊字符匹配
    final specialCharPattern = RegExp(r'[^A-Za-z\d]');
    if (specialCharPattern.hasMatch(value)) {
      return I18n.of(context)?.translate('cm_common.pwdFormatTips') ??
          "No introduzca nada que no sean números y letras";
    }

    // 8-16位包含数字及字母
    final pattern = RegExp(r'^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{8,16}$');
    if (!pattern.hasMatch(value)) {
      return I18n.of(context)?.translate('cm_common.pwdLengthTips') ??
          "8-16 bit que contienen letras y números";
    }

    return null;
  }

  // 验证确认密码
  String? _validateConfirmPassword(String? value) {
    if (value == null || value.isEmpty) {
      return I18n.of(context)?.translate('cm_common.inputConPwd') ??
          "Por favor ingrese la confirmación de su nueva contraseña";
    }

    if (value != _newPasswordController.text) {
      return I18n.of(context)?.translate('cm_common.conPwdTips') ??
          "La nueva contraseña introducida dos veces es inconsistente";
    }

    return null;
  }

  // 忘记密码
  void _onForgetPasswordClick() {
    Get.toNamed(AppRoutes.ModifyPassword,
        arguments: {'pageSource': Get.currentRoute});
  }

  // 修改密码
  Future<void> _updatePassword() async {
    if (_formKey.currentState!.validate()) {
      try {
        final result = await UserAPI.useUpdatePassword({
          'oldPassword': _oldPasswordController.text,
          'newPassword': _newPasswordController.text,
          'conPassword': _confirmPasswordController.text,
        });

        if (result['result']['code'] == 200) {
          Get.snackbar(
            '',
            I18n.of(context)?.translate('cm_common.updatePwdSuccess') ??
                "Se modificó con éxito la contraseña",
            snackPosition: SnackPosition.TOP,
            titleText: const SizedBox.shrink(),
          );
          var user = LocalStorage().getJSON(USER_INFO);
          await _login(user?['email'], _newPasswordController.text);
        } else {
          Get.snackbar(
            '',
            result['result']['message'] ?? "",
            snackPosition: SnackPosition.TOP,
          );
        }
      } catch (e) {
        Get.snackbar(
          '错误',
          e.toString(),
          snackPosition: SnackPosition.TOP,
        );
      }
    }
  }

  // 登录请求
  Future<void> _login(String username, String password) async {
    try {
      final res = await UserAPI.useLogin({
        "username": username.trim(),
        "password": password.trim(),
      });

      if (res['result']['code'] == 200) {
        // 保存用户信息
        await LocalStorage().setJSON(USER_INFO, res['data']);

        // 更新用户状态
        await mineController.loadUserInfo();

        // 更新全局登录状态
        await Global.updateLoginStatus();

        // 清空输入框数据
        _clearInputFields();
      } else {
        Get.snackbar(
          '错误',
          res['result']['message'] ?? '登录失败',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      Get.snackbar(
        '错误',
        e.toString(),
        snackPosition: SnackPosition.TOP,
      );
    }
  }

  // 清空输入框数据
  void _clearInputFields() {
    _oldPasswordController.clear();
    _newPasswordController.clear();
    _confirmPasswordController.clear();

    // 移除所有输入框的焦点
    FocusScope.of(context).unfocus();

    // 重置密码显示状态
    setState(() {
      _showOldPassword = false;
      _showNewPassword = false;
      _showConfirmPassword = false;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        elevation: 0,
        backgroundColor: Colors.white,
        centerTitle: true,
        title: Text(
          I18n.of(context)?.translate('cm_user.updatePwd') ?? '修改密码',
        ),
        bottom: PreferredSize(
          preferredSize: Size.fromHeight(1.sp),
          child: Container(
            height: 1.sp,
            color: const Color(0xFFE5E5E5),
          ),
        ),
      ),
      body: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16.sp),
        child: Form(
          key: _formKey,
          child: ListView(
            padding: EdgeInsets.only(top: 20.sp, left: 8.sp, right: 8.sp),
            children: [
              // 旧密码输入
              Padding(
                padding: EdgeInsets.only(bottom: 16.sp),
                child: TextFormField(
                  controller: _oldPasswordController,
                  obscureText: !_showOldPassword,
                  decoration: InputDecoration(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          I18n.of(context)
                                  ?.translate('cm_common.oldPassword') ??
                              '旧密码',
                          style:
                              TextStyle(color: Colors.black, fontSize: 15.sp),
                        ),
                        Text(' *',
                            style: TextStyle(
                                color: AppColors.primaryColor,
                                fontSize: 15.sp)),
                      ],
                    ),
                    hintText:
                        I18n.of(context)?.translate('cm_common.inputOldPwd') ??
                            '请输入旧密码',
                    hintStyle:
                        TextStyle(color: Color(0xFFA8A8A8), fontSize: 12.sp),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showOldPassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                        color: const Color(0xFF7F7F7F),
                        size: 20.sp,
                      ),
                      onPressed: () {
                        setState(() {
                          _showOldPassword = !_showOldPassword;
                        });
                      },
                    ),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    errorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    focusedErrorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    errorStyle: TextStyle(
                      fontSize: 13.sp,
                      color: AppColors.primaryColor,
                      overflow: TextOverflow.visible,
                      height: 1.3,
                    ),
                    errorMaxLines: 3,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)
                              ?.translate('cm_common.inputOldPwd') ??
                          '请输入旧密码';
                    }
                    return null;
                  },
                ),
              ),
              // 新密码输入
              Padding(
                padding: EdgeInsets.only(bottom: 16.sp),
                child: TextFormField(
                  controller: _newPasswordController,
                  obscureText: !_showNewPassword,
                  decoration: InputDecoration(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          I18n.of(context)
                                  ?.translate('cm_common.newPassword') ??
                              '新密码',
                          style:
                              TextStyle(color: Colors.black, fontSize: 15.sp),
                        ),
                        Text(' *',
                            style: TextStyle(
                                color: AppColors.primaryColor,
                                fontSize: 15.sp)),
                      ],
                    ),
                    hintText:
                        I18n.of(context)?.translate('cm_common.inputNewPwd') ??
                            '请输入新密码',
                    hintStyle:
                        TextStyle(color: Color(0xFFA8A8A8), fontSize: 12.sp),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showNewPassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                        color: const Color(0xFF7F7F7F),
                        size: 20.sp,
                      ),
                      onPressed: () {
                        setState(() {
                          _showNewPassword = !_showNewPassword;
                        });
                      },
                    ),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    errorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    focusedErrorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    errorStyle: TextStyle(
                      fontSize: 13.sp,
                      color: AppColors.primaryColor,
                      overflow: TextOverflow.visible,
                      height: 1.3,
                    ),
                    errorMaxLines: 3,
                  ),
                  validator: _validateNewPassword,
                ),
              ),
              // 确认密码输入
              Padding(
                padding: EdgeInsets.only(bottom: 16.sp),
                child: TextFormField(
                  controller: _confirmPasswordController,
                  obscureText: !_showConfirmPassword,
                  decoration: InputDecoration(
                    label: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          I18n.of(context)
                                  ?.translate('cm_common.conPassword') ??
                              '确认密码',
                          style:
                              TextStyle(color: Colors.black, fontSize: 15.sp),
                        ),
                        Text(' *',
                            style: TextStyle(
                                color: AppColors.primaryColor,
                                fontSize: 15.sp)),
                      ],
                    ),
                    hintText:
                        I18n.of(context)?.translate('cm_common.inputConPwd') ??
                            '请再次输入新密码',
                    hintStyle:
                        TextStyle(color: Color(0xFFA8A8A8), fontSize: 12.sp),
                    suffixIcon: IconButton(
                      icon: Icon(
                        _showConfirmPassword
                            ? Icons.visibility_outlined
                            : Icons.visibility_off_outlined,
                        color: const Color(0xFF7F7F7F),
                        size: 20.sp,
                      ),
                      onPressed: () {
                        setState(() {
                          _showConfirmPassword = !_showConfirmPassword;
                        });
                      },
                    ),
                    enabledBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    focusedBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: Color(0xFFCCCCCC), width: 1),
                    ),
                    errorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    focusedErrorBorder: const UnderlineInputBorder(
                      borderSide:
                          BorderSide(color: AppColors.primaryColor, width: 1),
                    ),
                    errorStyle: TextStyle(
                      fontSize: 13.sp,
                      color: AppColors.primaryColor,
                      overflow: TextOverflow.visible,
                      height: 1.3,
                    ),
                    errorMaxLines: 3,
                  ),
                  validator: _validateConfirmPassword,
                ),
              ),
              // 忘记密码
              Align(
                alignment: Alignment.centerRight,
                child: GestureDetector(
                  onTap: _onForgetPasswordClick,
                  child: Padding(
                    padding: EdgeInsets.only(top: 10.sp, right: 2.sp),
                    child: Text(
                      I18n.of(context)?.translate('cm_common.forgotMyPwd') ??
                          '忘记密码？',
                      style: TextStyle(
                        color: Colors.blue,
                        fontSize: 14.sp,
                      ),
                    ),
                  ),
                ),
              ),
              SizedBox(height: 50.sp),
              // 提交按钮
              Center(
                child: SizedBox(
                  height: 38.sp,
                  child: ElevatedButton(
                    onPressed: _updatePassword,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppColors.primaryColor,
                      foregroundColor: Colors.white,
                      textStyle: TextStyle(fontSize: 18.sp),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(24.sp),
                      ),
                      minimumSize: Size(double.infinity, 38.sp),
                      elevation: 0,
                    ),
                    child: Text(
                      I18n.of(context)?.translate('cm_common.submitOpt') ??
                          '提交',
                      style: TextStyle(
                          fontSize: 18.sp, fontWeight: FontWeight.w500),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
