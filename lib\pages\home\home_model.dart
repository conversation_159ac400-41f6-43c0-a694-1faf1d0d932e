import 'package:flutter/material.dart';

class NewsItem {
  IconData? icon;
  String? title;
  Widget page;
  Color? bgcolor;

  NewsItem({this.icon, this.title, required this.page, this.bgcolor});
}

class ProcessItem {
  String title;
  IconData icon;
  String iconActive;
  List<String> contents;

  ProcessItem(
      {required this.title,
      required this.icon,
      required this.iconActive,
      required this.contents});
}

class ChooseItem {
  String title;
  String desc;

  ChooseItem({
    required this.title,
    required this.desc,
  });
}
