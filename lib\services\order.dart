import 'package:chilat2_mall_app/utils/request.dart';

class OrderService {
  // 订单管理-分页查询订单列表
  static Future<dynamic> useGetOrderList(dynamic data) async {
    var response = await RequestUtil().post('/pages/MallOrderPage/getOrderList',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 订单管理-取消订单
  static Future<dynamic> useCancelOrder(dynamic data) async {
    var response = await RequestUtil().post('/pages/MallOrderPage/cancelOrder',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 订单管理-获取取消订单理由列表
  static Future<dynamic> useGetCancelOrderReasonConfig(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderGetConfig/getCancelOrderReasonConfig',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单管理-查询订单详情
  static Future<dynamic> useGetOrderDetail(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/getOrderDetail',
        params: data,
        handleAuthErrors: true);
    return response;
  }

// 订单管理-未支付订单跳转到收银台，同时保存订单备注和所选路线等上下文信息
  static Future<dynamic> useOpenCashDesk(dynamic data) async {
    var response = await RequestUtil().post('/pages/MallOrderPage/openCashDesk',
        params: data, handleAuthErrors: true);
    return response;
  }

  // 查询收银台信息，包括待支付金额、支付方式列表、是否已支付
  static Future<dynamic> useGetCashDeskInfo(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/getCashDeskInfo',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单管理-收银台提交支付
  static Future<dynamic> useSubmitPayment(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/submitPayment',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单管理-查询支付结果
  static Future<dynamic> useQueryPayResult(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/queryPayResult',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单管理-确认收货
  static Future<dynamic> useConfirmReceipt(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/confirmReceipt',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单管理-获取可用优惠券列表
  static Future<dynamic> useGetCouponUsableList(dynamic data) async {
    var response = await RequestUtil().post(
        '/marketing/CouponDetail/getCouponUsableList',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // checkbox优惠券校验
  static Future<dynamic> useCheckAvailableList(dynamic data) async {
    var response = await RequestUtil().post(
        '/marketing/CouponDetail/checkAvailableList',
        params: data,
        handleAuthErrors: true);
    return response;
  }

  // 订单支付
  static Future<dynamic> useOrderPay(dynamic data) async {
    var response = await RequestUtil().post(
        '/pages/MallOrderPage/submitPayment',
        params: data,
        handleAuthErrors: true);
    return response;
  }
}
