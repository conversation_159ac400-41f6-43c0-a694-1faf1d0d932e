import 'package:chilat2_mall_app/services/services.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:chilat2_mall_app/services/user.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';

class MineAddressPage extends StatefulWidget {
  @override
  _MineAddressPageState createState() => _MineAddressPageState();
}

class _MineAddressPageState extends State<MineAddressPage> {
  final _formKey = GlobalKey<FormState>();
  var addressList = [].obs;
  var editForm = {}.obs;
  var countryList = [].obs;
  var provinceList = [].obs;
  var cityList = [].obs;
  var dialogVisible = false.obs;
  var currentDelAddr = {}.obs;
  var delVisible = false.obs;

  final TextEditingController _contactNameController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _regionController = TextEditingController();
  final TextEditingController _streetController = TextEditingController();
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _houseNoController = TextEditingController();
  final TextEditingController _postcodeController = TextEditingController();
  final TextEditingController _referLandmarkController =
      TextEditingController();
  final TextEditingController _provinceController = TextEditingController();
  final TextEditingController _cityController = TextEditingController();

  @override
  void dispose() {
    _contactNameController.dispose();
    _phoneController.dispose();
    _regionController.dispose();
    _streetController.dispose();
    _addressController.dispose();
    _houseNoController.dispose();
    _postcodeController.dispose();
    _referLandmarkController.dispose();
    _provinceController.dispose();
    _cityController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    onListUserAddress();
    onGetCountry();
  }

  Future<void> onListUserAddress() async {
    var res = await UserAPI.useListUserAddress({});
    if (res['result']['code'] == 200) {
      addressList.value = (res['data'] as List)
          .map((item) => Map<String, dynamic>.from(item))
          .toList();
    }
  }

  Future<void> onGetCountry() async {
    var res = await BasisAPI.useGetCountry({});
    if (res['result']['code'] == 200) {
      countryList.value = res['data'];
    }
  }

  void onOpenAddAddr([dynamic val]) async {
    print('onOpenAddAddr $val');
    _contactNameController.text = '';
    _phoneController.text = '';
    _regionController.text = '';
    _streetController.text = '';
    _addressController.text = '';
    _houseNoController.text = '';
    _postcodeController.text = '';
    _referLandmarkController.text = '';
    _provinceController.text = '';
    _cityController.text = '';

    editForm.clear();

    // 确保先加载国家列表
    if (countryList.isEmpty) {
      await onGetCountry();
    }

    if (val != null) {
      Map<String, dynamic> address = Map<String, dynamic>.from(val);

      editForm.value = {
        'id': address['id'],
        'contactName': address['contactName'],
        'phone': address['phone'],
        'countryId': address['countryId'],
        'country': address['country'],
        'province': address['province'],
        'city': address['city'],
        'region': address['region'],
        'street': address['street'],
        'address': address['address'],
        'houseNo': address['houseNo'],
        'postcode': address['postcode'],
        'referLandmark': address['referLandmark'],
        'addressLabel': address['addressLabel'],
        'isDefault': address['isDefault'] ?? false,
      };

      _contactNameController.text = address['contactName'] ?? '';
      _phoneController.text = address['phone'] ?? '';
      _regionController.text = address['region'] ?? '';
      _streetController.text = address['street'] ?? '';
      _addressController.text = address['address'] ?? '';
      _houseNoController.text = address['houseNo'] ?? '';
      _postcodeController.text = address['postcode'] ?? '';
      _referLandmarkController.text = address['referLandmark'] ?? '';
      _provinceController.text = address['province'] ?? '';
      _cityController.text = address['city'] ?? '';

      // 加载省市数据
      if (editForm['countryId'] != null) {
        await onSelectCountry('update');
      }
    } else {
      editForm['addressLabel'] = 'ADDRESS_LABEL_HOME';
    }

    dialogVisible.value = true;
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (BuildContext context) {
        return Container(
          height: MediaQuery.of(context).size.height * 0.9,
          child: Column(
            children: [
              // 固定标题
              Container(
                padding: EdgeInsets.symmetric(vertical: 12.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 2,
                      offset: Offset(0, 1),
                    ),
                  ],
                ),
                child: Stack(
                  children: [
                    // 关闭按钮
                    Positioned(
                      right: 8.sp,
                      top: -3.sp,
                      bottom: 0,
                      child: IconButton(
                        icon: Icon(
                          Icons.close,
                          color: Color(0xFF666666),
                          size: 20.sp,
                        ),
                        onPressed: () {
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    // 标题
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Text(
                          I18n.of(context)?.translate('cm_addr.addOrEdit') ??
                              'Agregar/editar Dirección',
                          style: TextStyle(
                            fontSize: 20.sp,
                            color: Color(0xFF333333),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
              // 可滚动的表单内容
              Expanded(
                child: SingleChildScrollView(
                  child: Padding(
                    padding: EdgeInsets.only(
                      bottom: MediaQuery.viewInsetsOf(context).bottom,
                    ),
                    child: buildAddressForm(),
                  ),
                ),
              ),
              // 固定底部按钮
              Container(
                padding: EdgeInsets.all(12.sp),
                decoration: BoxDecoration(
                  color: Colors.white,
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.1),
                      blurRadius: 4,
                      offset: Offset(0, -2),
                    ),
                  ],
                ),
                child: SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Color(0xFFE4393C),
                      padding: EdgeInsets.symmetric(vertical: 12),
                    ),
                    onPressed: onAddAddr,
                    child: Text(
                      I18n.of(context)?.translate('cm_addr.setAddr') ??
                          'Acceder y usar',
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    ).then((_) {
      dialogVisible.value = false;
    });
  }

  void onCloseAddAddr() {
    dialogVisible.value = false;
  }

  Future<void> onSelectCountry([String? type]) async {
    var res =
        await UserAPI.useListRegionByCountry({'id': editForm['countryId']});
    if (res['result']['code'] == 200) {
      onHandleRegion(res['data'], type);
    } else {
      onHandleRegion([], type);
      Get.snackbar('Error', res['result']['message']);
    }
  }

  void onHandleRegion(List<dynamic> data, [String? type]) {
    provinceList.value = data;

    // Only clear city list if we're not in update mode
    if (type != 'update') {
      cityList.clear();
    }

    // Don't clear province and city in update mode
    if (type == 'update' && editForm['province']?.isNotEmpty == true) {
      // Find the matching province and load its cities
      var matchProvince = provinceList.firstWhere(
        (item) => item['name'] == editForm['province'],
        orElse: () => null,
      );
      if (matchProvince != null) {
        cityList.value = matchProvince['children'] ?? [];
      }
    }
  }

  void onSelectProvince([String? type]) {
    var matchProvince = provinceList.firstWhere(
      (item) => item['name'] == editForm['province'],
      orElse: () => null,
    );

    if (matchProvince != null) {
      cityList.value = matchProvince['children'] ?? [];

      // Don't clear city in update mode if it exists
      if (type == 'update' && editForm['city']?.isNotEmpty == true) {
        var cityExists =
            cityList.any((city) => city['name'] == editForm['city']);
        if (!cityExists) {
          editForm['city'] = '';
          _cityController.text = '';
        }
      }
    } else {
      cityList.clear();
      if (type != 'update') {
        editForm['city'] = '';
        _cityController.text = '';
      }
    }
  }

  Future<void> onAddAddr() async {
    if (_formKey.currentState!.validate()) {
      var matchProvince = provinceList.firstWhere(
          (item) => item['name'] == editForm['province'],
          orElse: () => null);
      var matchCity = matchProvince != null
          ? matchProvince['children'].firstWhere(
              (item) => item['name'] == editForm['city'],
              orElse: () => null)
          : null;

      var params = {
        ...editForm,
        'addressLabel':
            editForm['addressLabel'] == 'ADDRESS_LABEL_HOME' ? 0 : 1,
        'cityCode': matchCity != null ? matchCity['code'] : null,
        'provinceCode': matchProvince != null ? matchProvince['code'] : null,
      };

      var res = await UserAPI.useSaveUserAddress(params);
      if (res['result']['code'] == 200) {
        Get.back();
        onListUserAddress();
        Get.snackbar(
            'Success',
            editForm['id'] != null
                ? (I18n.of(context)?.translate('cm_addr.editSuccess') ??
                    '¡Edición exitosa!')
                : (I18n.of(context)?.translate('cm_addr.addSuccess') ??
                    '¡Se agregó con éxito!'));
      } else {
        Get.snackbar('Error', res['result']['message']);
      }
    }
  }

  Future<void> onDelAddr() async {
    var params = {
      'id': currentDelAddr.value['id'],
    };
    var res = await UserAPI.useDeleteUserAddress(params);
    if (res['result']['code'] == 200) {
      onListUserAddress();
      Get.snackbar(
          'Success',
          I18n.of(context)?.translate('cm_addr.delSuccess') ??
              '¡Eliminación exitosa!');
    } else {
      Get.snackbar('Error', res['result']['message']);
    }
  }

  void onOpenDelDg(dynamic val) {
    currentDelAddr.value = val;
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          contentPadding: EdgeInsets.fromLTRB(24, 20, 24, 12),
          content: Container(
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  margin: EdgeInsets.only(bottom: 20),
                  child: Text(
                    I18n.of(context)?.translate('cm_addr.delAddrTip') ??
                        '¿Seguro que deseas eliminar esta dirección?',
                    style: TextStyle(fontSize: 14.sp, color: Color(0xFF333333)),
                    textAlign: TextAlign.center,
                  ),
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(right: 8),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFFF5F5F5),
                            foregroundColor: Color(0xFF333333),
                            padding: EdgeInsets.symmetric(vertical: 12),
                          ),
                          onPressed: () {
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            I18n.of(context)?.translate('cm_addr.cancelBtn') ??
                                'Cancelar',
                          ),
                        ),
                      ),
                    ),
                    Expanded(
                      child: Container(
                        margin: EdgeInsets.only(left: 8),
                        child: ElevatedButton(
                          style: ElevatedButton.styleFrom(
                            backgroundColor: Color(0xFFE4393C),
                            foregroundColor: Colors.white,
                            padding: EdgeInsets.symmetric(vertical: 12),
                          ),
                          onPressed: () {
                            onDelAddr();
                            Navigator.of(context).pop();
                          },
                          child: Text(
                            I18n.of(context)?.translate('cm_addr.confirmBtn') ??
                                'Confirmar',
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> onAddrToDefault(dynamic val) async {
    var res = await UserAPI.useAddressToDefault({'id': val['id']});
    if (res['result']['code'] == 200) {
      onListUserAddress();
      Get.snackbar(
          'Success',
          I18n.of(context)?.translate('cm_addr.editSuccess') ??
              '¡Edición exitosa!');
    } else {
      Get.snackbar('Error', res['result']['message']);
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: Color(0xFFF2F2F2),
      appBar: AppBar(
        backgroundColor: Color(0xFFFFFFFF),
        title: Text(I18n.of(context)?.translate('cm_user.direction') ??
            'Gestión de direcciones'),
        centerTitle: true,
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            Get.back();
          },
        ),
      ),
      body: Stack(
        children: [
          Obx(() {
            return Padding(
              padding: EdgeInsets.all(10.0.sp),
              child: addressList.isEmpty
                  ? Center(
                      child: Text(
                          I18n.of(context)?.translate('cm_address.noData') ??
                              'La lista de direcciones está vacía'),
                    )
                  : SingleChildScrollView(
                      child: Column(
                        children: [
                          ListView.builder(
                            shrinkWrap: true,
                            physics: NeverScrollableScrollPhysics(),
                            itemCount: addressList.length,
                            itemBuilder: (context, index) {
                              var address = addressList[index];
                              return Card(
                                color: Colors.white,
                                margin: EdgeInsets.only(bottom: 10.0.sp),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8.sp),
                                ),
                                child: Padding(
                                  padding: EdgeInsets.all(0.0.w),
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Padding(
                                        padding: EdgeInsets.all(10.0.sp),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Expanded(
                                              child: Column(
                                                crossAxisAlignment:
                                                    CrossAxisAlignment.start,
                                                children: [
                                                  Row(
                                                    children: [
                                                      Flexible(
                                                        child: Text(
                                                          address['contactName'] ??
                                                              '',
                                                          style: TextStyle(
                                                            fontSize: 14.0.sp,
                                                            fontWeight:
                                                                FontWeight.w500,
                                                            color: Color(
                                                                0xFF333333),
                                                          ),
                                                          overflow: TextOverflow
                                                              .ellipsis,
                                                          maxLines: 1,
                                                        ),
                                                      ),
                                                      SizedBox(width: 16.0.sp),
                                                      Text(
                                                        address['phone'] ?? '',
                                                        style: TextStyle(
                                                          fontSize: 14.0.sp,
                                                          color:
                                                              Color(0xFF333333),
                                                        ),
                                                      ),
                                                    ],
                                                  ),
                                                  SizedBox(height: 6.sp),
                                                  Text(
                                                    address['fullAddress'] ??
                                                        '',
                                                    style: TextStyle(
                                                      fontSize: 13.sp,
                                                      color: Color(0xFF333333),
                                                    ),
                                                    maxLines: 2,
                                                    overflow:
                                                        TextOverflow.ellipsis,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            IconButton(
                                              icon: Icon(
                                                Icons.arrow_forward_ios,
                                                size: 18.0.sp,
                                              ),
                                              onPressed: () =>
                                                  onOpenAddAddr(address),
                                            ),
                                          ],
                                        ),
                                      ),
                                      Divider(
                                        height: 6.sp,
                                        thickness: 1.sp,
                                        color: Color(0xFFDDDDDD),
                                      ),
                                      Padding(
                                        padding: EdgeInsets.symmetric(
                                            horizontal: 10.0.sp),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            if (address['isDefault'] == true)
                                              Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 4.0.sp,
                                                    vertical: 4.0.sp),
                                                decoration: BoxDecoration(
                                                  color: Color.fromRGBO(
                                                      240, 160, 32, 0.15),
                                                  borderRadius:
                                                      BorderRadius.circular(
                                                          4.0.r),
                                                ),
                                                child: Text(
                                                  I18n.of(context)?.translate(
                                                          'cm_addr.defaultAddr') ??
                                                      '默认地址',
                                                  style: TextStyle(
                                                    color: Color(0xFFF0A020),
                                                    fontSize: 13.sp,
                                                  ),
                                                ),
                                              ),
                                            if (address['isDefault'] != true)
                                              GestureDetector(
                                                onTap: () =>
                                                    onAddrToDefault(address),
                                                child: Row(
                                                  mainAxisSize:
                                                      MainAxisSize.min,
                                                  children: [
                                                    Icon(
                                                      Icons
                                                          .radio_button_unchecked,
                                                      size: 18.sp,
                                                      color: Color(0xFFB0B0B6),
                                                    ),
                                                    SizedBox(width: 2.0.sp),
                                                    Text(
                                                      I18n.of(context)?.translate(
                                                              'cm_addr.defaultAddr') ??
                                                          '设为默认',
                                                      style: TextStyle(
                                                        fontSize: 13.0.sp,
                                                        color:
                                                            Color(0xFF333333),
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),
                                            Padding(
                                              padding: EdgeInsets.fromLTRB(
                                                  0.0, 0.0, 0.0, 4.0.sp),
                                              child: Row(
                                                children: [
                                                  TextButton(
                                                    onPressed: () =>
                                                        onOpenAddAddr(address),
                                                    style: TextButton.styleFrom(
                                                      padding: EdgeInsets.zero,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons
                                                              .edit_location_alt_outlined,
                                                          size: 20.0.sp,
                                                          color:
                                                              Color(0xFF555555),
                                                        ),
                                                        SizedBox(width: 2.0.sp),
                                                        Text(
                                                          I18n.of(context)
                                                                  ?.translate(
                                                                      'cm_addr.editAddr') ??
                                                              '编辑',
                                                          style: TextStyle(
                                                            fontSize: 14.0.sp,
                                                            color: Color(
                                                                0xFF555555),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                  SizedBox(width: 8.0.sp),
                                                  TextButton(
                                                    onPressed: () =>
                                                        onOpenDelDg(address),
                                                    style: TextButton.styleFrom(
                                                      padding: EdgeInsets.zero,
                                                    ),
                                                    child: Row(
                                                      children: [
                                                        Icon(
                                                          Icons.delete_outline,
                                                          size: 20.0.sp,
                                                          color:
                                                              Color(0xFF555555),
                                                        ),
                                                        SizedBox(width: 2.0.sp),
                                                        Text(
                                                          I18n.of(context)
                                                                  ?.translate(
                                                                      'cm_addr.delAddr') ??
                                                              '删除',
                                                          style: TextStyle(
                                                            fontSize: 14.0.sp,
                                                            color: Color(
                                                                0xFF555555),
                                                          ),
                                                        ),
                                                      ],
                                                    ),
                                                  ),
                                                ],
                                              ),
                                            ),
                                          ],
                                        ),
                                      )
                                    ],
                                  ),
                                ),
                              );
                            },
                          ),
                          SizedBox(height: 80.0.h),
                        ],
                      ),
                    ),
            );
          }),
          Align(
            alignment: Alignment.bottomCenter,
            child: Container(
              color: Colors.white,
              width: double.infinity,
              padding:
                  EdgeInsets.symmetric(vertical: 12.0.h, horizontal: 16.0.w),
              child: ElevatedButton(
                onPressed: () => onOpenAddAddr(),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primaryColor,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(24.0.r),
                  ),
                  padding: EdgeInsets.symmetric(vertical: 6.0.sp),
                ),
                child: Text(
                  I18n.of(context)?.translate('cm_addr.addAddress') ?? '添加地址',
                  style: TextStyle(
                    fontSize: 14.0.sp,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildAddressForm() {
    return Container(
      color: Colors.white,
      child: SingleChildScrollView(
        child: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.sp, vertical: 2.sp),
          child: Form(
            key: _formKey,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                RequiredTextFormField(
                  labelText:
                      I18n.of(context)?.translate('cm_addr.contactName') ??
                          'Contacto',
                  controller: _contactNameController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'contactName': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.contactPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText: I18n.of(context)?.translate('cm_addr.phone') ??
                      'Teléfono',
                  keyboardType: TextInputType.phone,
                  controller: _phoneController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'phone': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.phonePh');
                    }
                    return null;
                  },
                ),
                DropdownButtonFormField(
                  selectedItemBuilder: (BuildContext context) {
                    return countryList.map<Widget>((country) {
                      return Text(
                        country['countryEsName'],
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      );
                    }).toList();
                  },
                  decoration: InputDecoration(
                    label: RichText(
                      text: TextSpan(
                        text:
                            '${I18n.of(context)?.translate('cm_addr.country') ?? 'País'} ',
                        style: TextStyle(color: Colors.black),
                        children: [
                          TextSpan(
                            text: '*',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    errorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    focusedErrorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    hintStyle: TextStyle(fontSize: 13.sp),
                  ),
                  value: editForm['countryId'],
                  items: countryList.map((country) {
                    return DropdownMenuItem(
                      value: country['id'],
                      child: Text(
                        country['countryEsName'],
                        style: TextStyle(
                          fontSize: 14.sp,
                          fontWeight: FontWeight.normal,
                          color: Colors.black,
                        ),
                      ),
                    );
                  }).toList(),
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'countryId': value,
                      'province': '',
                      'city': '',
                    };
                    _provinceController.text = '';
                    _cityController.text = '';
                    onSelectCountry();
                  },
                  validator: (value) {
                    if (value == null) {
                      return I18n.of(context)?.translate('cm_addr.countryPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText: I18n.of(context)?.translate('cm_addr.postcode') ??
                      'Código postal',
                  controller: _postcodeController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'postcode': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.postcodePh');
                    }
                    return null;
                  },
                ),
                TextFormField(
                  decoration: InputDecoration(
                    label: RichText(
                      text: TextSpan(
                        text:
                            '${I18n.of(context)?.translate('cm_addr.state')} ',
                        style: TextStyle(color: Colors.black),
                        children: [
                          TextSpan(
                            text: '*',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                    suffixIcon: PopupMenuButton<String>(
                      icon: Icon(Icons.arrow_drop_down),
                      onSelected: (String value) {
                        editForm.value = {
                          ...editForm.value,
                          'province': value,
                        };
                        _provinceController.text = value;
                        onSelectProvince();
                      },
                      itemBuilder: (BuildContext context) {
                        return provinceList
                            .map<PopupMenuItem<String>>((province) {
                          return PopupMenuItem<String>(
                            value: province['name'],
                            child: Text(province['name']),
                          );
                        }).toList();
                      },
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    errorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    focusedErrorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    hintStyle: TextStyle(fontSize: 13.sp),
                  ),
                  controller: _provinceController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'province': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.provincePh');
                    }
                    return null;
                  },
                ),
                TextFormField(
                  decoration: InputDecoration(
                    label: RichText(
                      text: TextSpan(
                        text: '${I18n.of(context)?.translate('cm_addr.city')} ',
                        style: TextStyle(color: Colors.black),
                        children: [
                          TextSpan(
                            text: '*',
                            style: TextStyle(color: Colors.red),
                          ),
                        ],
                      ),
                    ),
                    suffixIcon: PopupMenuButton<String>(
                      icon: Icon(Icons.arrow_drop_down),
                      onSelected: (String value) {
                        editForm.value = {
                          ...editForm.value,
                          'city': value,
                        };
                        _cityController.text = value;
                      },
                      itemBuilder: (BuildContext context) {
                        return cityList.map<PopupMenuItem<String>>((city) {
                          return PopupMenuItem<String>(
                            value: city['name'],
                            child: Text(city['name']),
                          );
                        }).toList();
                      },
                    ),
                    enabledBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    focusedBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Color.fromARGB(255, 102, 102, 102),
                        width: 1.0,
                      ),
                    ),
                    errorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    focusedErrorBorder: UnderlineInputBorder(
                      borderSide: BorderSide(
                        color: Colors.red,
                        width: 1.0,
                      ),
                    ),
                    hintStyle: TextStyle(fontSize: 13.sp),
                  ),
                  controller: _cityController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'city': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.cityPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText:
                      I18n.of(context)?.translate('cm_addr.regionCode') ??
                          'Colonia/Fraccionamiento',
                  controller: _regionController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'region': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)
                          ?.translate('cm_addr.regionCodePh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText:
                      I18n.of(context)?.translate('cm_addr.street') ?? 'Calle',
                  controller: _streetController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'street': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.streetPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText: I18n.of(context)?.translate('cm_addr.address') ??
                      'Dirección detallada',
                  controller: _addressController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'address': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.addressPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText: I18n.of(context)?.translate('cm_addr.houseNo') ??
                      'Número de casa',
                  controller: _houseNoController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'houseNo': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)?.translate('cm_addr.houseNoPh');
                    }
                    return null;
                  },
                ),
                RequiredTextFormField(
                  labelText:
                      I18n.of(context)?.translate('cm_addr.referLandmark') ??
                          'Referencias',
                  controller: _referLandmarkController,
                  onChanged: (value) {
                    editForm.value = {
                      ...editForm.value,
                      'referLandmark': value,
                    };
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return I18n.of(context)
                          ?.translate('cm_addr.referLandmarkPh');
                    }
                    return null;
                  },
                ),
                FormField(
                  builder: (FormFieldState<String> field) {
                    return InputDecorator(
                      decoration: InputDecoration(
                        border: InputBorder.none,
                        labelText: I18n.of(context)
                                ?.translate('cm_addr.addressLabel') ??
                            'Etiqueta',
                        labelStyle: TextStyle(
                          fontSize: 20.sp,
                          color: Colors.black,
                        ),
                        contentPadding:
                            EdgeInsets.only(top: 20.0.sp, bottom: 10.0.sp),
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          SizedBox(height: 8.0.sp),
                          Obx(() => Row(
                                mainAxisAlignment: MainAxisAlignment.start,
                                children: [
                                  Expanded(
                                    child: Container(
                                      decoration: BoxDecoration(
                                        borderRadius:
                                            BorderRadius.circular(30.sp),
                                      ),
                                      child: SegmentedButton<String>(
                                        segments: [
                                          ButtonSegment<String>(
                                            value: 'ADDRESS_LABEL_HOME',
                                            label: Padding(
                                              padding:
                                                  EdgeInsets.only(left: 8.sp),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(Icons.home, size: 16.sp),
                                                  SizedBox(width: 4.sp),
                                                  Text(
                                                    I18n.of(context)?.translate(
                                                            'cm_addr.home') ??
                                                        'Casa',
                                                    style: TextStyle(
                                                        fontSize: 14.sp),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            icon: null,
                                          ),
                                          ButtonSegment<String>(
                                            value: 'ADDRESS_LABEL_COMPANY',
                                            label: Padding(
                                              padding:
                                                  EdgeInsets.only(left: 8.sp),
                                              child: Row(
                                                mainAxisSize: MainAxisSize.min,
                                                children: [
                                                  Icon(Icons.business,
                                                      size: 16.sp),
                                                  SizedBox(width: 4.sp),
                                                  Text(
                                                    I18n.of(context)?.translate(
                                                            'cm_addr.business') ??
                                                        'Empresa',
                                                    style: TextStyle(
                                                        fontSize: 14.sp),
                                                  ),
                                                ],
                                              ),
                                            ),
                                            icon: null,
                                          ),
                                        ],
                                        selected: {
                                          editForm['addressLabel'] ??
                                              'ADDRESS_LABEL_HOME'
                                        },
                                        onSelectionChanged:
                                            (Set<String> value) {
                                          if (value.isNotEmpty) {
                                            editForm.value = {
                                              ...editForm.value,
                                              'addressLabel': value.first,
                                            };
                                          }
                                        },
                                        style: ButtonStyle(
                                          surfaceTintColor:
                                              MaterialStateProperty.all<Color>(
                                                  Colors.transparent),
                                          backgroundColor: MaterialStateProperty
                                              .resolveWith<Color>(
                                            (Set<MaterialState> states) {
                                              if (states.contains(
                                                  MaterialState.selected)) {
                                                return AppColors.primaryColor;
                                              }
                                              return Colors.white; // 未选中时为白色
                                            },
                                          ),
                                          foregroundColor: MaterialStateProperty
                                              .resolveWith<Color>(
                                            (Set<MaterialState> states) {
                                              if (states.contains(
                                                  MaterialState.selected)) {
                                                return Colors.white;
                                              }
                                              return Colors.black; // 未选中时文字为黑色
                                            },
                                          ),
                                          padding: MaterialStateProperty.all<
                                              EdgeInsets>(
                                            EdgeInsets.only(
                                              left: 10.sp,
                                              right: 10.sp,
                                              top: 10.sp,
                                              bottom: 10.sp,
                                            ),
                                          ),
                                          side: MaterialStateProperty.all<
                                              BorderSide>(
                                            BorderSide(
                                              color: Colors.grey.shade300,
                                              width: 1.0,
                                            ),
                                          ),
                                          elevation:
                                              MaterialStateProperty.all<double>(
                                                  0),
                                          shadowColor:
                                              MaterialStateProperty.all<Color>(
                                                  Colors.transparent),
                                        ),
                                      ),
                                    ),
                                  ),
                                ],
                              )),
                        ],
                      ),
                    );
                  },
                ),
                Obx(() => Padding(
                      padding: EdgeInsets.symmetric(horizontal: 0),
                      child: Row(
                        children: [
                          Checkbox(
                            value: editForm['isDefault'] ?? false,
                            shape: CircleBorder(),
                            materialTapTargetSize:
                                MaterialTapTargetSize.shrinkWrap,
                            visualDensity: VisualDensity.compact,
                            onChanged: (value) {
                              editForm.value = {
                                ...editForm.value,
                                'isDefault': value ?? false,
                              };
                            },
                          ),
                          Expanded(
                            child: GestureDetector(
                              onTap: () {
                                editForm.value = {
                                  ...editForm.value,
                                  'isDefault':
                                      !(editForm['isDefault'] ?? false),
                                };
                              },
                              child: Text(
                                I18n.of(context)
                                        ?.translate('cm_addr.defaultFlag') ??
                                    'Establecer como dirección predeterminada',
                                style: TextStyle(
                                  fontSize: 14.sp,
                                  color: Color(0xFF333333),
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                    )),
                SizedBox(height: 20.sp),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

class RequiredTextFormField extends StatelessWidget {
  final String labelText;
  final String? hintText;
  final FormFieldValidator<String>? validator;
  final ValueChanged<String>? onChanged;
  final TextInputType? keyboardType;
  final TextEditingController? controller;

  const RequiredTextFormField({
    Key? key,
    required this.labelText,
    this.hintText,
    this.validator,
    this.onChanged,
    this.keyboardType,
    this.controller,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return TextFormField(
      decoration: InputDecoration(
        label: RichText(
          text: TextSpan(
            text: labelText,
            style: const TextStyle(color: Colors.black),
            children: const [
              TextSpan(
                text: ' *',
                style: TextStyle(color: Colors.red),
              ),
            ],
          ),
        ),
        hintText: hintText,
        hintStyle: TextStyle(fontSize: 13.sp),
        enabledBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color.fromARGB(255, 102, 102, 102),
            width: 1.0,
          ),
        ),
        focusedBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: Color.fromARGB(255, 102, 102, 102),
            width: 1.0,
          ),
        ),
        errorBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: Colors.red,
            width: 1.0,
          ),
        ),
        focusedErrorBorder: UnderlineInputBorder(
          borderSide: BorderSide(
            color: Colors.red,
            width: 1.0,
          ),
        ),
      ),
      controller: controller,
      validator: validator,
      onChanged: onChanged,
      keyboardType: keyboardType,
    );
  }
}
