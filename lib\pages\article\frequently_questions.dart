//

import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/components/my_search_bar.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class FrequentlyQuestionsPage extends StatefulWidget {
  final String? columnId;

  const FrequentlyQuestionsPage({super.key, this.columnId});

  @override
  State<FrequentlyQuestionsPage> createState() =>
      _FrequentlyQuestionsPageState();
}

class _FrequentlyQuestionsPageState extends State<FrequentlyQuestionsPage> {
  bool _isLoading = true;
  final dynamic _pageData = {};
  bool _showBackToTopButton = false;
  // final List<ArticleCategoryTreeModel> _questionList = [];
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();

    onPageData();
    _scrollController.addListener(() {
      setState(() {
        _showBackToTopButton = _scrollController.offset > 200;
      });
    });
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void onScrollToTop() {
    _scrollController.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  Future<void> onPageData([bool? scroll]) async {
    try {
      dynamic res = await ArticleAPI.useListArticleCategory({
        'names': ["Preguntas Frecuentes"],
      });
      dynamic articleList = await onGetArticleList(Get.arguments?['columnId']);

      if (res?['result']?['code'] == 200) {
        setState(() {
          if (res?['data']?[0]?['children'] != null) {
            _pageData['columnList'] = res?['data']?[0]?['children'];
          }
          if (Get.arguments?['columnId'] != null) {
            dynamic columnIndex;
            for (int i = 0; i < _pageData?['columnList']?.length; i++) {
              if (_pageData['columnList']?[i]?.id ==
                  Get.arguments['columnId']) {
                columnIndex = i;
              }
            }

            if (articleList != null) {
              _pageData['columnList'][columnIndex]['articleList'] = articleList;
            }
          }
        });
      }
      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      showErrorMessage(e.toString());
    }
  }

  Future<dynamic> onGetArticleList(String? columnId) async {
    try {
      dynamic res = await ArticleAPI.useListArticleByCategoryId({
        "deviceType": "VISIT_DEVICE_TYPE_PC",
        "articleCategoryId": columnId,
      });
      if (res?['result']?['code'] == 200) {
        return res?['data'];
      } else {
        return null;
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            backgroundColor: Colors.white,
            body: Stack(children: [
              SearchHeader(
                showHomeIcon: true,
              ),
              Container(
                margin: EdgeInsets.only(top: 108),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Center(
                      child: Text(
                        "Preguntas frecuentes",
                        style: TextStyle(
                          fontSize: 16,
                        ),
                      ),
                    ),
                    const Divider(
                      color: Colors.black26,
                      thickness: 0.3,
                      indent: 0,
                      endIndent: 0,
                    ),
                  ],
                ),
              ),
              Container(
                margin: EdgeInsets.only(top: 132),
                child: SingleChildScrollView(
                  controller: _scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: EdgeInsets.only(bottom: 64),
                        child: ListView.builder(
                            padding: EdgeInsets.zero,
                            physics: const NeverScrollableScrollPhysics(),
                            shrinkWrap: true,
                            itemCount: _pageData['columnList']?.length,
                            itemBuilder: (context, index) {
                              dynamic question =
                                  _pageData['columnList']?[index];
                              return QuestionExpansionTile(
                                index: index,
                                question: question,
                              );
                            }),
                      ),
                      MyFooter()
                    ],
                  ),
                ),
              ),
              if (_showBackToTopButton)
                Positioned(
                  bottom: 20,
                  right: 20,
                  child: FloatingActionButton(
                    onPressed: onScrollToTop,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(64),
                    ),
                    child: const Icon(Icons.arrow_upward),
                  ),
                ),
            ]),
          );
  }
}

class QuestionExpansionTile extends StatefulWidget {
  final int index;
  final dynamic question;
  const QuestionExpansionTile(
      {super.key, required this.question, required this.index});

  @override
  State<QuestionExpansionTile> createState() => _QuestionExpansionTileState();
}

class _QuestionExpansionTileState extends State<QuestionExpansionTile> {
  bool _isExpanded = false;
  List<dynamic> _articleList = [];

  @override
  void initState() {
    super.initState();
    onPageData();
  }

  Future<dynamic> onPageData() async {
    try {
      dynamic res = await ArticleAPI.useListArticleByCategoryId({
        "deviceType": "VISIT_DEVICE_TYPE_PC",
        "articleCategoryId": widget.question['id'],
      });
      if (res?['result']?['code'] == 200) {
        setState(() {
          _articleList = res?['data'] ?? [];
        });
      } else {
        return null;
      }
    } catch (e) {
      showErrorMessage(e.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        ListTile(
          title: Text(
            '${widget.index + 1}. ${widget.question['name']}',
            style: TextStyle(color: _isExpanded ? Colors.red : Colors.black),
          ),
          trailing: Icon(
            _isExpanded ? Icons.expand_less : Icons.expand_more,
            color: _isExpanded ? Colors.red : Colors.black,
          ),
          onTap: () {
            setState(() {
              _isExpanded = !_isExpanded;
            });
          },
        ),
        AnimatedCrossFade(
          firstChild: Container(height: 0),
          secondChild: Padding(
            padding: EdgeInsets.symmetric(horizontal: 12),
            child: ListView.builder(
                padding: EdgeInsets.zero,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                itemCount: _articleList.length,
                itemBuilder: (context, index) {
                  dynamic article = _articleList[index];
                  return _buildArticleItem(
                      article, index + 1 == _articleList.length);
                }),
          ),
          crossFadeState: _isExpanded
              ? CrossFadeState.showSecond
              : CrossFadeState.showFirst,
          duration: Duration(milliseconds: 300),
        ),
      ],
    );
  }

  Widget _buildArticleItem(dynamic article, bool isLast) {
    return Container(
      padding: EdgeInsets.only(top: 8, bottom: 8, left: 6),
      decoration: BoxDecoration(
        border: Border(
          bottom: BorderSide(
            color: isLast ? Colors.white : Colors.black26,
            width: isLast ? 0 : 0.3,
          ),
        ),
      ),
      child: GestureDetector(
        onTap: () {
          Get.toNamed(AppRoutes.ArticleFAQ, arguments: {
            'id': article['id'],
            'title': article['title'],
            'columnId': widget.question['id'],
            'columnTitle': widget.question['name'],
          });
        },
        child: Text(
          article['title'],
          style: TextStyle(color: Colors.grey.shade700),
        ),
      ),
    );
  }
}
