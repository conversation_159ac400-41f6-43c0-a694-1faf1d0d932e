import 'package:chilat2_mall_app/utils/utils.dart';

class InquiryAPI {
  // 购物车列表
  static Future<dynamic> useGetCart(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/Cart/getCart', params: data);

    return response;
  }

  // 获取购物车统计信息
  static Future<dynamic> useGetCartStat(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/Cart/getCartStat', params: data);

    return response;
  }

  // 添加购物车
  static Future<dynamic> useAddCart(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/Cart/addCart', params: data);

    return response;
  }

  // 修改购物车商品
  static Future<dynamic> useUpdateCart(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/Cart/updateCart', params: data);

    return response;
  }

  // 删除购物车商品
  static Future<dynamic> useRemoveCart(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/Cart/removeCart', params: data);

    return response;
  }

  // 获取询盘信息
  static Future<dynamic> useGetInquiry(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/GoodsLooking/inquiry', params: data);

    return response;
  }

  // 提交询盘
  static Future<dynamic> useSubmitInquiry(dynamic data) async {
    var response =
        await RequestUtil().post('inquiry/GoodsLooking/submit', params: data);

    return response;
  }

  // 保存求购信息
  static Future<dynamic> useGoodsLookingSave(dynamic data) async {
    var response =
        await RequestUtil().post('commodity/GoodsLooking/save', params: data);

    return response;
  }

  // 暂存求购信息
  static Future<dynamic> useGoodsLookingTemSave(dynamic data) async {
    var response = await RequestUtil()
        .post('commodity/GoodsLooking/saveTemporary', params: data);

    return response;
  }

// 提交找货信息
  static Future<dynamic> useSubmitGoodsFind(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/MallGoodsFindPage/submitGoodsFind', params: data);

    return response;
  }

  // 提交找货信息
  static Future<dynamic> useGoodsFindDetail(dynamic data) async {
    var response = await RequestUtil()
        .post('pages/MallGoodsFindPage/useGoodsFindDetail', params: data);

    return response;
  }
}
