{"appPreferencesBuildSettings": {}, "buildConfigurations": [{"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "ENABLE_TESTABILITY": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_DYNAMIC_NO_PIC": "NO", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_OPTIMIZATION_LEVEL": "0", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_DEBUG=1 DEBUG=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "INCLUDE_SOURCE", "MTL_FAST_MATH": "YES", "ONLY_ACTIVE_ARCH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "DEBUG", "SWIFT_OPTIMIZATION_LEVEL": "-<PERSON><PERSON>", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98814b7e2c3bac55ee99d78eaa8d1ec61e", "name": "Debug"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_PROFILE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e98c22f26ca3341c3062f2313dc737070d4", "name": "Profile"}, {"buildSettings": {"ALWAYS_SEARCH_USER_PATHS": "NO", "CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED": "YES", "CLANG_ANALYZER_NONNULL": "YES", "CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION": "YES_AGGRESSIVE", "CLANG_CXX_LANGUAGE_STANDARD": "gnu++14", "CLANG_CXX_LIBRARY": "libc++", "CLANG_ENABLE_MODULES": "YES", "CLANG_ENABLE_OBJC_ARC": "YES", "CLANG_ENABLE_OBJC_WEAK": "YES", "CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING": "YES", "CLANG_WARN_BOOL_CONVERSION": "YES", "CLANG_WARN_COMMA": "YES", "CLANG_WARN_CONSTANT_CONVERSION": "YES", "CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS": "YES", "CLANG_WARN_DIRECT_OBJC_ISA_USAGE": "YES_ERROR", "CLANG_WARN_DOCUMENTATION_COMMENTS": "YES", "CLANG_WARN_EMPTY_BODY": "YES", "CLANG_WARN_ENUM_CONVERSION": "YES", "CLANG_WARN_INFINITE_RECURSION": "YES", "CLANG_WARN_INT_CONVERSION": "YES", "CLANG_WARN_NON_LITERAL_NULL_CONVERSION": "YES", "CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF": "YES", "CLANG_WARN_OBJC_LITERAL_CONVERSION": "YES", "CLANG_WARN_OBJC_ROOT_CLASS": "YES_ERROR", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "YES", "CLANG_WARN_RANGE_LOOP_ANALYSIS": "YES", "CLANG_WARN_STRICT_PROTOTYPES": "YES", "CLANG_WARN_SUSPICIOUS_MOVE": "YES", "CLANG_WARN_UNGUARDED_AVAILABILITY": "YES_AGGRESSIVE", "CLANG_WARN_UNREACHABLE_CODE": "YES", "CLANG_WARN__DUPLICATE_METHOD_MATCH": "YES", "COPY_PHASE_STRIP": "NO", "DEBUG_INFORMATION_FORMAT": "dwarf-with-dsym", "ENABLE_NS_ASSERTIONS": "NO", "ENABLE_STRICT_OBJC_MSGSEND": "YES", "GCC_C_LANGUAGE_STANDARD": "gnu11", "GCC_NO_COMMON_BLOCKS": "YES", "GCC_PREPROCESSOR_DEFINITIONS": "POD_CONFIGURATION_RELEASE=1 $(inherited)", "GCC_WARN_64_TO_32_BIT_CONVERSION": "YES", "GCC_WARN_ABOUT_RETURN_TYPE": "YES_ERROR", "GCC_WARN_UNDECLARED_SELECTOR": "YES", "GCC_WARN_UNINITIALIZED_AUTOS": "YES_AGGRESSIVE", "GCC_WARN_UNUSED_FUNCTION": "YES", "GCC_WARN_UNUSED_VARIABLE": "YES", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MTL_ENABLE_DEBUG_INFO": "NO", "MTL_FAST_MATH": "YES", "PRODUCT_NAME": "$(TARGET_NAME)", "STRIP_INSTALLED_PRODUCT": "NO", "SWIFT_COMPILATION_MODE": "wholemodule", "SWIFT_OPTIMIZATION_LEVEL": "-O", "SWIFT_VERSION": "5.0", "SYMROOT": "${SRCROOT}/../build"}, "guid": "bfdfe7dc352907fc980b868725387e9828903703a9fe9e3707306e58aab67b51", "name": "Release"}], "classPrefix": "", "defaultConfigurationName": "Release", "developmentRegion": "en", "groupTree": {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98d0b25d39b515a574839e998df229c3cb", "path": "../Podfile", "sourceTree": "SOURCE_ROOT", "type": "file"}, {"children": [{"children": [{"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98bc29f4f26de368d8155a6400aed4f7bf", "path": "Flutter.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98624fa9f496f746e2476d7b3831cf14db", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98ed9c0ae9675162c46f2bdff200b5987a", "path": "Flutter.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988ce48ddc098538df207f64da734dda97", "path": "Flutter.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98da45c4d554b37d626fe1510f05b4cb15", "name": "Support Files", "path": "../Pods/Target Support Files/Flutter", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98488e84226ffee2e9e5dfd24664a4a1a4", "name": "Flutter", "path": "../Flutter", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d3d322cd5f02dabf4e6d138b6003b877", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/CredentialDatabase.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98759a2a2d8e79d645434612336813063b", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98911f5d6248cbc853332fb55cc5746807", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebViewFlutterPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984966706fddb6cef2822e7ccbc786f9ce", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/ISettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9831dd367ec730091cf8d2af32ebfde5f3", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/LeakAvoider.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98704315d6837a9d0ff06f4160ff035f8f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/MyCookieManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98894cae77cef9d856c9f84369e4a75138", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/MyWebStorageManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985c10ce6d35b81d15f7c5e3864ba01dfa", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PlatformUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9827b032f52c3ebdbe98433a51a04c8e18", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SwiftFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f50625f9d3bc1c55dcf94932816e972f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Util.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98daa8e77d54d00adf63469cfc49949fa8", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/WKProcessPoolManager.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e4290ed842c3f9214c83bf3398190712", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b85e75ad2cfc01ab6cef79a99ebf3ca7", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f32bd462acb8b15d57b7dd24736e6ab8", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/FindInteraction/FindInteractionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9892cac0685091d7e372d411f295ac3e84", "name": "FindInteraction", "path": "FindInteraction", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98369033c851dbae2ff36354adaffaf446", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98225f4abfb5ac863666d54602d7d64c79", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessInAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b09fb27f002d736cdb1f6b6f8139f5d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/HeadlessInAppWebView/HeadlessWebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989f467a97a7f4110a2ed2a805eae18235", "name": "HeadlessInAppWebView", "path": "HeadlessInAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980b8adfd6ddf1eebe4e7a9cc76a41b346", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987de87a994f8886df14018676776d60b5", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980c2191b8cd7535fce54f8efdc267d2a1", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98aa4f9fa602b83742dbdca0ce6df4da67", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserNavigationController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d1e90c08d75f2034ab30aacb7c9ca925", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985af05b566225efac6e763adcf8ee7f52", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppBrowser/InAppBrowserWebViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98fea59e7ada08bf4578e16f421699bb5f", "name": "InAppBrowser", "path": "InAppBrowser", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9836754dd8db48443e5c2dc35fae631537", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/ContextMenuSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9825193cd024b9c98ea4bde8645e4a097e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/CustomSchemeHandler.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d51964dec4a70090f67ddaf349f58506", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c2fed47319be00d4dd2f9ab9efe891ae", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/FlutterWebViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f3dc9dcbc2d97a9ac908ac34d83f5de2", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebView.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987c4a0f14c1f15f71885ca2c9c71a63f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9836e24208ffff0ee4edee1dd838039a8f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/InAppWebViewSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f258e388adc76a80e5a810425cec50e7", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9898e5a27a86b58f7f1433d14d64ccf37b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebViewChannelDelegateMethods.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cc21b705a1d4cf3423664f61f2c63e95", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ca04b657f59dc9d497b055310a689e2e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageChannelChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ab7689e3920217e1901e55cd7515285f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListener.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9857523ac1e7bd64da85d9900b3daf2b59", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/InAppWebView/WebMessage/WebMessageListenerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988748a2c0a6fc8465adf3771a4eb1ef8f", "name": "WebMessage", "path": "WebMessage", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98586d2f033333434287685776b735178b", "name": "InAppWebView", "path": "InAppWebView", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a54500776ba878821120c2d05acb7670", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/CallAsyncJavaScriptBelowIOS14WrapperJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98488372ccd18890c1296e15200abe4f24", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/ConsoleLogJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982c691a4a0f389ae3e1b04851902919f9", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/EnableViewportScaleJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9830fd1326b3e52038a9b8adf13e3a833f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindElementsAtPointJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e11a066232913e921d08d38e2a6e4102", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/FindTextHighlightJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f7d3ebb3982107042ca467988218e9ab", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptAjaxRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986da19537df6a6adee9cec00e9c3411d1", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/InterceptFetchRequestJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb8b084942f7b86438a6788c9730dd70", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/JavaScriptBridgeJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989543f923107f85639bdc25e6db5c8456", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/LastTouchedAnchorOrImageJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986a83687c266d57a091be68649ae29b82", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnLoadResourceJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dea930f29d68c0cd002735b6beaff00d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowBlurEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9801bde62761f702dd4a9dc4f825d60bb6", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OnWindowFocusEventJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829d609822e96fac8901d2234885478a7", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/OriginalViewPortMetaTagContentJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98686c3b3af5d64437f52829f39a29a36a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PluginScriptsUtil.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987fadb923a25bb5a65a959db117ce7315", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PrintJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e79974fbcc26c9f94887773be36be5db", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/PromisePolyfillJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98d8bc9c3e4e0595c4174ba18dc8aaba80", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/SupportZoomJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98797b487ba7cc1d3d8ce970b8cf672523", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageChannelJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98378b602ff3eef9ec78d3d0c9be2678d3", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WebMessageListenerJS.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a55881f383c2e60b4e9052d77a06ab0d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PluginScriptsJS/WindowIdJS.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bd75de1f7952e2005e29c82a6e877343", "name": "PluginScriptsJS", "path": "PluginScriptsJS", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987639a4140e3ac79130a8222f2277b349", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/CustomUIPrintPageRenderer.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ba146af7485af65772a275a225e199eb", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintAttributes.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982119fc0d6aa0621ab3e420e4550cb39f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987aab9a10fa52a8000b28c1fe6cb1f398", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98db7c52f65a425a0791bc22a400af226b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c503b0f1b7c29e3ac9bcc510c2e913a9", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9877eb04f787f024aba4c287d4c0c04608", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PrintJob/PrintJobSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986f079fd9935ba8b56281d1765ca8ebac", "name": "PrintJob", "path": "PrintJob", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98751a694e84330575f8fd1631858e89f3", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cabe8e63bcae4813b0056b43a237ecfc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshControl.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a15525c1f7b52aa40a535da2f65099d5", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989851a724e221663a9a66f0acae1f59a5", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/PullToRefresh/PullToRefreshSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9895d36c05865c12247a2cebd2613868f9", "name": "PullToRefresh", "path": "PullToRefresh", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9871655b9e35d73506ca8257cadd284fdc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/ChromeSafariBrowserManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983cd3acd134147f0246ce261f3f7450e2", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/CustomUIActivity.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9875881687a5d376fb5f1c7137793e9f48", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariBrowserSettings.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987ae060ea9a5245aa04949f39fc2777aa", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e11dd40365c58606bafa8315235cc3bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/SafariViewController/SafariViewControllerChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98c863eb0c27abe5ca3880aa7c73294014", "name": "SafariViewController", "path": "SafariViewController", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9847cb72a869a1c73ceaf834ff374d6027", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ActivityButton.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981f63c33357150465a113a4a45495364a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/BaseCallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98be13e81379187bc9c591e704395127a8", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CallbackResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98df72c77634e0c75efe85bef5d8e3df38", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGRect.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981986e889736b21c3b3616805fdcb7348", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CGSize.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986b6045b7d3b427a07c8e2a1f1ca41aba", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983bc5b7766d052088195076bf9addb4fe", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b80d3376c630e49d0a489ea26e25b35c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ClientCertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98377ab8202b37ac38b634e29713b324f6", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CreateWindowAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980a1e0f1377fe9a5c191c347fa7adbbd7", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/CustomSchemeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9834d8e3d526848509c47da2e855034bc2", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Disposable.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981385d735a43178328181338a2eb23252", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/DownloadStartRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cccea270e81150ba6d7998308e69c58b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodCallDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98de4a49e58ed6e3d5a06ef5fed8b2d980", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/FlutterMethodChannel.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983954a5ef591c2822e78ef70e0296c47f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HitTestResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988a182a1c780802991886b875883d21c4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ffa38bd57907682967d4878d7540b296", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/HttpAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c6d2bd2efb6c5d9ad9cd55bfeb767f19", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/InAppBrowserMenuItem.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cd89cd8df9e860cd69013bd85e92cac1", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsAlertResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819e5cd5c7a5ca05bf915f490cfe5cc67", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsConfirmResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cab5da6ff10ae888e0d3308157eccc19", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/JsPromptResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9800f0bd8703c59826fb300cc7441d1aaa", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/MethodChannelResult.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884b3492e5312cb9a44d3e998ccdae96f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/NSAttributedString.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e980cba43d7aa2fd437911ca511a1d3694d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98b01c0551a88b258613395cd4c1e396bd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PermissionResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e17bee98a6113c6a0108d8276f3b3f31", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/PluginScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98193c9973501ccad6470294bceb16356a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SecCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985cd8c12aade62a0d44669239dfb33627", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustAuthResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f32204c02a31780701d3954686316aa0", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/ServerTrustChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e987714489307e9abde5af86893e1a10239", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/Size2D.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983a4ec24ac89cb7b2b22cc621db823d82", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslCertificate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98cf997a9b9d615d123295f7161881dc2b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/SslError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98343ba17a909fa71bebb57080a969e682", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/StringOrInt.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982ad3177dd36b116d99b26934a254c776", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIColor.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988815c24b80659bdd27854e30fc10a38a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEdgeInsets.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98c9499d07230e2800332b52820b739610", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIEventAttribution.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98029a0ef765d1a06f659a631411bfbfc0", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIFindSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e2b8c8c85d2f5a5d8c803440e4288242", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UIImage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dbb09125144e6cc09562dc21bd01ae5c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLAuthenticationChallenge.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ad4caefef1773b9cede69c21bde09d46", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLCredential.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9891e20f7057617e5bafc7598c4e456572", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLProtectionSpace.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802dc15e00ba6fc5260719c9933d16bef", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e093cebcd6f548e5eda9b35ac4133102", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/URLResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982738ec415d73c51bf3c8c52eeff2b116", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/UserScript.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983787e8ee42c99ad1a2f2aa98070c6ece", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessage.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98398b734459e74724db04dda2cbf5ff00", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebMessagePort.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fdc43b3c4862e8cb378f7aa0f706f1f4", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceError.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9870a1b55cc8fb0c32fce7b5dfe5bbc33b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceRequest.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9884d41d7c5db1941296bc19d83d648293", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebResourceResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988873ea185ce2074e7ae64a636b0de898", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WebViewTransport.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a6b4e637084d32b738d4e4df7814735f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKContentWorld.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eec3c5a4ec3f7c3cff9e4294c81ef594", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKFrameInfo.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a89684b3fbdabd2cc382b423377b0f58", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationAction.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988e654cdf1c3a4788ff92a0af122c71ca", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKNavigationResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9836d87a22579593a0f241652cbf0ced4b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKSecurityOrigin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988babe22f25b62563df248e41eeb592cd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKUserContentController.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e989bc93e9a7c8fa74948c875f4a015d933", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/Types/WKWindowFeatures.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983fca9034d10a70a8246f00a6c1c5dd17", "name": "Types", "path": "Types", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9829375158f7bbe4621554abdb373beeaf", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/UIApplication/VisibleViewController.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985b5b3e2b9cc3c63e6b0131df166322f5", "name": "UIApplication", "path": "UIApplication", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e981706abfa4046b7ed220484ec0aba550a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSession.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9802ffb4066dea9246e592e259071a9cd8", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionChannelDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9868b16d4f140f994ccf9e31e6a0174dcd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f5a64c3b11c481f71fe7cfccbcaa6dcd", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Classes/WebAuthenticationSession/WebAuthenticationSessionSettings.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983d25fbc4e2eeb2618519136c003c0c40", "name": "WebAuthenticationSession", "path": "WebAuthenticationSession", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98116e943e5215a7e7b221ff7fbc6e345d", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9886b7f2c5413fe8708ff3ca2b6931fb54", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988c37ceb8058e729dff6c00e318d4bde5", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "file.storyboard", "guid": "bfdfe7dc352907fc980b868725387e98a73c1ee5fe469e676f7657cbdc7d21ce", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/Storyboards/WebView.storyboard", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b5494fe183abd61f9c4b65d898b8dc9a", "name": "Storyboards", "path": "Storyboards", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982b1b9cf969af3d278fd592d4651d223c", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eccc4b74d4b07442c77d2ae164d42bbe", "name": "flutter_inappwebview_ios", "path": "flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9844c699bc56f1d6dc80f2a4bb91267340", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9813f3a3a8af1afbcf5f410c07b8c967d4", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987363f97d48de9b935a604edd21838614", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982cf6dc2ab5c6baaa3c68531e62eec689", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd9564af31d5df8faaf2d1a2e07fd855", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b7f235a07ddc35220c7987ef7b1577bf", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854f6e370ad4bd5edcea77264bed20dcb", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984f86d05e4e55c93d4505b62cad896165", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982969b7ff3f0a737648622f912a724320", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9811c8c1147962160bd038d9bb5d0a0a2c", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cc44b7375bb62d4cbae166c6417c983", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987682420cf12fc7e0068fc7b77f347be8", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/ios/flutter_inappwebview_ios.podspec", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98d2c2e210603e0ee0279456927e432c36", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/flutter_inappwebview_ios-1.1.2/LICENSE", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9853b893b9d31e418b1d5ce9742dc2d844", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e982f6f89eec997f901a585f71b6221d572", "path": "flutter_inappwebview_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985e8cdcfc096bda516d22ca7ee4f50cf9", "path": "flutter_inappwebview_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e988ad00b4880bd4e40a261bf55774c9fb8", "path": "flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984dc7ded8d7d2e55adfeac28e9e0d7c5f", "path": "flutter_inappwebview_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ede51750570a408106537ed55980ff10", "path": "flutter_inappwebview_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e982a2d73415e235cf88d7b7dcd3f7def6d", "path": "flutter_inappwebview_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98edf11b808c493b5d49456f6c496ce534", "path": "flutter_inappwebview_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9884061cf88c0d85e00fec3ea6232c90e4", "path": "ResourceBundle-flutter_inappwebview_ios_privacy-flutter_inappwebview_ios-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ac40e8bbac9665ebcc12834f4ea13be6", "name": "Support Files", "path": "../../../../Pods/Target Support Files/flutter_inappwebview_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987ee0fb21dc7f182becb12a06e53e6d54", "name": "flutter_inappwebview_ios", "path": "../.symlinks/plugins/flutter_inappwebview_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e988dc8880d03a788479e7c3913b6c8a2ea", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f5e433c8a8f37483b84600a691658651", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac99fa73df115f147bb0062aedb4c16d", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986ce29d66947fbd84897fc0cde184c5ab", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989dd90e6a00ced2f0a02c9ff4c041a9ed", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f0b8ce0b7c86231375448cc6e5b67092", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9828b21e30ee7942293a90998f40faacf0", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988d0032a85c1156a3ba72ea86e6e266d2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98611591da936f7c65f3c5c9b8b5c5812c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983a2b6ce98fa58b42f607fc7a4a9aca40", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892c75f58eaf878859eb36aad207069a8", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985a943824a7158f24912d94042304fc0b", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a72a119150d90a9a9d2b98c2cf4f1677", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9851c31d6d58e5c4882b0b5bc2e48ce287", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988f3febba94a814e54ee919769a0c192b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources/path_provider_foundation/PathProviderPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e989aadcfe26790048627c854389ff60e1c", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dab4959c46b9b6c371e531e384458ec2", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a52c2d77ce9483feca943dc66c791dd8", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98eb3431911445788ddfe5ff1cad72974c", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980fd11565150031d2f9b2e3e1ae8cdb99", "name": "path_provider_foundation", "path": "path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9891f9cb4a70ea63abb4c4c2aa72474a15", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986cac60063a1925a1192d79bc2167548c", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fdd37f236ed9d69fb6fc53f85bf09001", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9894d2e5f3aad77cc12a002f6ddbe23874", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d627525bcb8ddadd3f1f61d4c22af174", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981c9c1475185697295bdc8092e90673d2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98132f17a3190a806cf81219ebc76a9591", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98404952868f0078f0c5a7377552bcceed", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982ce6aaedef96b3caf8c502a81ef839f2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a21243277d05c20776fedd5d29326250", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98962432547de89106b398f3968e1a86f9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98389ea36586456a4de92ce7ff4ccaabd0", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b44bd2855f6e9f608fceada25f26a21", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98c3a6d6988b9801d6164e7d71144ac3eb", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9874e222adefc4ad35110d5d2d1ef0adce", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/path_provider_foundation-2.4.1/darwin/path_provider_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d737c9acd80b2b20fb2e55e5f3b968b8", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e980e064e8a8b22dd083a504cc07c6f4deb", "path": "path_provider_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d78a45dc44536299cb99459e9287dbe8", "path": "path_provider_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98afd7fb78ed8506d174eac548871d1e6a", "path": "path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980023f454641e5ec9cff0071ea727415b", "path": "path_provider_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98337259e3d34ab1883cf8a3dc50bfe3ce", "path": "path_provider_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98e5bceb79484a14c2eb2ee84ee07bfed5", "path": "path_provider_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9872e1541cb1dc3930f9d55a2f5ac77f7b", "path": "path_provider_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98e482015dbad1dff6a5de110b82230d27", "path": "ResourceBundle-path_provider_foundation_privacy-path_provider_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98aa1b05a27151d7aaf059473353a8a7ad", "name": "Support Files", "path": "../../../../Pods/Target Support Files/path_provider_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983877f86cc0e686411216fbba09f641ab", "name": "path_provider_foundation", "path": "../.symlinks/plugins/path_provider_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984844c5759926567230e2f23fdb9ea38f", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerEnums.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864cad3d17cfc6aa58cf0b79e5df79541", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fae8be0d4986a2d92573c02760c0a982", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionHandlerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d1f6abaa13135887efe25900c1a34304", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981501413b91381ce3f4fd73de3293641e", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/PermissionManager.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d59d3ccda6dab576780ef3122cc46eac", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b1e1780ad8c3428628054e32ecc13d9e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AppTrackingTransparencyPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98d4637978616e8741eaf6d63baaca9ef0", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98cdc170a65e25d8d08e96649aaf25487d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AssistantPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987e7119e8be56fbeaeca0d839e0aa764f", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9884a8f8366bf0f9283fd371735cc6c47c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/AudioVideoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858628d063027fc071135e8a7883c8904", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98acdc95958e2ee048de98448d8f05207b", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BackgroundRefreshStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98183b048c5be44367efc8b72b5e58aa7d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9834abf65ec5937fbec2334ebf3c1b7cff", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/BluetoothPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e986f5aed7ed116a53bb9b4c94dbc20d39c", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985d8da055c8ffdd178a9631ed55c172f9", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/ContactPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980c8f129ddd527d942c09479e36bd3865", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868cf8d30f76839c5d25c79d73b2a5db5", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/CriticalAlertsPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b73934618d6fe98a2ce3fc648e43ffcf", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868f44a7b86bfa4174fd9e0ccf917749d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/EventPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984660234cb08a46dd5d017d6c4d54831d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98da3701566ebd96afc2a42b47775f591e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/LocationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98ab65da116fc32c7a0e75c6e97ff9b50e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98a44aeac2fe4afb1e1c282477a61d03df", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/MediaLibraryPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dde3dc35ac19db309f3dc1146ba0e5d2", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e981fb8ed8cc0622f550a0acc598cff3e90", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/NotificationPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f631c9b1445286abcbe87b997263d01e", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cdff0e7390e633302bf7bbf066006864", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98eee53ca66d929b944237d89e1fec22fb", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhonePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e51b09da45ddb6a45c034018ca108592", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b3e316f97dffbff523bf2bd2bf4b972a", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/PhotoPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98322a84800c5ee038e7b0a6260c09c425", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987d1aae3e9473931a2103e04f67f684fa", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SensorPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e813137b50ea6c4e7c680551bc90c9de", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98859a7902e5a8c7669ea849407ab6d557", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/SpeechPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98695293efc1477d3b906aa90dce772811", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98f60879376c15ed401ee147c15a0615bc", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/StoragePermissionStrategy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e981ac100d2234f39ae6d63bb86c16e89c3", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988409f490de932fbe2a0369fe52880f4d", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/strategies/UnknownPermissionStrategy.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e986a242985c1729f4aa34ab3db03f35d4a", "name": "strategies", "path": "strategies", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98f53c75e42b58140e55abdb0e4ce81d93", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b374ba16ddf28584f4e01ee2df99566", "path": "../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Classes/util/Codec.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98ea3415baf030fa0070afeb94d8ca2fcb", "name": "util", "path": "util", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98698a02aa31fb5996d71f08f7e2da6982", "name": "Classes", "path": "Classes", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e847489c1e1d1e9aaf2b6d822afb1e30", "path": "../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e4097ec577567f86beabeb971606caa1", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d0f00610b7b284e3154c734197119575", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b36536cc5c0aa016ea4b0031be1e9e30", "name": "permission_handler_apple", "path": "permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6670e09a9494926c873cbf263d77574", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cae5708f7907a07b776bf426b4ec5ae7", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985177900fee7d7168af14b5af0a5b496b", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98384a3c4c7e2c4a195a0c16391da30b5e", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989305a12dec04f7139b437295d2fcba97", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c94f56070cd982867884e1b4118449d5", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98db26b90d8479f239bd972f6193e8d08f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bd7f0f599aadddc2deeb5a4f58eb7e1d", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ee55d166ccaa5ec4572aaea39f396d17", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d125c81cec73805e435e1d91eab2d136", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9800d07e125a06d026330ac4e0b544b61b", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9888eef6ffa4db588ef759505d49e445dc", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e987fc3e809431a5eec614996443c8043a9", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/permission_handler_apple-9.4.7/ios/permission_handler_apple.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98db9e01546d4f00a10f23a0f337050c54", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98c0f5dd07034a6ce6315a8cc37d727aa9", "path": "permission_handler_apple.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9830868cddd4126341224705d85095ddf9", "path": "permission_handler_apple-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9889de7c961629a48caa38bb28e34c6ab1", "path": "permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984937b54fd534040ac5b7778631675590", "path": "permission_handler_apple-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fa1b6cea00f64700432d0650bfed2f87", "path": "permission_handler_apple-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9856804c33e2fecb400b8ef3376b52514f", "path": "permission_handler_apple.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b93ac8ae39a05c396f03c7eee9831993", "path": "permission_handler_apple.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e987d5c69836dcf297e4bdbe21d4e9c14e0", "path": "ResourceBundle-permission_handler_apple_privacy-permission_handler_apple-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9864675d80b06ca0164e4fcffdbb8a9973", "name": "Support Files", "path": "../../../../Pods/Target Support Files/permission_handler_apple", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988fb3cbe70f6c0bd524c93a0152bd36eb", "name": "permission_handler_apple", "path": "../.symlinks/plugins/permission_handler_apple/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9881cd4ec9e4aaac010137a109577c1127", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d50a630c3bf4fb146973cbd625f3fe05", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989bcc785ee2579bcd8a5d39ac34990af6", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9854963d3893c305a48ee4d594df4c6189", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98875a23ae900dfc0259060569caf38dbf", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981819a3621b1b9615137fc42821b42049", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a6616edadf12bae3e9457c0629a44e40", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ddc6237f4f648987bd32bef307254ee", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987a9212c24edd549011d026cf22063b23", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98efc50d9b60bd7d9e7dccd885ff9434af", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980477286b76fb6d4319d6d692c2981053", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982e2ac88c1e8b1c508754aab5f1a1fcbc", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f40162c49409d12a5097208089931613", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e985c4bbce564164cd03cf62a9201b207e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98bfb5efe6a51926ae2c2df16c700339a5", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources/shared_preferences_foundation/SharedPreferencesPlugin.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9815444bf05fbd3df23dde05dade737fdc", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b75c219954fddeebe98934fe20bd8c23", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a87400bc7b076081f3bfbebf6b43140e", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985e8606fd12b7d9e1804698b7a6a72e62", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98950cf7d448c1d741cc39a9210784e4eb", "name": "shared_preferences_foundation", "path": "shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984b3c353c2434949f439a91926088872f", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6fef22944882599d9200865340cc3d1", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983d690ccae4e9db46dfa4c03fb47d0f35", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9897626e501b5d824ae0cad4133ff53e6e", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9880dad316441df3e835bef8b49e78d6c4", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982d8fff40e57de9090171f067c3bae967", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983fd18ac53a59e0e8fdb8ca5bc405f508", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984936a293043f59c5ec3e590b59daafae", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b417234ccbbe65ae4f8ae1b513f5a5db", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b75c95ac49a1a60e7d4c8238ad6f6f26", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cdc57192b13f3fc9f83c846a2fb14d23", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98820a4568b7dfee6b9b9bced4caa0c8ec", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98245f186ec1920bd2298765785add5392", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98cf98a8735f4645f3c31032328bd9d82a", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e985ea16405bc7243c295fc6ce92655a01c", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/shared_preferences_foundation-2.5.4/darwin/shared_preferences_foundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e984995efd2baa0745512e3087043ce2c3a", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98bb3cf6bf227377274c8991c3fb6999f7", "path": "ResourceBundle-shared_preferences_foundation_privacy-shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e988111659160f20d7ff8cf294511186611", "path": "shared_preferences_foundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98773489f6c24d9c8b8a268a74ef2b3aa6", "path": "shared_preferences_foundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98580109f5a05276fc6269f9fcadc2806f", "path": "shared_preferences_foundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9850f6d6982afa6dfc35de373c89c9f501", "path": "shared_preferences_foundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e984c75460e55f4c7f8aeabc4196b1c9a43", "path": "shared_preferences_foundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e989d60d32208e877f6179616e979ed7cf7", "path": "shared_preferences_foundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98abf9a59bf09ff538119f0851580d7a87", "path": "shared_preferences_foundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985bfa4134ff628da988246815a6131004", "name": "Support Files", "path": "../../../../Pods/Target Support Files/shared_preferences_foundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980d8a447b034a53e6ea9b827d97851faf", "name": "shared_preferences_foundation", "path": "../.symlinks/plugins/shared_preferences_foundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c168e210047c21f3b2b991f033dfe527", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d8e2b05d4bada53dc51dc19410b28347", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e4354a4c175cf63706183fa195fd162d", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9884a6275a684fbc00179eea68d57a7b89", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9806357beea58c3ae0f118aaf3ebbe1957", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988da3496b93ee1d059f513684ed58978b", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ac3b9cad5139dfac63fad83aef9804de", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f8a58b25d77faee16c9f93275f54efed", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983bd9ac9aadef72b035ca9e6e57cd0109", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a3052ebec2337eeb07f22db33ea32b6e", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98397ea979d1707f3ca435909aa207ac88", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98184aad0644c1ce5d8c7678a8d1adcfe3", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9892ed98854a33302fb5c8e5b00f88fe42", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98dbbadd9505c9507e2200bb1b611df355", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9849e2bdbd001edd5578b42bad05eb0c0b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteCursor.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bd6f9e876f2c7b2be4055af8250fc3d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d2311b9561e9a981753ccd94f6e95f38", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98df0ace16b9d02bd6167fde65ceea9557", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d4fc6378befc1cb9192eb137da7943c6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseAdditions.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9858a95fdb885a3c134eb8b08c1d246267", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d263366efb283b8be730fd02f4276bdc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDatabaseQueue.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e9df0fc6f0aee9399521dd8fa942bb48", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinDB.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9852809701e7775684735b3c6a1f8286dc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b4989995ae751319e6863ba01fcc54c1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9868f0a24e28b1f98082b7a0cef138787a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDarwinResultSet.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b1cff7b0d931b36f6b1beb874587a75a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e980150f69af57b52f2e79c10539b936bdc", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteDatabase.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98143ed2a80084476f93458be5088091b1", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteImport.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98cfd45e8e2fc1c68f3119ab15c98a7f4e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9823816c84e127922d402a13f1b0cf237a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqfliteOperation.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98edf7a37b3bc3d2924b02cbfb68168ebd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98c561793e50c44037675dd80f5876e292", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/SqflitePlugin.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c9f0e9a1a22706738aea164659b92c1c", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqfliteImportPublic.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98874f157837efd7276d833cdcc202be51", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources/sqflite_darwin/include/sqflite_darwin/SqflitePluginPublic.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9878125ba169ad20bc963dadaaffb62887", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983b5b89d03a990d44e374a4f95919b3a8", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9ea8d06d04b0b173386ebd4798833f7", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98269ef312262ebb2cb7e60e1d6134244b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d9910ea31b13d5c3d567336d0dbc62c9", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9819d6d73a6bee38a3f92393c7b3c99550", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c6039da0ff5f76709000d836f9ea842e", "name": "sqflite_darwin", "path": "sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986be687f0331ffb42c24fa21f160ad6e2", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c00b8262bcda71824e8e4e0947a31370", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983e1ef69270877f0c3ce8dc531d1b1d03", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d69704e585228e6fb1a05639ca1afecb", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b1cb672a95323d7cfae7de20febfed09", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b853876fb5d444753a901e14d47ec3ff", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb893413defd7f1037025ae79d9ca9fd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1bb8ba0d12d5b8fb3deaca7c516114e", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98599a67c9c9d019aac4774c8825ff9f81", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f37010a9111108a00b65caeca88cba3b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9895555f379520ed4111e8f2f5c2b9d88a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98933b14a697f43019ad35f8717fc79a14", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d943d79494614f9480fd17344e965c3", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98ed31fc217cfeb4f615196432db7171ec", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "net.daringfireball.markdown", "guid": "bfdfe7dc352907fc980b868725387e98d6b02a41e901aea2c17fb8b560d7d594", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/README.md", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9848d6662bf749b82235c3c0f86381bef2", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/sqflite_darwin-2.4.2/darwin/sqflite_darwin.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98bfa323ff6dc6e9b56ef38ae07d9817f4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9824fdaa89582c248ea74c4920bbb2098c", "path": "ResourceBundle-sqflite_darwin_privacy-sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98473ff30453d2ef1166f7e9ba1213e102", "path": "sqflite_darwin.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9836521190708c92289f6efb23e43dd79b", "path": "sqflite_darwin-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98876dbab0b6a0422909f89f146c5b3846", "path": "sqflite_darwin-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987876893be9320d964d5a8df3133141ac", "path": "sqflite_darwin-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e985f123aa80acb31ec8cdc79c20c56b2e3", "path": "sqflite_darwin-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bee77ae14d2c67fdf55814c5bea1bbf9", "path": "sqflite_darwin.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98b1cfa07a2dcb2283ec4767925eff924b", "path": "sqflite_darwin.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98acc30e7ae09cfbb5f5e29ade7630dc0b", "name": "Support Files", "path": "../../../../Pods/Target Support Files/sqflite_darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c29b2d4153d005f46afbdfc8f8cb7ee5", "name": "sqflite_darwin", "path": "../.symlinks/plugins/sqflite_darwin/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98e93acdacb463106e020a28435050b09a", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9834b46fede86dec74d4c2684b9971c96b", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d523dee59cc38ee1738961ba136705e1", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98288c234bc342c4c93ab4229d84dcc649", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9868afec78d0b2eac54c4cf54a4639a46d", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a75be0c92895b62f58b11117e114d6fe", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9804446af9b30ccf5422d52610618e091d", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98017d43135f5ed91947b13873b1448779", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4a172354724d3072769c3418fc8e52d", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988585e9bdff53b41ee64462ca4662f070", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f2a0255fd4bc4accd57f8383c4c9413e", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98cd0acfa20d14c91dde230dd9d92f3630", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984ceade8a94711d41037ceb67000e00f6", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f67d40e0116621bb61e3f6872861c0f2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/Launcher.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98a9c454f162235b868df26975c9ff7606", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/messages.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984e0afe87f3c5eaf9fb26a896be6ed0af", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLauncherPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986ffde1fef5dfe71724276290c520b639", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources/url_launcher_ios/URLLaunchSession.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d32d41ac398b86c6815de905e76d7e53", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982df2e3f1006cf665600439e21f17b61b", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9871ecb22075eba7b27bb1cebe0be873d9", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e988e92fbf5ba2f6254f96fabc4196d4e59", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98b6059352728917bed05790557511c668", "name": "url_launcher_ios", "path": "url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981ce705f1ffa92575d2a0466d1e999cbd", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864d697521a1bdaabba36bcd6f84da20e", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98732fcd683cf18c9ffaf19a5db18dbc1f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986e1d55bc0c864f5a21b2279e6c9afb12", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984c412c596a65dfc5494530f36cbfbe80", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98c4ea98a82698adf6547bd91f6d003c8a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d6ddee7fe9528c9c77fc324d9bcc0291", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98630a50f98c9ca9c540069423b1560da9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98299f36d1644d8153f5975c4dfeb1dd96", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980698b8cc5fe62c45014a0f571a55f482", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980a6477e267623aff3337b1c1b506b7cd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980ea8ad33af71dfea1982ea4d82c98a3b", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980beb4613d042daca0c6bc1f63b51591e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e982384ee4f0a078188975cff8ebc1ffee0", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e982fffda2cdcc5c2fcc2ac5dd9a4227a70", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/url_launcher_ios-6.3.3/ios/url_launcher_ios.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e988765c410a7bfe16d5b6d2e60aac50fb4", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98f088356ecfa8bc56a47d72d0112981b9", "path": "ResourceBundle-url_launcher_ios_privacy-url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9860830355e247590046875f6dc91b7366", "path": "url_launcher_ios.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e987c8e981149e2cd3dba5d710ccb66d342", "path": "url_launcher_ios-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98b97c03012b7b10696976d0898510d986", "path": "url_launcher_ios-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b602b3d15ec404a600bb06ba6f43279f", "path": "url_launcher_ios-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98479fe6e29bc1fa55869314accece5bbe", "path": "url_launcher_ios-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98fc4680d86f28592b61850115ddc093da", "path": "url_launcher_ios.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f62239a36256366cdf5494ec5adaf518", "path": "url_launcher_ios.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98f5dec31041ce1ce6d7ce182c7df34db4", "name": "Support Files", "path": "../../../../Pods/Target Support Files/url_launcher_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98bb259330eab2ff3f5fc08267560686be", "name": "url_launcher_ios", "path": "../.symlinks/plugins/url_launcher_ios/ios", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e9812bef98fd47af89fdc24b3e0e46bf289", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98b09fbc61997bcb39e4b20bdb2687cc8f", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fc5ca328b50e6eacf730008f69361158", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ea23d01b6f23050743aaf443b584e1e0", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9846aef96cb9bb413197c444b8ed2b78f3", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f85e87dcac095688e9af41b444c1bb49", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9898b11e4e7adba3476d6adac90124fa72", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e7532500fc9ed6af977368460a404227", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983eb70db05b00707cb1ddf3fd1d74e08b", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e012bade1bbe87dcee5db10f766ab59f", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98799f94af81995b918a546511fbdbb725", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98928fb155c6f6c65ce7f3f769e475d920", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9866eeb45f758221b8f092d7e0feb0198f", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e987d6d9c983edcf37dbb4baf155b4a6022", "name": "..", "path": ".pub-cache", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e982101bbf6d7b6001e89206542c098d1d2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/AVAssetTrackUtils.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98516c22c111416a43a49c58a7b9002875", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPAVFactory.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e985cc33e3d8d9a9fa2a41ea78e019ba6e8", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPFrameUpdater.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ad34a67a0b995c8ccc99611245bc738b", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPTextureBasedVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9853dbe7b3407492ff5d16b1ca250608dd", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayer.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e9800a3f4b48bd08ae5eb54d2582060eabe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/FVPVideoPlayerPlugin.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98ca1a10fca7e6d8a90c555cbec26abbfe", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/messages.g.m", "sourceTree": "<group>", "type": "file"}, {"children": [{"children": [{"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98fdfc1d8a442ac74910127e728ed08114", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/AVAssetTrackUtils.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e0fcbe48dffc422cb64d6226a259483a", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPAVFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9851c793f5344ba9b01b4033ebe20876f6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPDisplayLink.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b5d26af4d4282e39b66a28b9e79a09ed", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPFrameUpdater.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9887e94459fde73ade81a429b22aa5ddb5", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoView.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e982ec0f0897c1a5c8939bb55536a55e0f7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPNativeVideoViewFactory.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9859d88f100a41e5eab54b6c2507d26932", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e987307cef802a6a0f0913d69f98414429d", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPTextureBasedVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9866f0511f4754ed029eaaafcbd9f29456", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a190932066ec6888d3fda36b29bab86", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Internal.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c52b6322c0e64a1370450f60bb700f73", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayer_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98b77ac17e1856603e90b832c7b2b9f5e7", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e989a929861cf9a7f7a4c6db75c37f0cea6", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/FVPVideoPlayerPlugin_Test.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e983a882b6904cd9816af15db2690fc1bbd", "path": "../../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation/include/video_player_avfoundation/messages.g.h", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98d9a0106db6b54da3225674f979b976d2", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a22fef1da4a4bf965a11f8af9516d2c1", "name": "include", "path": "include", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f1230fea363d99d3a75eb4a8b6356324", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e983f122f50a10de641dd8f11ea37fd9d45", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPDisplayLink.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988c01dbae1961d06cb7d4e7fa88755467", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoView.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e988b35c1cd63f97307c00f2e2b0fbe44c6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation/Sources/video_player_avfoundation_ios/FVPNativeVideoViewFactory.m", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98818b5445e0f83176b39f2f919807adcf", "name": "video_player_avfoundation_ios", "path": "video_player_avfoundation_ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98330366f30f6b2e9be47fa6bc1f740fab", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984cfe231a3bbc59846bd8fbf0533edec9", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98f28cf139ae22b9f5510ca04471b7720f", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9860a527b8f4ab1be95c1e1b7da637fe85", "name": "video_player_avfoundation", "path": "video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982581f5ac1c47a0c19a52ec6e5e672b0a", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fa3e717f972eeb8c407941a60bf025fe", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9809d6e856613e0eb333df10f89776f6ec", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982c4c6b0cf4bb7c42b9677c8eb7943b0b", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984da6791ca31cff5710e25db1011ca071", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9841398858c210465072763247c09a7631", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98fecc79c9b3997ca07bdf319aea10af94", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98755bd2325d8d8a28c561b15dac1e9de2", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984053f23854aa6c37f5568040dd84acf9", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980f072c56594084a24f9bb4ad23fc2752", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d163141d3cc6ff6bc88bf40cbbddbee8", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989350b588eff244d30876c00073b9855c", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98219899faba1af2d40658923de8691d35", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e9812c160aee531b35ef6c5be891e144a3e", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/video_player_avfoundation-2.7.1/darwin/video_player_avfoundation.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e982389c7d13267d0e0266e552e25c39f5d", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98555b912294a2402c5d366de2bf2347c5", "path": "ResourceBundle-video_player_avfoundation_privacy-video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e985cee062e9d62e8db81ab6176d28323fe", "path": "video_player_avfoundation.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98df3097025aa54478a15219e54ffa64da", "path": "video_player_avfoundation-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98dfd4890116e10db9da273912c1c0f4d8", "path": "video_player_avfoundation-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9889c5205031754fc75ec8006797f94ad2", "path": "video_player_avfoundation-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9828981ab724ff018298e53e5feedc7c20", "path": "video_player_avfoundation-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98be66f5b89e39e7a49b577231aa29829a", "path": "video_player_avfoundation.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e986dd1a037feb0a53308838fbe4aff6ccb", "path": "video_player_avfoundation.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e983b916d0d9f566ccd880c75e033e973ad", "name": "Support Files", "path": "../../../../Pods/Target Support Files/video_player_avfoundation", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9875a1bca101442821f53ce3b395566836", "name": "video_player_avfoundation", "path": "../.symlinks/plugins/video_player_avfoundation/darwin", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e981a9864ccad41b3eea93a25f8cdece528", "path": "../../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/Resources/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98a71d89cd4e818b7e77772e9b9ae88700", "name": "Resources", "path": "Resources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986d9f372b4c40d1a0ae9f9a7fb84a8390", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d66a235e2fb3c79571a8e78b33c5b4b3", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9808cb56255ba85535fa34ecf241c1cfb9", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9810b78d50b9ef3572ebd66faab6ea7075", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e986b9680b0c182677c779676a16f7f9234", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e982065391b10b7c212489c510bc4c9fff4", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983af097d5d65eb917f2793cc77905515a", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d4f5972f55a4e1dc316afc69d01ad4f0", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984d2b087a00bb831747738dca15b7a24c", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a00e6832c3124d9bea42e665e0172a35", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980cc2bf4ce3dc2c0ae09f17930b3af504", "name": "..", "path": ".", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9846a79e7f207eb9f7d74c9c91d3c92341", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984ac7959d3e183eee8a14c0a90e8791b0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/AuthenticationChallengeResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98245ccaf06f831c7f4068b3da72dd0234", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ErrorProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e95f154c1c347921ff55d95d06decfea", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterAssetManager.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e983aaf36a7347ad5d72f5a63e5ebc6cee6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FlutterViewFactory.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e984303027ca5ae4616796039af30c42093", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/FrameInfoProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dc3316b4ca3862abbff9e2be115bdbb6", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponse.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ed209853db0a4365cf0e745cc4550e2a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/GetTrustResultResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f4393c3e94b6c5dd5bbe14b1b50849e2", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9882fc3f0576567cdab3359153ed317663", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPCookieStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986385126e4b8d0aadf390ea5de92bc11c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/HTTPURLResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e988a4ccabcfbfbffe27008f61a1cc0d638", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationActionProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98135bef56e17ed72b7166f9c10d251154", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9863ef6ba8955f43d0659943767428bd03", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NavigationResponseProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9821402dd626927ebb8ee9621ee0283ce3", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/NSObjectProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f75e81475e53ff24ee312da0c8b891e9", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/PreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ec3f550376bced9707c689f68db14c01", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ProxyAPIRegistrar.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98af42a73ece4a6f24d50f0ed045ad14eb", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageHandlerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982213b1b076ab416c965cee7cf1d99686", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScriptMessageProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98dcaf6a87e0c7cff741560915d6b5df2f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e01070d3a72f4aadd9c8f0a0a66fdc98", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/ScrollViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98eb76cdcc1f309f5b5fb9777cea8d0834", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecCertificateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f8146a0c77774f2627308c94cc769a8e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecTrustProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9818b412eb722b4ef4bd0d7fdd335e940e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecurityOriginProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803361c49d7a3342a28f5d0ebc7e49168", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/SecWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9879a0cc5966fa0d54638f51788982e269", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/StructWrappers.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9846acdc4941a1e0e386e555274065ac9d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIDelegateProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9819433f0f77370f4dd31e8f21e1606e9e", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UIViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e986e355dc5f3ca0580826fd708fde4b4ec", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLAuthenticationChallengeProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982aa7b6eb55f09885f3d2abb6c26e350c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLCredentialProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9820d26b16a5d70653d24ab7677d315bca", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProtectionSpaceProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f568d843e000a39a97ba16e1dbc2012c", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fd6e7d80003707ae789b7bacb9f55809", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/URLRequestProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e982a13c2a349535f1014ba4553edab94d0", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserContentControllerProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f383ff4dbe535e5de25a75f3c904cc83", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/UserScriptProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9858afe87177b91decb4667dd1423e01c4", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebKitLibrary.g.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fe9b5cf701a55e4e522b749e75ea030f", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebpagePreferencesProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9822e8e3bc9ea6d1e1ca02d2bba95ecd36", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebsiteDataStoreProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98ed53979a4f36581126dc43ef203edc3a", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewConfigurationProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e9803283781717421a5d27bb7dce0e9ac9d", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterPlugin.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98fb475591857ac83474d794c8b856a4de", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewFlutterWKWebViewExternalAPI.swift", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98f415c46adb8232a62eb8e300b9580d59", "path": "../../../../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources/webview_flutter_wkwebview/WebViewProxyAPIDelegate.swift", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9820edb95e7230d705a77ac7d1ec269b31", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98681d68946f837f5e6d1cf63a6990b54d", "name": "Sources", "path": "Sources", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9864b9a9a4a2d355ef596c5591bfa83562", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98201d309fd58b387964ab26d6d7c3088d", "name": "darwin", "path": "darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98ca712642dcbf81bd737db837ec612977", "name": "webview_flutter_wkwebview", "path": "webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98991b344d5dcac4f4a404da7f81424cad", "name": "plugins", "path": "plugins", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98060df7a2543fe8e8795b32316af93476", "name": ".symlinks", "path": ".symlinks", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983ba9fb0ca55649f6e711a5407290048d", "name": "ios", "path": "ios", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98721e57dfe7107d268c5be77949280794", "name": "chilat2_mall_app", "path": "chilat2_mall_app", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e980609d081f9b0cc6298ddb07ff80a6030", "name": "workspace", "path": "workspace", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e989addfe63f1cb15e5e978903fdde645cc", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e984375e92e529812f68a8a69e29c1dfdc6", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98013f77e763570d105580f753b9781086", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98da52548b4916526d63673e755921ea89", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e985191278356313af57900200f4741ea4a", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e981d1019d537be80b1c6f87dcc6c315cfd", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9803e3340f0c3ae1aa962debc476739496", "name": "..", "path": "..", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98a281c98825e9aed114ee1e7a569d202e", "name": "..", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview/Sources", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98b2e6238600a47741771dd3bad4b7144d", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/LICENSE", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.ruby", "guid": "bfdfe7dc352907fc980b868725387e98aa0be949dc865bd76a6c981010b3e28d", "path": "../../../../../../../.pub-cache/hosted/pub.flutter-io.cn/webview_flutter_wkwebview-3.20.0/darwin/webview_flutter_wkwebview.podspec", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98e0cc52ea9c1367bfd8fdbbd22c75f701", "name": "Pod", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e982234e5e72603cfbc926371c9720cca84", "path": "ResourceBundle-webview_flutter_wkwebview_privacy-webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98cb084a16450993a85c31ce8e637a14db", "path": "webview_flutter_wkwebview.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98d847322d3e2b6e83be4208b2ef42bb18", "path": "webview_flutter_wkwebview-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e983100c11cbe0ab22464d8484b90b1c2c8", "path": "webview_flutter_wkwebview-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98473a8023932fb9b000f0988d88fd2974", "path": "webview_flutter_wkwebview-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e9864834471e4989ba07e4ec3def766a928", "path": "webview_flutter_wkwebview-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e988725d6ae281ebdc12e63fc812be9773f", "path": "webview_flutter_wkwebview.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9894947526e324545b92715452e2a1527b", "path": "webview_flutter_wkwebview.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9854b2ac4f19d44b3e88b2ce8c0257a2bf", "name": "Support Files", "path": "../../../../Pods/Target Support Files/webview_flutter_wkwebview", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98299b3eacab90f3714e97b96e9a6b26b0", "name": "webview_flutter_wkwebview", "path": "../.symlinks/plugins/webview_flutter_wkwebview/darwin", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98d7799401c0b9158aee24aea3e11cc3a5", "name": "Development Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "wrapper.framework", "guid": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "path": "Platforms/iPhoneOS.platform/Developer/SDKs/iPhoneOS18.0.sdk/System/Library/Frameworks/Foundation.framework", "sourceTree": "DEVELOPER_DIR", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98942867ef9a9b82a20a4a7fc87d922201", "name": "iOS", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e9862d9c32904d63fdaba0f2aec66ff1562", "name": "Frameworks", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.swift", "guid": "bfdfe7dc352907fc980b868725387e98e8dc19b238382d08895d8e4b46c3a2b5", "path": "Sources/OrderedSet.swift", "sourceTree": "<group>", "type": "file"}, {"children": [{"fileType": "text.xml", "guid": "bfdfe7dc352907fc980b868725387e98c7ae475fd0c8f46886dcc83917017f4a", "path": "Framework/PrivacyInfo.xcprivacy", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e987dd43a185e7b58db6c4d26f6d8a0d591", "name": "Resources", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e9824c6cf38e2cef870f1dd5f18b705cd13", "path": "OrderedSet.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98b6762e9f97b4365983a466ff18bad6f5", "path": "OrderedSet-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98fac68a6300d25ba608f4c766361e5401", "path": "OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98bed80a0806d677323da295c1dc61fbb8", "path": "OrderedSet-prefix.pch", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98c278ba6b496f7b69e08e7ffb46f36970", "path": "OrderedSet-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9848a94da1254874a46613396ffae50f2c", "path": "OrderedSet.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98bf9d47c52138ff5f4904bcddd6f04d79", "path": "OrderedSet.release.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98db345e6b1c965fd5fa740dfa2daa79bb", "path": "ResourceBundle-OrderedSet_privacy-OrderedSet-Info.plist", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e985421577ac6913d41b13dd1c8a065b93a", "name": "Support Files", "path": "../Target Support Files/OrderedSet", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98e94b29ffc686e9fe30f64e8c61854171", "name": "OrderedSet", "path": "OrderedSet", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e983664289781c8a24620b2caa3db682d4b", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, {"guid": "bfdfe7dc352907fc980b868725387e98c0d8e968c3f0cabb024c9f76af8f8c93", "name": "Products", "path": "", "sourceTree": "<group>", "type": "group"}, {"children": [{"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e98a3f4680c874ecbcb5b5a6338bc13a426", "path": "Pods-Runner.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e9814181933c973297b0001d0e9ee64381d", "path": "Pods-Runner-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e9887440869a53d6020d968ddf6b9b30aa0", "path": "Pods-Runner-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98fed0970e703d355c9aff813f280aafdc", "path": "Pods-Runner-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e9826e505869a67d81656859ded68178c1d", "path": "Pods-Runner-frameworks.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e98de02ac5005ab1eb052abf25c63731e95", "path": "Pods-Runner-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.script.sh", "guid": "bfdfe7dc352907fc980b868725387e98f6f4dcb116856f2da0fa97d3015cb1af", "path": "Pods-Runner-resources.sh", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e980ec6fbca7264a6936f2adfc48dd7f5bc", "path": "Pods-Runner-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e9850cc7fc2d23136fb4fac488d6c47df20", "path": "Pods-Runner.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98dc1a21852c7a085c7dd8f65cf0fa9907", "path": "Pods-Runner.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98f17abf55d75f35efcaf45a1185b085b6", "path": "Pods-Runner.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e98cf3c1972df678a5e36df75a46391500d", "name": "Pods-Runner", "path": "Target Support Files/Pods-Runner", "sourceTree": "<group>", "type": "group"}, {"children": [{"fileType": "sourcecode.module-map", "guid": "bfdfe7dc352907fc980b868725387e984c4f55ec853c945e234980557a98aed8", "path": "Pods-RunnerTests.modulemap", "sourceTree": "<group>", "type": "file"}, {"fileType": "text", "guid": "bfdfe7dc352907fc980b868725387e98fc0f7e7242f459f81e455145932dcafd", "path": "Pods-RunnerTests-acknowledgements.markdown", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e985f8b68b152f46f18718da20c04e675cb", "path": "Pods-RunnerTests-acknowledgements.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.objc", "guid": "bfdfe7dc352907fc980b868725387e98022654f1ff78dd844d694dba2439dab2", "path": "Pods-RunnerTests-dummy.m", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.plist.xml", "guid": "bfdfe7dc352907fc980b868725387e989e5ad6b9a07953a12c7008a15bd9c99c", "path": "Pods-RunnerTests-Info.plist", "sourceTree": "<group>", "type": "file"}, {"fileType": "sourcecode.c.h", "guid": "bfdfe7dc352907fc980b868725387e98e5e8bcdff29e5f8321be18f7989b4bc7", "path": "Pods-RunnerTests-umbrella.h", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98144cd18850e477837c238075d5256ffe", "path": "Pods-RunnerTests.debug.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e981b663a2c82f0220040296818ba53477e", "path": "Pods-RunnerTests.profile.xcconfig", "sourceTree": "<group>", "type": "file"}, {"fileType": "text.xcconfig", "guid": "bfdfe7dc352907fc980b868725387e98965b92d39d30a7872295adc2841cd1b1", "path": "Pods-RunnerTests.release.xcconfig", "sourceTree": "<group>", "type": "file"}], "guid": "bfdfe7dc352907fc980b868725387e9859551a2ccb1df711861b574920cd49bf", "name": "Pods-RunnerTests", "path": "Target Support Files/Pods-RunnerTests", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98dafc421ff02609f2772b356038eb9849", "name": "Targets Support Files", "path": "", "sourceTree": "<group>", "type": "group"}], "guid": "bfdfe7dc352907fc980b868725387e98677e601b37074db53aff90e47c8f96d1", "name": "Pods", "path": "", "sourceTree": "<group>", "type": "group"}, "guid": "bfdfe7dc352907fc980b868725387e98", "path": "/Users/<USER>/workspace/chilat2_mall_app/ios/Pods/Pods.xcodeproj", "projectDirectory": "/Users/<USER>/workspace/chilat2_mall_app/ios/Pods", "targets": ["TARGET@v11_hash=fa3b42314b0e69c94c1e51ee1cfb1de4", "TARGET@v11_hash=a20acb914263dc2f05a64efd49d089e3", "TARGET@v11_hash=224ca1e2504cc0de26aa5b8779421879", "TARGET@v11_hash=034f57f07b02e4404ddfb752b54f39a0", "TARGET@v11_hash=bf00703c790da9c8e67952e74bf20635", "TARGET@v11_hash=b6abeb243488735fee749213cbb10643", "TARGET@v11_hash=235bb79bf6a7333a7408a822b9f0e109", "TARGET@v11_hash=2ce48c9d60e1f8c669848cb5cdb9d922", "TARGET@v11_hash=a9d366b2cbbb1524e3d68c5caf3b0890", "TARGET@v11_hash=612706b340184d49344364c6d1032db5", "TARGET@v11_hash=ff2325a9b5f524769124c1ae27c7157e", "TARGET@v11_hash=a0916208f5ab9ba57f605c3e80df4c8c", "TARGET@v11_hash=21c9fbc811d1ce908918384d91e1a68f", "TARGET@v11_hash=ac78f5cbaebba2abaea4ddd70bad67df", "TARGET@v11_hash=2b5bba1c0313797ee5545faaa7513ff1", "TARGET@v11_hash=65b247206b57ae41992ca8b219a9af86", "TARGET@v11_hash=69707ecd0611e8d019af893fb83c4204", "TARGET@v11_hash=520be341dbd8c00a55602f2cb01c95dd", "TARGET@v11_hash=f29d56e8f9b8429e56b7718d13ed6502", "TARGET@v11_hash=cb66bc5560d7abc634348c13516c9954", "TARGET@v11_hash=e78305d0dacbc4d75826129015ee2977"]}