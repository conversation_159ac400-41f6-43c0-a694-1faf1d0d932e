import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/services/order.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';

class OrderCancelDrawer extends StatefulWidget {
  final String orderNo;
  final Function? onCancelSuccess;

  const OrderCancelDrawer({
    Key? key,
    required this.orderNo,
    this.onCancelSuccess,
  }) : super(key: key);

  @override
  State<OrderCancelDrawer> createState() => _OrderCancelDrawerState();
}

class _OrderCancelDrawerState extends State<OrderCancelDrawer> {
  final _formKey = GlobalKey<FormState>();
  List<Map<String, dynamic>> _cancelReasons = [];
  String? _cancelReasonId;
  String? _cancelRemark;
  bool _isLoadingReasons = false;
  String? selectedReason;

  @override
  void initState() {
    super.initState();
    _loadCancelReasons();
  }

  Future<void> _loadCancelReasons() async {
    setState(() => _isLoadingReasons = true);
    try {
      final res = await OrderService.useGetCancelOrderReasonConfig({});
      if (res['result']['code'] == 200) {
        setState(() {
          _cancelReasons =
              List<Map<String, dynamic>>.from(res['data']['reasonList']);
        });
      }
    } catch (e) {
      print('加载取消原因错误: $e');
      Get.snackbar(
        'Error',
        I18n.of(context)?.translate('cm_order.getReasonsFailed') ?? '获取取消原因失败',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      setState(() => _isLoadingReasons = false);
    }
  }

  Future<void> _submitCancel() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoadingReasons = true);

    try {
      final res = await OrderService.useCancelOrder({
        'orderNo': widget.orderNo,
        'cancelReasonId': _cancelReasonId,
        'cancelRemark': _cancelRemark ?? '',
        'cancelReason': selectedReason,
      });

      if (res['result']['code'] == 200) {
        await widget.onCancelSuccess?.call();
        Navigator.of(context).pop();
        Get.snackbar(
          'Success',
          I18n.of(context)?.translate('cm_order.cancelSuccess') ?? '订单取消成功',
          snackPosition: SnackPosition.TOP,
        );
      } else {
        Get.snackbar(
          'Error',
          res['result']['message'] ??
              I18n.of(context)?.translate('cm_order.cancelFailed') ??
              '订单取消失败',
          snackPosition: SnackPosition.TOP,
        );
      }
    } catch (e) {
      print('取消订单错误: $e');
      Get.snackbar(
        'Error',
        I18n.of(context)?.translate('cm_order.cancelFailed') ?? '订单取消失败',
        snackPosition: SnackPosition.TOP,
      );
    } finally {
      setState(() => _isLoadingReasons = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.only(
        bottom: MediaQuery.viewInsetsOf(context).bottom,
      ),
      child: Container(
        color: Colors.white,
        padding: EdgeInsets.all(16.sp),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.orderCancelTitle') ??
                        'Anulación de pedidos',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                  GestureDetector(
                    onTap: () => Navigator.of(context).pop(),
                    child: Icon(Icons.close, size: 20.sp),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Row(
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.orderCancelReason') ??
                        'Motivo de la anulación',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                  Text(' *',
                      style: TextStyle(color: Colors.red, fontSize: 14.sp)),
                ],
              ),
              SizedBox(height: 8.h),
              if (_isLoadingReasons)
                Center(child: CircularProgressIndicator())
              else
                DropdownButtonFormField<String>(
                  decoration: InputDecoration(
                    contentPadding: EdgeInsets.all(6.sp),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4.r),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(4.r),
                      borderSide: BorderSide(color: Colors.grey[300]!),
                    ),
                  ),
                  value: _cancelReasonId,
                  isExpanded: true,
                  hint: Text(
                    I18n.of(context)?.translate('cm_order.chooseReason') ??
                        'Por favor, seleccione el motivo de la anulación.',
                    style: TextStyle(
                      color: Colors.grey[400],
                      fontSize: 12.sp,
                    ),
                  ),
                  items: _cancelReasons.map((reason) {
                    return DropdownMenuItem(
                      value: reason['id'].toString(),
                      child: Text(
                        reason['reason'],
                        style: TextStyle(fontSize: 14.sp),
                      ),
                    );
                  }).toList(),
                  onChanged: (String? value) {
                    setState(() {
                      _cancelReasonId = value;
                      selectedReason = _cancelReasons.firstWhere((reason) =>
                          reason['id'].toString() == value)['reason'];
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return I18n.of(context)
                              ?.translate('cm_order.chooseReason') ??
                          'Por favor, seleccione el motivo de la anulación.';
                    }
                    return null;
                  },
                ),
              SizedBox(height: 16.h),
              Row(
                children: [
                  Text(
                    I18n.of(context)?.translate('cm_order.orderCancelRemark') ??
                        'Anulación el comentario',
                    style: TextStyle(fontSize: 14.sp),
                  ),
                  Text(' *',
                      style: TextStyle(color: Colors.red, fontSize: 14.sp)),
                ],
              ),
              SizedBox(height: 8.h),
              TextFormField(
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: I18n.of(context)
                          ?.translate('cm_order.inputRemark') ??
                      'Por favor, introduzca el comentario de la anulación.',
                  hintStyle: TextStyle(
                    color: Colors.grey[400],
                    fontSize: 12.sp,
                  ),
                  contentPadding: EdgeInsets.all(6.sp),
                  border: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4.r),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderRadius: BorderRadius.circular(4.r),
                    borderSide: BorderSide(color: Colors.grey[300]!),
                  ),
                ),
                onChanged: (value) => _cancelRemark = value,
                validator: (value) {
                  if (value == null || value.trim().isEmpty) {
                    return I18n.of(context)
                            ?.translate('cm_order.inputRemark') ??
                        'Por favor, introduzca el comentario de la anulación.';
                  }
                  return null;
                },
              ),
              SizedBox(height: 24.h),
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => Navigator.of(context).pop(),
                      style: OutlinedButton.styleFrom(
                        backgroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(vertical: 10.sp),
                        side: BorderSide(color: Colors.grey[300]!),
                      ),
                      child: Text(
                        I18n.of(context)?.translate('cm_order.orderCancelNo') ??
                            'No',
                        style:
                            TextStyle(color: Colors.black87, fontSize: 14.sp),
                      ),
                    ),
                  ),
                  SizedBox(width: 30.sp),
                  Expanded(
                    child: ElevatedButton(
                      onPressed: _isLoadingReasons ? null : _submitCancel,
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primaryColor,
                        padding: EdgeInsets.symmetric(vertical: 10.sp),
                      ),
                      child: _isLoadingReasons
                          ? SizedBox(
                              width: 20.sp,
                              height: 20.sp,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor:
                                    AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          : Text(
                              I18n.of(context)
                                      ?.translate('cm_order.orderCancelYes') ??
                                  'Sí.',
                              style: TextStyle(
                                  color: Colors.white, fontSize: 14.sp),
                            ),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}
