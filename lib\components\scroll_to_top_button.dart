import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

/// 通用置顶按钮组件
///
/// 这个组件可以在任何页面中使用，只需要提供一个ScrollController
/// 可以通过Stack和Positioned来放置在页面的任何位置
class ScrollToTopButton extends StatefulWidget {
  final ScrollController controller;
  final double showThreshold;
  final double? size;

  const ScrollToTopButton({
    required this.controller,
    this.showThreshold = 300,
    this.size,
    super.key,
  });

  @override
  State<ScrollToTopButton> createState() => _ScrollToTopButtonState();
}

class _ScrollToTopButtonState extends State<ScrollToTopButton>
    with SingleTickerProviderStateMixin {
  bool _show = false;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;

  @override
  void initState() {
    super.initState();
    widget.controller.addListener(_scrollListener);

    // Initialize animation controller
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 200),
    );

    // Initialize scale animation
    _scaleAnimation = Tween<double>(begin: 1.0, end: 1.2).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOut,
      ),
    );
  }

  @override
  void dispose() {
    widget.controller.removeListener(_scrollListener);
    _animationController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final shouldShow = widget.controller.positions.isNotEmpty &&
        widget.controller.position.pixels > widget.showThreshold;

    if (_show != shouldShow) {
      setState(() {
        _show = shouldShow;
      });
    }
  }

  Future<void> _handleTap() async {
    if (!_show) return;

    // Play the animation
    await _animationController.forward();
    await _animationController.reverse();

    // Scroll to top
    widget.controller.animateTo(
      0,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeOut,
    );
  }

  @override
  Widget build(BuildContext context) {
    final buttonSize = widget.size ?? 40.sp;

    return AnimatedOpacity(
      opacity: _show ? 1 : 0,
      duration: const Duration(milliseconds: 300),
      child: GestureDetector(
        onTap: _handleTap,
        child: ScaleTransition(
          scale: _scaleAnimation,
          child: SvgPicture.asset(
            'assets/images/common/arrow-upward.svg',
            width: buttonSize,
            height: buttonSize,
          ),
        ),
      ),
    );
  }
}

/// 扩展方法，用于在任何Widget上添加置顶按钮
extension ScrollToTopExtension on Widget {
  /// 在Widget上添加置顶按钮
  ///
  /// [controller] - 滚动控制器
  /// [showThreshold] - 显示按钮的滚动阈值
  /// [bottom] - 按钮距离底部的距离
  /// [right] - 按钮距离右侧的距离
  /// [size] - 按钮大小
  Widget withScrollToTop({
    required ScrollController controller,
    double showThreshold = 300,
    double bottom = 140,
    double right = 12,
    double? size,
  }) {
    return Stack(
      children: [
        this,
        Positioned(
          bottom: bottom.sp,
          right: right.sp,
          child: ScrollToTopButton(
            controller: controller,
            showThreshold: showThreshold,
            size: size,
          ),
        ),
      ],
    );
  }
}
