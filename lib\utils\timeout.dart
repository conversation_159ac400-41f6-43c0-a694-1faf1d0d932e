class TimeoutMap<K, V> {
  // 存储结构：key -> (value, 过期时间)
  final Map<K, _TimeoutItem<V>> _map = {};

  // 存入数据（timeoutSeconds：超时秒数，0 表示永不过期）
  void put(K key, V value, Future<dynamic> future, int timeoutSeconds) {
    int? expireTime;
    if (timeoutSeconds > 0) {
      expireTime =
          DateTime.now().millisecondsSinceEpoch + (timeoutSeconds * 1000);
    }
    _map[key] =
        _TimeoutItem(value: value, future: future, expireTime: expireTime);
  }

  // 获取数据（返回 null 表示超时或不存在）
  dynamic get(K key, Future<dynamic> future,
      {int timeoutSeconds = 3600}) async {
    final item = _map[key];
    // print("==>>TODO 3342: $key, $item, $timeoutSeconds");
    // if (item == null) return null;

    // 永不过期
    if (item?.expireTime == null && item?.value != null) return item?.value;
    // print("==>>TODO 3343: $key, ${item?.expireTime}");
    // 判断是否超时
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    // print(
    //     "==>>TODO 3344: $currentTime, ${((item?.expireTime ?? 0) - currentTime)}");
    // 未超时并且已有值
    if (currentTime < (item?.expireTime ?? 0)) {
      // 值是否存在
      if (item?.value == null) {
        final result = await future;
        put(key, result, future, timeoutSeconds);
        return result;
      } else {
        return item?.value;
      }
    } else {
      //超时
      final result = await future;
      put(key, result, future, timeoutSeconds);
      return result;
    }

    // if (currentTime > item.expireTime!) {
    //   _map.remove(key); // 超时自动删除
    //   return null;
    // }

    // return item.value;
  }

  // 判断键是否存在且未超时
  // bool containsValid(K key) => get(key) != null;

  // 清除所有数据
  void clear() => _map.clear();

  // 清除超时数据
  void clearExpired() {
    final currentTime = DateTime.now().millisecondsSinceEpoch;
    _map.removeWhere((key, item) =>
        item.expireTime != null && currentTime > item.expireTime!);
  }
}

// 内部辅助类：存储值和过期时间
class _TimeoutItem<V> {
  final V? value;
  final int? expireTime; // 毫秒时间戳，null 表示永不过期
  final Future<dynamic> future;

  _TimeoutItem({this.value, this.expireTime, required this.future});
}
