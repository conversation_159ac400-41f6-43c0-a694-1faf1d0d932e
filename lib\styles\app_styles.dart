import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/styles/colors.dart';
import 'package:pull_to_refresh/pull_to_refresh.dart';

/// 应用通用样式
class AppStyles {
  /// 创建MaterialColor
  static MaterialColor createMaterialColor(Color color) {
    return MaterialColor(
      color.value,
      <int, Color>{
        50: color,
        100: color,
        200: color,
        300: color,
        400: color,
        500: color,
        600: color,
        700: color,
        800: color,
        900: color,
      },
    );
  }

  //  这里的主题先注释，需要的再加,现在还没有统一主题
  /// 获取应用主题
  static ThemeData getAppTheme() {
    return ThemeData(
      fontFamily: 'Roboto',
      primarySwatch: createMaterialColor(AppColors.primaryColor),
      primaryColor: AppColors.primaryColor,
      colorScheme: ColorScheme.fromSwatch(
        primarySwatch: createMaterialColor(AppColors.primaryColor),
      ),
      scaffoldBackgroundColor: AppColors.primaryBackground,
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primaryBackground,
        scrolledUnderElevation: 0,
        elevation: 0,
        surfaceTintColor: Colors.transparent,
      ),
      // 按钮主题
      elevatedButtonTheme: ElevatedButtonThemeData(),
      // 文本按钮主题
      textButtonTheme: TextButtonThemeData(),
      // 轮廓按钮主题
      outlinedButtonTheme: OutlinedButtonThemeData(),
      // 复选框主题
      checkboxTheme: CheckboxThemeData(),
      // 单选按钮主题
      radioTheme: RadioThemeData(),
      // 开关主题
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith((states) {
          return states.contains(MaterialState.selected)
              ? AppColors.primaryColor
              : null;
        }),
        trackColor: MaterialStateProperty.resolveWith((states) {
          return states.contains(MaterialState.selected)
              ? AppColors.primaryColor.withOpacity(0.5)
              : null;
        }),
      ),
      // 进度指示器主题
      progressIndicatorTheme: ProgressIndicatorThemeData(
        color: AppColors.primaryColor,
      ),
      // Tab主题
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.primaryColor,
        indicatorColor: AppColors.primaryColor,
      ),

      // 对话框主题
      dialogTheme: DialogTheme(
        backgroundColor: Colors.white,
        shape:
            RoundedRectangleBorder(borderRadius: BorderRadius.circular(4.sp)),
      ),
    );
  }

  /// 下拉刷新头部样式
  static WaterDropMaterialHeader refreshHeader() {
    return WaterDropMaterialHeader();
  }
}
