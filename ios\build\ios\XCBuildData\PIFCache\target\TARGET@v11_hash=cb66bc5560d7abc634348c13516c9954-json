{"buildConfigurations": [{"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e988725d6ae281ebdc12e63fc812be9773f", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "ONLY_ACTIVE_ARCH": "NO", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98d6da72224a0002e2c8d4ad6a4140a101", "name": "Debug"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894947526e324545b92715452e2a1527b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e984cab3693a69eb1c553c791758183685f", "name": "Profile"}, {"baseConfigurationFileReference": "bfdfe7dc352907fc980b868725387e9894947526e324545b92715452e2a1527b", "buildSettings": {"CLANG_ENABLE_OBJC_WEAK": "NO", "CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER": "NO", "CODE_SIGN_IDENTITY[sdk=appletvos*]": "", "CODE_SIGN_IDENTITY[sdk=iphoneos*]": "", "CODE_SIGN_IDENTITY[sdk=watchos*]": "", "CURRENT_PROJECT_VERSION": "1", "DYLIB_COMPATIBILITY_VERSION": "1", "DYLIB_CURRENT_VERSION": "1", "DYLIB_INSTALL_NAME_BASE": "@rpath", "ENABLE_BITCODE": "NO", "ENABLE_MODULE_VERIFIER": "NO", "ENABLE_USER_SCRIPT_SANDBOXING": "NO", "EXCLUDED_ARCHS[sdk=iphoneos*]": "$(inherited) armv7", "EXCLUDED_ARCHS[sdk=iphonesimulator*]": "$(inherited) i386", "FRAMEWORK_SEARCH_PATHS[sdk=iphoneos*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64\" $(inherited)", "FRAMEWORK_SEARCH_PATHS[sdk=iphonesimulator*]": "\"/Users/<USER>/env/flutter/bin/cache/artifacts/engine/ios-release/Flutter.xcframework/ios-arm64_x86_64-simulator\" $(inherited)", "GCC_PREFIX_HEADER": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-prefix.pch", "GENERATE_INFOPLIST_FILE": "NO", "INFOPLIST_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview-Info.plist", "INSTALL_PATH": "$(LOCAL_LIBRARY_DIR)/Frameworks", "IPHONEOS_DEPLOYMENT_TARGET": "12.0", "MODULEMAP_FILE": "Target Support Files/webview_flutter_wkwebview/webview_flutter_wkwebview.modulemap", "OTHER_LDFLAGS": "$(inherited) -framework Flutter", "PRODUCT_MODULE_NAME": "webview_flutter_wkwebview", "PRODUCT_NAME": "webview_flutter_wkwebview", "SDKROOT": "iphoneos", "SKIP_INSTALL": "YES", "SWIFT_ACTIVE_COMPILATION_CONDITIONS": "$(inherited) ", "SWIFT_INSTALL_OBJC_HEADER": "YES", "SWIFT_VERSION": "5.0", "TARGETED_DEVICE_FAMILY": "1,2", "VALIDATE_PRODUCT": "YES", "VALID_ARCHS[sdk=iphonesimulator*]": "$(ARCHS_STANDARD)", "VERSIONING_SYSTEM": "apple-generic", "VERSION_INFO_PREFIX": ""}, "guid": "bfdfe7dc352907fc980b868725387e98f480631d57f584ae82569893436b3d19", "name": "Release"}], "buildPhases": [{"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9864834471e4989ba07e4ec3def766a928", "guid": "bfdfe7dc352907fc980b868725387e98faa2d77c0ef7eb87f7f8421e51c1b40a", "headerVisibility": "public"}], "guid": "bfdfe7dc352907fc980b868725387e98a1e3dac75ef5081293a3cce12f493338", "type": "com.apple.buildphase.headers"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9846a79e7f207eb9f7d74c9c91d3c92341", "guid": "bfdfe7dc352907fc980b868725387e98d76f0d420f275108ef56ce8538cf49a6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984ac7959d3e183eee8a14c0a90e8791b0", "guid": "bfdfe7dc352907fc980b868725387e982064cdc4761bcd789a246d7bf6dc9441"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98245ccaf06f831c7f4068b3da72dd0234", "guid": "bfdfe7dc352907fc980b868725387e9889d8e45ac06a242a04e867fe3c38558b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e95f154c1c347921ff55d95d06decfea", "guid": "bfdfe7dc352907fc980b868725387e982982488f924f337985522ee957b716ec"}, {"fileReference": "bfdfe7dc352907fc980b868725387e983aaf36a7347ad5d72f5a63e5ebc6cee6", "guid": "bfdfe7dc352907fc980b868725387e987bbff7cd3082359e51c2d756c3795b7f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e984303027ca5ae4616796039af30c42093", "guid": "bfdfe7dc352907fc980b868725387e984aa1a6f76d92e6fcdc21d247b5e4da8b"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dc3316b4ca3862abbff9e2be115bdbb6", "guid": "bfdfe7dc352907fc980b868725387e98299c495a1f3bef48b48626c41f396c02"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed209853db0a4365cf0e745cc4550e2a", "guid": "bfdfe7dc352907fc980b868725387e98d9fbe0993de6cc036722a32e4874e537"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f4393c3e94b6c5dd5bbe14b1b50849e2", "guid": "bfdfe7dc352907fc980b868725387e980d22b841610ccd78785243ba64c78006"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9882fc3f0576567cdab3359153ed317663", "guid": "bfdfe7dc352907fc980b868725387e98f9da4d19a6779a21441a77926f23cb62"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986385126e4b8d0aadf390ea5de92bc11c", "guid": "bfdfe7dc352907fc980b868725387e9823326b98304e3cfffae452840ab76b03"}, {"fileReference": "bfdfe7dc352907fc980b868725387e988a4ccabcfbfbffe27008f61a1cc0d638", "guid": "bfdfe7dc352907fc980b868725387e982740e3767bf04613e5fea2c3d8577967"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98135bef56e17ed72b7166f9c10d251154", "guid": "bfdfe7dc352907fc980b868725387e9877f3fccc090ce19fa3e42fd8501e685d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9863ef6ba8955f43d0659943767428bd03", "guid": "bfdfe7dc352907fc980b868725387e983f29880515600466416a354b798133ba"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9821402dd626927ebb8ee9621ee0283ce3", "guid": "bfdfe7dc352907fc980b868725387e984438b8a587f080bca6d85b786c0123a7"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f75e81475e53ff24ee312da0c8b891e9", "guid": "bfdfe7dc352907fc980b868725387e984e01475b2159a7ff340d3f23a8039dd4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ec3f550376bced9707c689f68db14c01", "guid": "bfdfe7dc352907fc980b868725387e98095b9b1810904d874886f6d6f0dba23f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98af42a73ece4a6f24d50f0ed045ad14eb", "guid": "bfdfe7dc352907fc980b868725387e98e2badecfc7b5d2ae705c6c3de9c8fc7c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982213b1b076ab416c965cee7cf1d99686", "guid": "bfdfe7dc352907fc980b868725387e98d7d2b9d00b8765292f1c024d0255a58c"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98dcaf6a87e0c7cff741560915d6b5df2f", "guid": "bfdfe7dc352907fc980b868725387e9891f14de83c55380115308487e2d95637"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98e01070d3a72f4aadd9c8f0a0a66fdc98", "guid": "bfdfe7dc352907fc980b868725387e98f46c1b9ac3dba180111e6df7acfcbe50"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98eb76cdcc1f309f5b5fb9777cea8d0834", "guid": "bfdfe7dc352907fc980b868725387e9888b1aee3f60951abc2a6e9808703f1d9"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f8146a0c77774f2627308c94cc769a8e", "guid": "bfdfe7dc352907fc980b868725387e986b190244ede8732272ccf7d48d1700ff"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9818b412eb722b4ef4bd0d7fdd335e940e", "guid": "bfdfe7dc352907fc980b868725387e98b1995b80607242a0b007d1c1cb81f389"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803361c49d7a3342a28f5d0ebc7e49168", "guid": "bfdfe7dc352907fc980b868725387e9887bdb8a986fa8bd9c354eec52c204717"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9879a0cc5966fa0d54638f51788982e269", "guid": "bfdfe7dc352907fc980b868725387e9816c2e33f31628ad53e9dc3d7a227b281"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9846acdc4941a1e0e386e555274065ac9d", "guid": "bfdfe7dc352907fc980b868725387e98b9e0fcb4d8877ccdf18c100b304af26f"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9819433f0f77370f4dd31e8f21e1606e9e", "guid": "bfdfe7dc352907fc980b868725387e98125a0d6cf8970bc5a62806731123d2a5"}, {"fileReference": "bfdfe7dc352907fc980b868725387e986e355dc5f3ca0580826fd708fde4b4ec", "guid": "bfdfe7dc352907fc980b868725387e98f1b5eca86564b3e79f8e7885918028b8"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982aa7b6eb55f09885f3d2abb6c26e350c", "guid": "bfdfe7dc352907fc980b868725387e98db62bfd78f5e365b5c0593fa9252c62d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9820d26b16a5d70653d24ab7677d315bca", "guid": "bfdfe7dc352907fc980b868725387e98d633ddfa5886e1ec5bb3569f5c5d36f4"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f568d843e000a39a97ba16e1dbc2012c", "guid": "bfdfe7dc352907fc980b868725387e98c3ae118535aaa4ec8a1815b65a80b3dc"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fd6e7d80003707ae789b7bacb9f55809", "guid": "bfdfe7dc352907fc980b868725387e98edf7be10a07cd938e0b7bc59be45f4c1"}, {"fileReference": "bfdfe7dc352907fc980b868725387e982a13c2a349535f1014ba4553edab94d0", "guid": "bfdfe7dc352907fc980b868725387e981b5e0773246664593cec87ff8375c9df"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f383ff4dbe535e5de25a75f3c904cc83", "guid": "bfdfe7dc352907fc980b868725387e984fa980fdb2fe627260c8ca2c4f0d74ed"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9858afe87177b91decb4667dd1423e01c4", "guid": "bfdfe7dc352907fc980b868725387e98901e4c414a0510309ea58a994bcb8ff6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fe9b5cf701a55e4e522b749e75ea030f", "guid": "bfdfe7dc352907fc980b868725387e980f1b927cce21faeb38ea8d3857fa1edf"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9822e8e3bc9ea6d1e1ca02d2bba95ecd36", "guid": "bfdfe7dc352907fc980b868725387e983d333770142b0a175913b1d5290c52f6"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98d847322d3e2b6e83be4208b2ef42bb18", "guid": "bfdfe7dc352907fc980b868725387e98d259083229b4eed28de4a364a94222ca"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98ed53979a4f36581126dc43ef203edc3a", "guid": "bfdfe7dc352907fc980b868725387e981007b01942a8dbc49dbd8e6ec267d331"}, {"fileReference": "bfdfe7dc352907fc980b868725387e9803283781717421a5d27bb7dce0e9ac9d", "guid": "bfdfe7dc352907fc980b868725387e98e9b4ee92e19768824f85187c304c7363"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98fb475591857ac83474d794c8b856a4de", "guid": "bfdfe7dc352907fc980b868725387e98fc598feab4c8c9e7d212c5315cbb343d"}, {"fileReference": "bfdfe7dc352907fc980b868725387e98f415c46adb8232a62eb8e300b9580d59", "guid": "bfdfe7dc352907fc980b868725387e98874f7f9ef1f2456468a2fc974b72c1d9"}], "guid": "bfdfe7dc352907fc980b868725387e980055fbf3a11bc15129d28ebbe5cc1480", "type": "com.apple.buildphase.sources"}, {"buildFiles": [{"fileReference": "bfdfe7dc352907fc980b868725387e9867aa7825f197b5c58a57320b4ccdc64e", "guid": "bfdfe7dc352907fc980b868725387e987ead9e15fc09a570554fa963eed1137d"}], "guid": "bfdfe7dc352907fc980b868725387e98da9cd099e7190758cfc42156c0f4e548", "type": "com.apple.buildphase.frameworks"}, {"buildFiles": [{"guid": "bfdfe7dc352907fc980b868725387e98b8defae1b39d33d1c1b6a7ec3e8cf51c", "targetReference": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1"}], "guid": "bfdfe7dc352907fc980b868725387e987fd8694c1d36b88c99f78feb0d04dd25", "type": "com.apple.buildphase.resources"}], "buildRules": [], "dependencies": [{"guid": "bfdfe7dc352907fc980b868725387e989da425bb6d6d5d8dbb95e4afffb82217", "name": "Flutter"}, {"guid": "bfdfe7dc352907fc980b868725387e987c93e943aa0a38b5f6684beaf6b4a3a1", "name": "webview_flutter_wkwebview-webview_flutter_wkwebview_privacy"}], "guid": "bfdfe7dc352907fc980b868725387e988efdc4dd0ac29b43123295eca853f4ed", "name": "webview_flutter_wkwebview", "predominantSourceCodeLanguage": "Xcode.SourceCodeLanguage.Swift", "productReference": {"guid": "bfdfe7dc352907fc980b868725387e980823710353e0487822d6da09bf8d6254", "name": "webview_flutter_wkwebview.framework", "type": "product"}, "productTypeIdentifier": "com.apple.product-type.framework", "provisioningSourceData": [{"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Debug", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Profile", "provisioningStyle": 1}, {"bundleIdentifierFromInfoPlist": "${PRODUCT_BUNDLE_IDENTIFIER}", "configurationName": "Release", "provisioningStyle": 1}], "type": "standard"}