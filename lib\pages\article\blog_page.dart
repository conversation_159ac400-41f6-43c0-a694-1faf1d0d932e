// 博客列表

import 'package:cached_network_image/cached_network_image.dart';
import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/config/config.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:chilat2_mall_app/models/common.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/services/services.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class BlogPage extends StatefulWidget {
  const BlogPage({super.key});

  @override
  State<BlogPage> createState() => _BlogPageState();
}

class _BlogPageState extends State<BlogPage> {
  bool _isLoading = true;
  bool _hasMore = true;
  List<GetBlogListModel> articleList = [];
  PageInfo _pageInfo = PageInfo(current: 1, pages: 1);
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    onPageData();
    _scrollController.addListener(_scrollListener);
  }

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    if (_scrollController.position.pixels ==
            _scrollController.position.maxScrollExtent &&
        !_isLoading &&
        _hasMore) {
      _pageInfo.current = _pageInfo.current + 1;

      onPageData(scroll: true);
    }
  }

  Future<void> onPageData({bool? scroll = false}) async {
    try {
      setState(() => _isLoading = scroll == true ? false : true);

      dynamic res = await ArticleAPI.useGetBlogList({
        "page": {
          "current": _pageInfo.current,
          "size": _pageInfo.size,
        },
        "deviceType": "VISIT_DEVICE_TYPE_H5",
      });
      if (res?['result']?['code'] == 200) {
        GetBlogListResp resp = GetBlogListResp.fromJson(res);

        setState(() {
          _pageInfo = PageInfo.fromJson(res['page']);
          if (_pageInfo.current >= _pageInfo.pages) {
            _hasMore = false;
          }

          if (scroll == true) {
            articleList.addAll(resp.data ?? []);
          } else {
            articleList = resp.data ?? [];
          }
        });
      }
    } catch (e) {
      showErrorMessage(e.toString());
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return _isLoading
        ? LoadingWidget()
        : AppScaffold(
            showScrollToTopButton: true,
            scrollController: _scrollController,
            body: Column(
              children: [
                SearchHeader(showHomeIcon: true),
                Expanded(
                  child: ListView.builder(
                    controller: _scrollController,
                    itemCount: articleList.length + 1,
                    itemBuilder: (context, index) {
                      if (index < articleList.length) {
                        dynamic item = articleList[index];
                        return _buildBlogItem(item);
                      } else {
                        return _buildLoader();
                      }
                    },
                  ),
                ),
              ],
            ),
          );
  }

  Widget _buildBlogItem(GetBlogListModel item) {
    return Container(
      padding: const EdgeInsets.all(8),
      child: GestureDetector(
        onTap: () {
          Get.toNamed(AppRoutes.ArticlePage,
              arguments: {'articleCode': item.articleCode});
        },
        child: Row(children: [
          Expanded(
            flex: 1,
            child: Container(
              padding: EdgeInsets.all(2),
              child: CachedNetworkImage(
                height: 60,
                width: 90,
                fit: BoxFit.cover,
                imageUrl: item.logo ?? APP_LOGO,
                placeholder: (context, url) =>
                    const CircularProgressIndicator(),
              ),
            ),
          ),
          Expanded(
              flex: 2,
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 2, horizontal: 6),
                child: Text(
                  item.title ?? "",
                  softWrap: true,
                  maxLines: 3,
                  overflow: TextOverflow.ellipsis,
                ),
              ))
        ]),
      ),
    );
  }

  Widget _buildLoader() {
    if (_isLoading && _hasMore) {
      return Padding(
        padding: const EdgeInsets.all(24),
        child: Center(
          child: Column(
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                I18n.of(context)!.translate("cm_blog.loadingMore"),
                style: TextStyle(color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      );
    } else if (!_hasMore) {
      return const SizedBox(); // 最后一条数据后不再显示提示
    } else {
      return Container();
    }
  }
}
