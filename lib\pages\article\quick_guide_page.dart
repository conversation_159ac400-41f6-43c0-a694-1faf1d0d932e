import 'package:get/get.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:chilat2_mall_app/utils/my_navigator.dart';
import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/components/my_footer.dart';
import 'package:chilat2_mall_app/utils/global.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:chilat2_mall_app/components/app_scaffold.dart';

class QuickGuidePage extends StatefulWidget {
  const QuickGuidePage({Key? key}) : super(key: key);

  @override
  State<QuickGuidePage> createState() => _QuickGuidePageState();
}

class _QuickGuidePageState extends State<QuickGuidePage> {
  late final List<GlobalKey> _sectionKeys;

  @override
  void initState() {
    super.initState();
    _sectionKeys = List.generate(6, (index) => GlobalKey());
  }

  final String headerBg =
      "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/05/cd4d768f-a83d-4dfc-a5d8-7f8cd92a93e6.jpg";

  final List<Map<String, String>> quickGuideData = [
    {
      "title": "Buscar los productos que desea comprar",
      "navId": "Paso1",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/c6ead570-6555-40da-b7f1-da197268938c.png",
    },
    {
      "title": "Empezar un nuevo pedido",
      "navId": "Paso2",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/069baad3-1046-4d7a-9325-bb2bbc760bcf.png",
    },
    {
      "title": "Cotización y pago del pedido",
      "navId": "Paso3",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/39e31996-b744-45bb-be29-794982a586cc.png",
    },
    {
      "title": "Inspección y control de calidad",
      "navId": "Paso4",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/677f3066-1ba5-4e93-809e-da62e200be6f.png",
    },
    {
      "title": "Presupuesto final de envío y pago",
      "navId": "Paso5",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/2aadefc2-b252-4e53-b502-8a1cb9e04a0c.png",
    },
    {
      "title": "Envío, importación y entrega del pedido",
      "navId": "Paso6",
      "imgUrl":
          "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/08/02/9f10c523-1e30-4930-8e13-668181436168.png",
    },
  ];

  final Map<String, String> bannerImages = {
    "bannerBg1":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/5303f07a-d9d4-42be-a303-6b42419db29a.jpg",
    "bannerBg2":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/29/821a2b65-66e1-4e8f-bbd9-8f1aacd6ec95.png",
    "bannerBg3":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/23/be6cdf4b-4eab-48bf-aca8-f99967ef632c.png",
    "bannerBg4":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/6ee99ba1-799c-4623-bb84-50cb60120377.png",
    "bannerBg5":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/e0828e8c-bb95-4aa3-b375-3d28d0080a9e.png",
    "bannerBg6":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/86f9df5e-c9a1-437c-94fa-f964acbfdec6.png",
    "bannerBg7":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/61ebcda2-d650-4ab1-a872-fb17204c4f20.png",
    "bannerBg8":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/002f29b9-c91b-4ca8-84f0-07c568e8ad3b.png",
    "bannerBg9":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/07/30/89562c27-ec52-4396-9c3a-f7ea125ee64a.png",
    "bannerBg10":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/a8b6a575-a063-4f78-b106-8c6a093ac744.png",
    "bannerBg11":
        "https://ldnsso.oss-us-east-1.aliyuncs.com/chilat/prod/2024/12/11/f0bb88d5-a7d0-4640-aa93-90a30e0816a2.png",
  };

  void _scrollToSection(String navId) {
    final keys = {
      "Paso1": 0,
      "Paso2": 1,
      "Paso3": 2,
      "Paso4": 3,
      "Paso5": 4,
      "Paso6": 5,
    };
    if (keys.containsKey(navId)) {
      final idx = keys[navId]!;
      final key = _sectionKeys[idx];
      final context = key.currentContext;
      if (context != null) {
        Scrollable.ensureVisible(
          context,
          duration: const Duration(milliseconds: 500),
          curve: Curves.easeInOut,
        );
      }
    }
  }

  void _onLoginClick() {
    // 实现登录点击事件
    // 由于MyNavigator没有pushNamed方法，我们需要创建一个注册页面并直接push
    // 这里简化处理，实际实现需要根据项目结构创建对应的页面
    MyNavigator.push(
      const Scaffold(
        body: Center(
          child: Text('注册页面'),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final ScrollController scrollController = ScrollController();
    return AppScaffold(
      showScrollToTopButton: true,
      scrollController: scrollController,
      body: SafeArea(
        child: SingleChildScrollView(
          controller: scrollController,
          child: Column(
            children: [
              const SearchHeader(
                showBackIcon: true,
              ),
              _buildHeaderBanner(),
              _buildQuickGuideIndex(),
              _buildSection1(key: _sectionKeys[0]),
              _buildSection2(key: _sectionKeys[1]),
              _buildSection3(key: _sectionKeys[2]),
              _buildSection4(key: _sectionKeys[3]),
              _buildSection5(key: _sectionKeys[4]),
              _buildSection6(key: _sectionKeys[5]),
              // 底部信息栏
              const MyFooter(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeaderBanner() {
    return Container(
      width: double.infinity,
      height: 320.sp,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: NetworkImage(headerBg),
          fit: BoxFit.cover,
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.sp),
            child: Text(
              'La manera más económico y segura de importar desde China',
              style: TextStyle(
                fontSize: 26.sp,
                fontWeight: FontWeight.w500,
                height: 1.1,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 10.sp),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 8.sp),
            child: Text(
              'Sigue nuestra guía de usuario paso a paso y comienza a importar los '
              'productos que tu negocio necesita, sin complicaciones y con total '
              'seguridad.',
              style: TextStyle(
                fontSize: 15.sp,
                height: 1.6,
              ),
              textAlign: TextAlign.center,
            ),
          ),
          SizedBox(height: 24.sp),
          Obx(() => Global.isLogin.value
              ? SizedBox()
              : ElevatedButton(
                  onPressed: () => {
                    AuthHelper.showRegisterModal(
                      context,
                    )
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(30.r),
                    ),
                    padding: EdgeInsets.symmetric(
                      horizontal: 20.sp,
                      vertical: 10.sp,
                    ),
                  ),
                  child: Text(
                    'Registrarse ahora',
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Colors.white,
                    ),
                  ),
                )),
        ],
      ),
    );
  }

  Widget _buildQuickGuideIndex() {
    return Container(
      padding: EdgeInsets.symmetric(
        vertical: 30.sp,
      ),
      color: const Color(0xFFF9F9F9),
      child: Column(
        children: [
          Text(
            'Índice de la guía rápida',
            style: TextStyle(
              fontSize: 22.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFF070027),
            ),
          ),
          SizedBox(height: 20.sp),
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 20.sp),
            child: Column(
              children: quickGuideData.asMap().entries.map((entry) {
                final index = entry.key;
                final item = entry.value;
                return InkWell(
                  onTap: () => _scrollToSection(item['navId']!),
                  child: Padding(
                    padding: EdgeInsets.only(bottom: 20.sp),
                    child: Row(
                      children: [
                        Text(
                          '${index + 1}.',
                          style: TextStyle(
                            fontSize: 15.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFF404040),
                          ),
                        ),
                        SizedBox(width: 5.sp),
                        Image.network(
                          item['imgUrl']!,
                          width: 20.sp,
                          height: 20.sp,
                        ),
                        SizedBox(width: 1.sp),
                        Expanded(
                          child: Text(
                            item['title']!,
                            style: TextStyle(
                              fontSize: 15.sp,
                              fontWeight: FontWeight.w500,
                              color: const Color(0xFF404040),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              }).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection1({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso1",
      title: "Paso 1 Buscar los productos que desea comprar",
      content:
          "Chilat shop cuenta con más de 100,000 proveedores chinos, por lo que dispondrá de una enorme selección de productos en la plataforma. También podemos ayudarle a encontrar los productos que desee y prestarle los servicios en función de sus necesidades.",
      children: [
        _buildBannerWrapper(
          title: "·Usa la barra de búsqueda",
          content: [
            "Introduzca el nombre de la mercancía que quería comprar y pulse el botón de buscar, aparecerá una lista de mercancías relacionadas.",
            "También puede hacer clic en el icono de la cámara y selecciona la imagen del producto que estás buscando.",
          ],
          imageUrl: bannerImages["bannerBg1"]!,
        ),
        _buildBannerWrapper(
          title: "·Página detallada del producto",
          content: [
            "En la página de detalles del producto, podrá ver parámetros detallados del producto, imágenes del producto, descripciones de funciones, precios unitarios y atributos del producto. Puede seleccionar atributos de producto como color, tamaño, tipo y rellenar la cantidad de producto.",
            "Seleccione los artículos y las cantidades que desee y Haga clic en el botón \"Añadir al carrito\".",
          ],
          imageUrl: bannerImages["bannerBg2"]!,
        ),
      ],
    );
  }

  Widget _buildSection2({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso2",
      title: "Paso 2 Empezar un nuevo pedido",
      content:
          "En Chilat shop, después de realizar un nuevo pedido, nuestro equipo en China le proporcionará un presupuesto. Si tiene requisitos adicionales para los productos, seleccione servicios adicionales o añada notas para que podamos proporcionarle presupuestos más precisos.",
      isBg: true,
      children: [
        _buildBannerWrapper(
          title: "·Detalles del pedido",
          content: [
            "Si necesita modificar su pedido, puede ajustar la cantidad del artículo en el carrito de la compra.",
          ],
          imageUrl: bannerImages["bannerBg3"]!,
        ),
        _buildBannerWrapper(
          title: "·Enviar pedido",
          content: [
            "Después de editar, haga clic en el botón \"Confirmar pedido\", rellene la información pertinente y, en unas 24 horas, nuestro servicio de atención al cliente se pondrá en contacto con usted por WhatsApp.",
          ],
          imageUrl: bannerImages["bannerBg4"]!,
        ),
      ],
    );
  }

  Widget _buildSection3({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso3",
      title: "Paso 3 Cotización y pago del pedido",
      content:
          "Una vez realizado el pedido, nuestro equipo en China se pondrá en contacto con el proveedor según los requisitos de su pedido para determinar el coste real.",
      children: [
        _buildBannerWrapper(
          title: "·Pedir presupuesto",
          content: [
            "En un plazo aproximado de 24 horas, nuestro servicio de atención al cliente se pondrá en contacto con usted por WhatsApp y le enviará un presupuesto del pedido y del envío. Los presupuestos de pedidos incluyen: presupuestos del coste del producto y presupuestos estimados de envío.",
            "Costes del producto incluye: precio del producto, comisión.",
            "Flete estimado incluye:Coste transporte internacional、Aranceles、IVA、Envío nacional.",
          ],
          imageUrl: bannerImages["bannerBg5"]!,
          warning:
              "Razón del presupuesto estimado de envío: Una vez embalada la mercancía en nuestro almacén de China, podemos proporcionarle un presupuesto de envío exacto basado en el volumen y el peso reales. En Chilat shop, podemos embalar conjuntamente mercancías de distintos proveedores y pedidos para que usted reduzca aún más sus gastos de envío y aumente su competitividad.",
          warningIcon: Icons.warning_rounded,
        ),
        _buildBannerWrapper(
          title: "·Seleccione el método de envío",
          content: [
            "Antes de pagar, puede elegir el método de envío que más le convenga. En cada opción encontrará un desglose de costes y el plazo de entrega aproximado de cada método de envío. Los métodos de transporte son los siguientes: Transporte marítimo、Transporte aéreo.",
          ],
          imageUrl: bannerImages["bannerBg6"]!,
        ),
        _buildBannerWrapper(
          title: "·Pagar el producto",
          content: [
            "El coste del producto del pedido y los gastos de envío se pagarán por separado. Una vez que hayamos confirmado el presupuesto del pedido, recibirá un correo electrónico con un enlace a la página de presupuesto y pago del coste del producto del pedido.",
            "Costes del producto incluye: precio del producto, comisión.",
          ],
          imageUrl: bannerImages["bannerBg7"]!,
          warning:
              "Cuando complete el pago del pedido, nuestro equipo realizará un pedido a la fábrica para adquirir la mercancía y, a continuación, nuestro almacén en China recibirá la mercancía solicitada.",
          warningIcon: Icons.check_circle_outline_rounded,
        ),
      ],
    );
  }

  Widget _buildSection4({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso4",
      title: "Paso 4 Inspección y control de calidad",
      content:
          "Las mercancías se envían desde los fabricantes a nuestro almacén en China. Cuando recibida la mercancía, nuestro equipo verificará si se ajusta a los requisitos de su pedido, como la cantidad, el aspecto, el color, las especificaciones, etc., y llevará a cabo un control de calidad para garantizar que la mercancía cumple los requisitos. Después de finalizar todo el proceso, nuestro equipo embalará la mercancía en el almacén.",
      isBg: true,
      children: [
        _buildBannerWrapper(
          title: "·Control de Calidad",
          content: [
            "El control de calidad estándar que realizamos cuando recibimos la mercancía desde fábrica incluye las siguientes comprobaciones: Comprobación de la entrada del pedido: verificamos que el producto recibido coincide con el producto que hemos pedido. Comprobación de daños visibles: revisamos visualmente el producto para detectar cualquier daño o defecto evidente. Verificación de cualquier diferencia importante entre la página del producto y el producto recibido: comprobamos que el producto recibido coincide con la descripción y las especificaciones que aparecen en la página del producto.",
          ],
          imageUrl: bannerImages["bannerBg8"]!,
        ),
      ],
    );
  }

  Widget _buildSection5({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso5",
      title: "Paso 5 Presupuesto final de envío y pago",
      content:
          "Después de que nuestro equipo chino empaquete la mercancía, le enviaremos un presupuesto final de envío basado en el método de envío que haya seleccionado previamente.",
      children: [
        _buildBannerWrapper(
          title: "·Pago del flete",
          content: [
            "Los gastos de envío tendrán que esperar hasta que la mercancía llegue al almacén y se vuelva a enviar un presupuesto de envío exacto. Una vez que nuestro equipo haya realizado un presupuesto definitivo de los gastos de envío, recibirá un correo electrónico con los datos de envío y un enlace a la página de pago.",
            "El desglose de costes del envío incluye: Coste transporte internacional、Aranceles、IVA、Envío nacional",
          ],
          imageUrl: bannerImages["bannerBg7"]!,
        ),
      ],
    );
  }

  Widget _buildSection6({Key? key}) {
    return _buildSection(
      key: key,
      id: "Paso6",
      title: "Paso 6 Envío, importación y entrega del pedido",
      content:
          "La mercancía se importará a su país por el medio de transporte que elija. Una vez realizado el despacho de aduanas, nuestro socio logístico se encargará de entregar la mercancía en su dirección de entrega.",
      isBg: true,
      children: [
        _buildBannerWrapper(
          title: "·Envío internacional",
          content: [
            "Nuestro equipo se encargará de organizar el transporte internacional de su envío, y usted podrá consultar la información logística de la carga en cualquier momento.",
          ],
          imageUrl: bannerImages["bannerBg9"]!,
        ),
        _buildBannerWrapper(
          title: "·Despacho de aduanas",
          content: [
            "Al llegar a su país, las mercancías deben pasar por trámites aduaneros. Todos los trámites correspondientes y procedimientos de pago de impuestos serán tramitados por Chilat shop. En este punto Chilat shop se encarga por ti de todos los tramites y burocracias.",
          ],
          imageUrl: bannerImages["bannerBg10"]!,
        ),
        _buildBannerWrapper(
          title: "·Envío nacional y entrega",
          content: [
            "La mercancía se almacenará en los almacenes de nuestros socios logísticos nacionales, que se encargarán de enviar el pedido a la dirección de entrega seleccionada.",
          ],
          imageUrl: bannerImages["bannerBg11"]!,
        ),
      ],
    );
  }

  Widget _buildSection({
    Key? key,
    required String id,
    required String title,
    required String content,
    required List<Widget> children,
    bool isBg = false,
  }) {
    return Container(
      key: key,
      padding: EdgeInsets.symmetric(vertical: 30.sp, horizontal: 16.sp),
      color: isBg ? const Color(0xFFF1EDED) : Colors.white,
      child: Column(
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 24.sp,
              fontWeight: FontWeight.w500,
              color: const Color(0xFFDB2221),
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 14.sp),
          Text(
            content,
            style: TextStyle(
              fontSize: 15.sp,
              height: 2,
            ),
            // textAlign: TextAlign.center,
          ),
          SizedBox(height: 18.sp),
          ...children,
        ],
      ),
    );
  }

  Widget _buildBannerWrapper({
    required String title,
    required List<String> content,
    required String imageUrl,
    String? warning,
    IconData? warningIcon,
  }) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 10.sp),
      margin: EdgeInsets.only(bottom: 25.sp),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: 20.sp,
              fontWeight: FontWeight.w500,
            ),
          ),
          SizedBox(height: 18.sp),
          ...content.map((text) => Padding(
                padding: EdgeInsets.only(bottom: 18.sp),
                child: Text(
                  text,
                  style: TextStyle(
                    fontSize: 14.sp,
                    height: 2.14,
                  ),
                ),
              )),
          Image.network(
            imageUrl,
            width: double.infinity,
            fit: BoxFit.contain,
          ),
          if (warning != null) ...[
            SizedBox(height: 30.sp),
            Container(
              padding: EdgeInsets.all(16.sp),
              decoration: BoxDecoration(
                border: Border.all(color: const Color(0xFFE2E2E2)),
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: Column(
                children: [
                  if (warningIcon != null)
                    Icon(
                      warningIcon,
                      size: 42.sp,
                      color: const Color(0xFFDB2221),
                    ),
                  if (warningIcon == Icons.check_circle) ...[
                    SizedBox(height: 8.sp),
                    Text(
                      "Compras",
                      style: TextStyle(
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                  SizedBox(height: 8.sp),
                  Text(
                    warning,
                    style: TextStyle(
                      fontSize: 14.sp,
                      height: 1.86,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }
}
