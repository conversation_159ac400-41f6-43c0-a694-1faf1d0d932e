import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/country_drawer.dart';
import 'package:chilat2_mall_app/models/basis.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'mine_controller.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/pages/mine/mine_inquiry_page.dart';
import 'package:chilat2_mall_app/utils/global.dart';
import 'package:chilat2_mall_app/utils/auth_helper.dart';
import 'package:cached_network_image/cached_network_image.dart';

class MinePage extends StatefulWidget {
  const MinePage({super.key});

  @override
  State<MinePage> createState() => _MinePageState();
}

class _MinePageState extends State<MinePage> {
  final MineController controller = Get.put(MineController());
  SiteListModel? siteData;

  @override
  void initState() {
    super.initState();
    onSiteData();
  }

  // 获取国家数据
  Future<void> onSiteData() async {
    try {
      SiteListModel? res = await Global.getSiteData();
      if (res != null) {
        setState(() {
          siteData = res;
        });
      }
    } catch (e) {
      // 获取国家数据失败，使用默认值
    }
  }

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
        backgroundColor: Color(0xFFf2f2f2), // 设置背景色
        body: Obx(() => controller.loading.value
            ? LoadingWidget()
            : Column(
                children: [
                  Expanded(
                      child: SingleChildScrollView(
                    child: Column(
                      children: [
                        Obx(() => Global.isLogin.value &&
                                controller.userInfo.value != null
                            ? _buildUserHeader(
                                context, controller.userInfo.value!)
                            : _buildLoginRegisterButtons(context)),
                        _buildUserInfoSection(context),
                        if (Global.isLogin.value) _buildLogoutButton(context),
                      ],
                    ),
                  )),
                  AppTabbar(currentIndex: 4) // 我的页面是第5个tab (index=4)
                ],
              )));
  }

  Widget _buildUserHeader(BuildContext context, dynamic userInfo) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.0.sp),
      height: 140.h,
      decoration: BoxDecoration(
        gradient: RadialGradient(
          center: Alignment.centerLeft,
          colors: [Color(0xFFE72528), Color(0xFFF13D33), Color(0xFFFC573F)],
        ),
      ),
      child: Stack(
        children: [
          Center(
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.account_circle, size: 60.sp, color: Colors.white),
                SizedBox(width: 16.0.sp),
                Expanded(
                  child: Text(
                    userInfo['email'] ?? '',
                    style: TextStyle(color: Colors.white, fontSize: 16.sp),
                    overflow: TextOverflow.ellipsis,
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            top: 4.0.sp,
            right: 0.sp,
            child: IconButton(
              icon: Icon(Icons.settings, color: Colors.white),
              onPressed: () {
                Get.toNamed(AppRoutes.MineSettingPage);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoginRegisterButtons(BuildContext context) {
    return Container(
      width: double.infinity,
      height: 200.sp,
      decoration: BoxDecoration(
        image: DecorationImage(
          image: AssetImage('assets/images/mine/unLogin.png'),
          fit: BoxFit.cover,
        ),
      ),
      child: Padding(
        padding: EdgeInsets.only(left: 14.0.sp, top: 92.0.sp),
        child: Row(
          children: [
            ElevatedButton(
              onPressed: () => AuthHelper.showLoginModal(context,
                  redirectRoute: AppRoutes.MinePage),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.black,
                foregroundColor: Colors.white,
                padding:
                    EdgeInsets.symmetric(horizontal: 18.0.sp, vertical: 8.0.sp),
                minimumSize: Size(0, 32.sp),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(34.0),
                ),
              ),
              child: Text(
                I18n.of(context)?.translate('cm_common_login') ?? '登录',
                style: TextStyle(fontSize: 14.0.sp),
              ),
            ),
            SizedBox(width: 8.0.sp),
            ElevatedButton(
              onPressed: () => AuthHelper.showRegisterModal(context),
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.white,
                foregroundColor: Theme.of(context).primaryColor,
                padding:
                    EdgeInsets.symmetric(horizontal: 18.0.sp, vertical: 8.sp),
                minimumSize: Size(0, 32.sp),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(34.0),
                ),
              ),
              child: Text(
                I18n.of(context)?.translate('cm_common_register') ?? '注册',
                style: TextStyle(fontSize: 14.0.sp),
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfoSection(BuildContext context) {
    return Transform.translate(
      offset: Offset(0, -20.0.sp),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 14.0.sp),
        child: Column(
          children: [
            Obx(() {
              if (Global.isLogin.value && controller.userInfo.value != null) {
                return _buildUserInquiryItem(
                    context, controller.userInfo.value!);
              }
              return SizedBox.shrink();
            }),
            _buildUserInfoItem(
              context,
              'cm_order.myOrder',
              '',
              showTrailing: true,
              onTap: () => Get.toNamed(AppRoutes.MineOrderPage),
            ),
            _buildUserInfoItem(
              context,
              'cm_user.invite',
              I18n.of(context)?.translate('cm_user.inviteSubtitle') ?? '',
              icon: SvgPicture.asset(
                'assets/images/mine/invite.svg',
                width: 20.sp,
                height: 20.sp,
              ),
              onTap: () => Get.toNamed(AppRoutes.MineInvitePage),
            ),
            _buildUserInfoItem(
              context,
              'cm_user.inquiry',
              '',
              icon: SvgPicture.asset(
                'assets/images/mine/email.svg',
                width: 20.sp,
                height: 20.sp,
              ),
              onTap: () => Get.toNamed(AppRoutes.MineInquiryPage),
            ),
            _buildUserInfoItem(
              context,
              'cm_bar.myCoupon',
              '',
              icon: SvgPicture.asset(
                'assets/images/mine/coupon.svg',
                width: 20.sp,
                height: 20.sp,
              ),
              onTap: () => Get.toNamed(AppRoutes.MineCouponPage),
            ),
            _buildUserInfoItem(
              context,
              'cm_user.direction',
              '',
              icon: SvgPicture.asset(
                'assets/images/mine/address.svg',
                width: 20.sp,
                height: 20.sp,
              ),
              onTap: () => Get.toNamed(AppRoutes.MineAddressPage),
            ),
            _buildCountrySelectItem(context),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInquiryItem(BuildContext context, dynamic userInfo) {
    return Container(
      alignment: Alignment.center,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4.0.sp),
      ),
      padding: EdgeInsets.symmetric(vertical: 15.0.sp, horizontal: 15.0.sp),
      margin: EdgeInsets.only(bottom: 8.0.sp),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          GestureDetector(
            onTap: () {
              Navigator.push(
                context,
                MaterialPageRoute(builder: (context) => MineInquiryPage()),
              );
              // 跳转到咨询页面
            },
            child: Column(
              children: [
                Text(
                  userInfo['inquiryCount']?.toString() ?? '0',
                  style:
                      TextStyle(fontSize: 20.0.sp, fontWeight: FontWeight.w500),
                ),
                Text(
                  I18n.of(context)?.translate('cm_user.consultas') ?? '咨询',
                  style: TextStyle(
                    fontSize: 20.0.sp,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoItem(
    BuildContext context,
    String titleKey,
    String subtitle, {
    bool showTrailing = false,
    Widget? icon,
    VoidCallback? onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Card(
        color: Colors.white,
        margin: EdgeInsets.symmetric(vertical: 4.0.sp),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.0.sp, horizontal: 15.0.sp),
          child: Row(
            children: [
              if (icon != null) icon,
              if (icon != null) SizedBox(width: 10.0.sp),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      I18n.of(context)?.translate(titleKey) ?? '',
                      style: TextStyle(fontSize: 16.sp),
                    ),
                    if (subtitle.isNotEmpty)
                      Text(
                        subtitle,
                        style: TextStyle(fontSize: 14.sp, color: Colors.grey),
                      ),
                  ],
                ),
              ),
              if (showTrailing)
                Icon(
                  Icons.arrow_forward_ios,
                  size: 16.sp,
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLogoutButton(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(10.0.sp),
      child: SizedBox(
        width: double.infinity,
        child: ElevatedButton(
          onPressed: () async {
            final successText =
                I18n.of(context)?.translate('cm_app.success') ?? '';
            final logoutText =
                I18n.of(context)?.translate('cm_app.logOut') ?? '';

            await controller.logout();
            await Global.setCartList();
            Get.snackbar(
              successText,
              logoutText,
              colorText: Colors.black,
              backgroundColor: Colors.white,
              snackPosition: SnackPosition.TOP,
              duration: const Duration(seconds: 2),
            );
          },
          style: ElevatedButton.styleFrom(
            padding: EdgeInsets.symmetric(vertical: 12.sp),
            backgroundColor: Colors.white,
            foregroundColor: Colors.black,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(4.0.sp),
            ),
          ),
          child: Text(
            I18n.of(context)?.translate('cm_common_loginOut') ?? 'Salir',
            style: TextStyle(fontSize: 14.sp),
          ),
        ),
      ),
    );
  }

  // 构建国家选择项
  Widget _buildCountrySelectItem(BuildContext context) {
    return GestureDetector(
      onTap: () {
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          isDismissible: true,
          enableDrag: false,
          builder: (BuildContext context) {
            return SizedBox(
              height: MediaQuery.of(context).size.height * 0.7,
              child: CountrySelectDrawer(
                onCountrySelect: (SiteListModel? siteInfo) {
                  setState(() {
                    siteData = siteInfo;
                    Global.setSiteData(siteInfo);
                  });
                },
                onCloseDrawer: () {
                  Get.back();
                },
              ),
            );
          },
        );
      },
      child: Card(
        color: Colors.white,
        margin: EdgeInsets.symmetric(vertical: 4.0.sp),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4.0),
        ),
        child: Padding(
          padding: EdgeInsets.symmetric(vertical: 15.0.sp, horizontal: 15.0.sp),
          child: Row(
            children: [
              SvgPicture.asset(
                'assets/images/mine/address.svg',
                width: 20.sp,
                height: 20.sp,
              ),
              SizedBox(width: 10.0.sp),
              Text(
                I18n.of(context)?.translate('cm_common.deliveryTo') ??
                    'Entregar a:',
                style: TextStyle(fontSize: 16.sp),
              ),
              SizedBox(width: 8.sp),
              if (siteData != null) ...[
                CachedNetworkImage(
                  height: 14.sp,
                  width: 20.sp,
                  fit: BoxFit.cover,
                  imageUrl: siteData?.logo ?? '',
                  placeholder: (context, url) =>
                      const CircularProgressIndicator(),
                  errorWidget: (context, url, error) => Container(
                    height: 14.sp,
                    width: 20.sp,
                    color: Colors.grey[300],
                    child: Icon(Icons.flag, size: 12.sp),
                  ),
                ),
                SizedBox(width: 4.sp),
                Text(
                  siteData?.code ?? '',
                  style:
                      TextStyle(fontSize: 15.sp, fontWeight: FontWeight.w500),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }
}
