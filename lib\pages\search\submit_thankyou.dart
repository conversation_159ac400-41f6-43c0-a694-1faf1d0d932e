import 'package:chilat2_mall_app/components/components.dart';
import 'package:chilat2_mall_app/components/search_header.dart';
import 'package:chilat2_mall_app/router/app_pages.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

class SearchSubmitThankYou extends StatelessWidget {
  const SearchSubmitThankYou({super.key});

  @override
  Widget build(BuildContext context) {
    return AppScaffold(
      backgroundColor: Color(0xFFFAFAFA),
      body: Column(
        children: [
          // 顶部搜索栏 - 白色背景
          Container(
            color: Colors.white,
            child: SearchHeader(showHomeIcon: true),
          ),

          // 主要内容区域
          Expanded(
            child: Container(
              constraints: BoxConstraints(
                  minHeight: MediaQuery.of(context).size.height * 0.5),
              padding: EdgeInsets.only(top: 26, left: 23, right: 23),
              child: Column(
                // mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // 成功图标
                  SvgPicture.asset(
                    'assets/images/search/find-submitted.svg',
                    width: 218,
                  ),

                  SizedBox(height: 26),

                  // 成功标题
                  Text(
                    I18n.of(context)?.translate("cm_search.sentSuccess") ??
                        "¡Enviado exitosamente!",
                    style: TextStyle(
                      fontSize: 26.sp,
                      fontWeight: FontWeight.w500,
                      color: Color(0xFF11263B),
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 12),

                  // 成功提示
                  Text(
                    I18n.of(context)?.translate("cm_search.sentSuccessTip") ??
                        "Una vez que encontremos la mercancía, nos pondremos en contacto con usted.",
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: Color(0xFF666666),
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),

                  SizedBox(height: 26),
                  // 分隔线
                  Container(
                    width: double.infinity,
                    height: 0.5,
                    color: Color(0xFFD9D9D9),
                  ),
                  SizedBox(height: 18),

                  // 底部按钮区域
                  Column(
                    children: [
                      // 继续找货按钮（深色）
                      GestureDetector(
                        onTap: () {
                          NavigatorUtil.pushNamed(
                              context, AppRoutes.SearchLooking);
                        },
                        child: Container(
                          width: double.infinity,
                          height: 42,
                          decoration: BoxDecoration(
                            color: Color(0xFF11263B),
                            borderRadius: BorderRadius.circular(24),
                          ),
                          child: Center(
                            child: Text(
                              I18n.of(context)
                                      ?.translate("cm_search.sendNewRequest") ??
                                  "Sigue ayudándome a buscar productos",
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Colors.white,
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 12),

                      // 返回首页按钮（浅色）
                      GestureDetector(
                        onTap: () {
                          NavigatorUtil.pushNamed(context, AppRoutes.HomePage);
                        },
                        child: Container(
                          width: double.infinity,
                          height: 42,
                          decoration: BoxDecoration(
                            border: Border.all(
                              color: Color(0xFFDDDDDD),
                              width: 1,
                            ),
                            borderRadius: BorderRadius.circular(24),
                            color: Colors.white,
                          ),
                          child: Center(
                            child: Text(
                              I18n.of(context)
                                      ?.translate("cm_search.goToHome") ??
                                  "Volver al inicio",
                              style: TextStyle(
                                fontSize: 16.sp,
                                color: Color(0xFF333333),
                              ),
                            ),
                          ),
                        ),
                      ),

                      SizedBox(height: 50.h),
                    ],
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
