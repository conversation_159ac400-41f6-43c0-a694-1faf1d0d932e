import 'package:chilat2_mall_app/pages/article/about_us.dart';
import 'package:chilat2_mall_app/pages/article/frequently_questions.dart';
import 'package:chilat2_mall_app/pages/article/invited_reward_page.dart';
import 'package:chilat2_mall_app/pages/article/quick_guide_page.dart';
import 'package:chilat2_mall_app/pages/home/<USER>';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/my_navigator.dart';
import 'package:flutter/material.dart';

class NewsList extends StatelessWidget {
  const NewsList({super.key});

  List<NewsItem> brandList(BuildContext context) => [
        NewsItem(
          icon: Icons.privacy_tip,
          title: I18n.of(context)?.translate('cm_news.aboutUs'),
          page: AboutUsPage(),
          bgcolor: Colors.red.shade800,
        ),
        NewsItem(
          icon: Icons.explore,
          title: I18n.of(context)?.translate('cm_news.quickGuide'),
          page: QuickGuidePage(),
          bgcolor: Colors.red.shade200,
        ),
        NewsItem(
          icon: Icons.help,
          title: I18n.of(context)?.translate('cm_news.askedQuestions'),
          page: FrequentlyQuestionsPage(),
          bgcolor: Colors.orange.shade800,
        ),
        NewsItem(
          icon: Icons.paid_sharp,
          title: I18n.of(context)?.translate('cm_news.invitedReward'),
          page: InvitedRewardPage(),
          bgcolor: Colors.orange.shade300,
        ),
      ];

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      color: Colors.white,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: brandList(context).length,
        itemBuilder: (context, index) {
          return _buildBrandItem(context, brandList(context)[index]);
        },
      ),
    );
  }

  Widget _buildBrandItem(BuildContext context, NewsItem item) {
    return Container(
      width: 130.0, // 每个列表项的宽度
      margin: EdgeInsets.only(right: 8.0),
      child: Card(
        elevation: 4,
        color: item.bgcolor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        child: InkWell(
          onTap: () {
            // 绑定点击事件
            MyNavigator.push(item.page, context: context);
          },
          child: Row(
            children: <Widget>[
              Expanded(
                flex: 2, // 左侧占比三分之二
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.only(left: 12, top: 6, bottom: 6),
                    child: Text(
                      item.title ?? "",
                      style: TextStyle(
                        color: Colors.white,
                        fontSize: 12.0,
                      ),
                    ),
                  ),
                ),
              ),
              Expanded(
                flex: 1, // 右侧占比三分之一
                child: Center(
                  child: Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(
                      item.icon ?? Icons.ac_unit,
                      size: 24.0,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
