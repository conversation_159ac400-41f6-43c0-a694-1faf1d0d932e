import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:chilat2_mall_app/utils/i18n.dart';
import 'package:chilat2_mall_app/utils/mixin.dart';
import 'package:dotted_border/dotted_border.dart';
import 'package:chilat2_mall_app/styles/styles.dart';

class CouponDetail extends StatefulWidget {
  final String title;
  final dynamic amount;
  final List<dynamic> couponList;
  final String couponType;

  const CouponDetail({
    Key? key,
    required this.title,
    required this.amount,
    required this.couponList,
    required this.couponType,
  }) : super(key: key);

  @override
  State<CouponDetail> createState() => _CouponDetailState();
}

class _CouponDetailState extends State<CouponDetail> {
  String decimalToPercentage(dynamic discount) {
    if (discount == null) return '0%';
    final value = double.tryParse(discount.toString());
    if (value == null) return '0%';
    return '${(value * 100).toStringAsFixed(0)}%';
  }

  void _showCouponDetails() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.only(
            topLeft: Radius.circular(16.r),
            topRight: Radius.circular(16.r),
          ),
        ),
        child: Column(
          children: [
            // 标题栏
            Padding(
              padding: EdgeInsets.symmetric(vertical: 14.h),
              child: Stack(
                children: [
                  Center(
                    child: Text(
                      widget.couponType == 'COUPON_TYPE_PRODUCT'
                          ? I18n.of(context)
                                  ?.translate('cm_coupon.productCoupons') ??
                              ''
                          : I18n.of(context)
                                  ?.translate('cm_coupon.commissionCoupons') ??
                              '',
                      style: TextStyle(
                        fontSize: 18.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  Positioned(
                    right: 13.w,
                    top: 0,
                    child: GestureDetector(
                      onTap: () => Navigator.pop(context),
                      child: Icon(
                        Icons.close,
                        size: 24.sp,
                        color: const Color(0xFF222222),
                      ),
                    ),
                  ),
                ],
              ),
            ),

            // 优惠券列表
            Expanded(
              child: SingleChildScrollView(
                child: Padding(
                  padding: EdgeInsets.symmetric(horizontal: 16.w),
                  child: Column(
                    children:
                        widget.couponList.asMap().entries.map<Widget>((entry) {
                      final index = entry.key;
                      final coupon = entry.value;
                      final isLast = index == widget.couponList.length - 1;
                      return Container(
                        padding: EdgeInsets.only(bottom: 10.h),
                        margin: EdgeInsets.only(bottom: 10.h),
                        decoration: BoxDecoration(
                          border: isLast
                              ? null
                              : Border(
                                  bottom: BorderSide(
                                    color: const Color(0xFFF2F2F2),
                                    width: 1.h,
                                  ),
                                ),
                        ),
                        child: Row(
                          children: [
                            Container(
                              width: 78.w,
                              child: DottedBorder(
                                color: const Color(0xFF4D4D4D),
                                strokeWidth: 1.w,
                                borderType: BorderType.RRect,
                                radius: Radius.circular(4.r),
                                padding: EdgeInsets.symmetric(vertical: 14.h),
                                child: Center(
                                  child: Text(
                                    coupon['couponWay'] == 'COUPON_WAY_DISCOUNT'
                                        ? '${decimalToPercentage(coupon['discount'])} ${I18n.of(context)?.translate('cm_coupon.discount')?.toLowerCase() ?? ''}'
                                        : setNewUnit(num.tryParse(
                                                coupon['preferentialAmount']
                                                        ?.toString() ??
                                                    '0') ??
                                            0),
                                    style: TextStyle(
                                      fontSize: 14.sp,
                                    ),
                                  ),
                                ),
                              ),
                            ),
                            SizedBox(width: 7.w),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    _getCouponConditionText(context, coupon),
                                    style: TextStyle(
                                      fontSize: 13.sp,
                                    ),
                                  ),
                                  if (coupon['couponWay'] ==
                                          'COUPON_WAY_DISCOUNT' &&
                                      coupon['preferentialAmount'] != null &&
                                      coupon['preferentialAmount'] > 0)
                                    Padding(
                                      padding: EdgeInsets.only(top: 7.h),
                                      child: Text(
                                        '${I18n.of(context)?.translate('cm_coupon.upToMoney') ?? ''} ${setNewUnit(num.tryParse(coupon['preferentialAmount'].toString()) ?? 0)}',
                                        style: TextStyle(
                                          fontSize: 12.sp,
                                          color: const Color(0xFF333333),
                                        ),
                                      ),
                                    ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      );
                    }).toList(),
                  ),
                ),
              ),
            ),

            // 底部按钮
            Padding(
              padding: EdgeInsets.all(16.w),
              child: SizedBox(
                width: double.infinity,
                height: 40.h,
                child: ElevatedButton(
                  onPressed: () => Navigator.pop(context),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primaryColor,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(25.r),
                    ),
                  ),
                  child: Text(
                    I18n.of(context)?.translate('cm_order.closeDetail') ?? '',
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w500,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              SizedBox(
                child: Text(
                  '${widget.title}:',
                  style: TextStyle(
                    fontSize: 13.sp,
                    color: const Color(0xFF333333),
                  ),
                ),
              ),
              GestureDetector(
                onTap: _showCouponDetails,
                child: Container(
                  margin: EdgeInsets.only(left: 8.w),
                  padding: EdgeInsets.symmetric(
                    horizontal: 2.sp,
                    vertical: 1.sp,
                  ),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFEF7F8),
                    border: Border.all(
                      color: const Color(0xFFFF94A8),
                    ),
                    borderRadius: BorderRadius.circular(3.r),
                  ),
                  child: Row(
                    children: [
                      Text(
                        I18n.of(context)?.translate('cm_coupon.viewCoupons') ??
                            '',
                        style: TextStyle(
                          fontSize: 10.sp,
                          color: const Color(0xFFFF4056),
                        ),
                      ),
                      Icon(
                        Icons.keyboard_arrow_down,
                        size: 14.sp,
                        color: const Color(0xFFFF4056),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          Text(
            setUnit(num.tryParse(widget.amount.toString()) ?? 0),
            style: TextStyle(
              fontSize: 13.sp,
              color: const Color(0xFF333333),
            ),
          ),
        ],
      ),
    );
  }

  String _getCouponConditionText(BuildContext context, dynamic coupon) {
    switch (coupon['couponUseConditionsType']) {
      case 'FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumRequired') ?? ''} ${setNewUnit(num.tryParse(coupon['useConditionsAmount']?.toString() ?? '0') ?? 0)}';
      case 'EVERY_FULL':
        return '${I18n.of(context)?.translate('cm_coupon.minimumUnmet') ?? ''} ${setNewUnit(num.tryParse(coupon['useConditionsAmount']?.toString() ?? '0') ?? 0)} ${I18n.of(context)?.translate('cm_coupon.minimumUnmetCost') ?? ''}';
      case 'UNLIMITED':
        return I18n.of(context)?.translate('cm_coupon.noLimit') ?? '';
      default:
        return '';
    }
  }
}
