import 'package:chilat2_mall_app/styles/styles.dart';
import 'package:chilat2_mall_app/utils/utils.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CouponCard extends StatelessWidget {
  // final List<dynamic> couponList;
  final dynamic coupon;

  const CouponCard({super.key, required this.coupon});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 12.sp),
      width: 172.sp, // 优惠券宽度 + x计数器宽度
      height: 58.sp,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 122.sp,
            height: 58.sp,
            decoration: BoxDecoration(
              image: DecorationImage(
                image: AssetImage(
                  coupon['couponType'] == 'COUPON_TYPE_COMMISSION'
                      ? 'assets/images/marketing/commissionCoupon.png'
                      : 'assets/images/marketing/productCoupon.png',
                ),
                fit: BoxFit.cover,
              ),
            ),
            padding: EdgeInsets.fromLTRB(24.sp, 10.sp, 4.sp, 4.sp),
            child: Column(
              children: [
                Container(
                  height: 28.sp,
                  alignment: Alignment.center,
                  margin: EdgeInsets.only(bottom: 4.sp),
                  child: coupon['couponWay'] == 'COUPON_WAY_DISCOUNT'
                      ? Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Text(
                              (coupon['discount'] * 100).toStringAsFixed(0) +
                                  '%',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 14.sp,
                                fontWeight: FontWeight.w500,
                                height: 1.0,
                              ),
                            ),
                            Transform.scale(
                              scale: 0.84,
                              alignment: Alignment.topRight,
                              child: Text(
                                I18n.of(context)
                                        ?.translate('cm_coupon.discount') ??
                                    '',
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 10.sp,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                          ],
                        )
                      : Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Transform.rotate(
                              angle: -90 * 3.1415927 / 180,
                              child: Text(
                                currencyUnit,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: 12.sp,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                            Text(
                              '\$${setNewUnit(coupon['preferentialAmount'], true)}',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w600,
                                height: 1.0,
                              ),
                            ),
                          ],
                        ),
                ),
                Container(
                  padding: EdgeInsets.only(left: 12.sp),
                  child: Transform.scale(
                    scale: 0.9,
                    alignment: Alignment.topLeft,
                    child: Text(
                      (coupon['couponType'] == 'COUPON_TYPE_PRODUCT'
                              ? I18n.of(context)
                                      ?.translate('cm_coupon.productCoupon') ??
                                  ''
                              : I18n.of(context)?.translate(
                                      'cm_coupon.commissionCoupon') ??
                                  '')
                          .toUpperCase(),
                      style: TextStyle(
                          color: Colors.white,
                          fontSize: 10.5.sp,
                          height: 1.0,
                          letterSpacing: -0.5,
                          fontWeight: FontWeight.w600),
                      softWrap: false,
                      overflow: TextOverflow.visible,
                    ),
                  ),
                )
              ],
            ),
          ),
          SizedBox(width: 2.sp),
          Row(
            children: [
              Text(
                'x',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  letterSpacing: -1.5,
                  fontSize: 15.sp,
                  height: 1.0,
                ),
              ),
              SizedBox(width: 2.sp),
              Text(
                '${coupon['count']}',
                style: TextStyle(
                  color: AppColors.primaryColor,
                  letterSpacing: -1.5,
                  fontSize: 15.sp,
                  height: 1.0,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
