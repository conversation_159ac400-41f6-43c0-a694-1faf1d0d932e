import 'package:chilat2_mall_app/models/common.dart';

class LangConfigData {
  Map<String, String> trans;

  LangConfigData({required this.trans});
  // 从 Map 创建 对象
  factory LangConfigData.fromJson(dynamic json) {
    Map<String, String> trans = {};
    json?['trans'].forEach((key, value) {
      trans[key] = value.toString();
    });
    return LangConfigData(trans: trans);
  }
}

class LangConfigResp {
  Result result;
  LangConfigData data;

  LangConfigResp({required this.result, required this.data});

  factory LangConfigResp.fromJson(Map<String, dynamic> json) {
    Result result = Result.fromJson(json?['result']);
    LangConfigData data = LangConfigData.fromJson(json?['data']);
    return LangConfigResp(result: result, data: data);
  }
}
