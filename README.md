### 常见问题

- Image.asset 不太适合展示大图

### 打包参考

- [构建和发布为 Android 应用](https://docs.flutter.cn/deployment/android)
- [Flutter Android Studio 打包 Apk 包](https://blog.csdn.net/qq_33449429/article/details/132169663)

### flutter_launcher_icons

[应用图标](https://blog.csdn.net/zhutao_java/article/details/103605526)

- 在 flutter 工程中新建 assets 目录。
- assets 放入一张 1024\*1024 的图片。
- flutter pub add dev:flutter_launcher_icons
- pubspec.yaml 文件中增加如下配置

```yaml
flutter_icons:
  android: "ic_launcher"
  ios: false
  image_path: "assets/launcher_icon.png"
```

- 运行 flutter pub run flutter_launcher_icons:main,自动生成各种尺寸的图标。
- 删除 android/app/src/main/res/mipmap-anydpi-v26/ 目录下所有文件。

### 密钥

```
 keytool -genkey -v -keystore chilat.keystore -alias chilat -keyalg RSA -keysize 2048 -validity 10000 -storepass 123654 -keypass 123654

keytool -importkeystore -srckeystore C:/workspace/config/chilat/key.jks -destkeystore C:/workspace/config/chilat/key.jks -deststoretype pkcs12

- keystore  签名文件的路径和名字
- storetype  生成密钥库的类型
- keyalg  生成密钥库算法
- keysize  生成密钥库的长度
- validity  生成密钥库的有效期
- alias  生成密钥库的别名

将生成的chilat.keystore文件复制到android/app目录下。****
```

### android/app/build.gradle 中配置签名

```
def keystoreProperties = new Properties()
def keystorePropertiesFile = rootProject.file('key.properties')
if (keystorePropertiesFile.exists()) {
    keystoreProperties.load(new FileInputStream(keystorePropertiesFile))
}

android {
   ...
}
```

### android/app/src/main/AndroidManifest.xml 配置权限和应用名

```
<manifest xmlns:android="http://schemas.android.com/apk/res/android">
    <application
        android:label="[project]"
        ...
    </application>
    ...
    <!-- 这里配置权限 -->
    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
</manifest>
```

### 打包

[构建和发布为 Android 应用](https://docs.flutter.cn/deployment/android#add-a-launcher-icon)

```
flutter clean
flutter build apk --release --no-shrink
adb connect 127.0.0.1:7555
adb install build\\app\\outputs\\flutter-apk\\app-release.apk
```

### 设置 Splash Screen

```
将`android\app\src\main\res\drawable\launch_background.xml`文件中的注释内容释放：
<item>
    <bitmap
        android:gravity="center"
        android:src="@mipmap/launch_image" />
</item>
另外一种策略是通过: Future.delayed 延迟3秒后进入主程序
```

### 书籍推荐

- [flutter 实战-第二版](https://book.flutterchina.club/#%E7%AC%AC%E4%BA%8C%E7%89%88%E5%8F%98%E5%8C%96)

### protobuf&dart

- [protobuf](https://github.com/protocolbuffers/protobuf/releases)，下载对应平台版本，解压后，将 bin 目录添加到环境变量中
- 将 flutter/bin 和 flutter/bin/cache/dart-sdk/bin 添加到环境变量中
- powerShell 管理员身份执行: dart pub global activate protoc_plugin, 安装 protoc_plugin, 生成:C:\Users\<USER>\AppData\Local\Pub\Cache\bin,并添加到环境变量中
- protoc --dart_out={目标路径} --proto_path={依赖路径} {proto 文件路径}/xxx.proto
- 参考: https://blog.csdn.net/weixin_42779997/article/details/111320379

### 参考

- [flutter 官方文档](https://flutterchina.club/)
- [flutter 官方库](https://pub.dev/)
- [上拉加载和下拉刷新的组件](https://github.com/peng8350/flutter_pulltorefresh/blob/master/README_CN.md)
- [webview_flutter 的使用](https://juejin.cn/post/7196698315835260984)
- [flutter icons](https://fonts.google.com/icons)

### 库介绍

- network_info_plus: 获取网络信息, 需要原生支持
- image_picker: 图片选择

### 注意事项

- fromJson 的时候如果返回的字段不存在，可以用 key: json['key'] ?? false 来处理, 根据实际类型设置默认值

### 认证系统使用说明

项目中实现了一套完整的认证系统，包括登录、注册和修改密码功能。认证系统通过模态框的形式呈现，便于在任何页面中调用。

#### 登录功能

使用 `AuthHelper.showLoginModal` 方法显示登录模态框：

```dart
// 显示登录模态框
AuthHelper.showLoginModal(
  context,
  redirectRoute: '/mine', // 可选，登录成功后的重定向路由
  onAuthSuccess: () {
    // 可选，登录成功后的回调函数
    print('登录成功');
  },
);
```

#### 注册功能

使用 `AuthHelper.showRegisterModal` 方法显示注册模态框：

```dart
// 显示注册模态框
AuthHelper.showRegisterModal(
  context,
  redirectRoute: '/home', // 可选，注册成功后的重定向路由
  onAuthSuccess: () {
    // 可选，注册成功后的回调函数
    print('注册成功');
  },
);
```

#### 修改密码功能

使用 `AuthHelper.showModifyPasswordModal` 方法显示修改密码模态框：

```dart
// 显示修改密码模态框
AuthHelper.showModifyPasswordModal(
  context,
  redirectRoute: '/mine', // 可选，修改密码成功后的重定向路由
  onAuthSuccess: () {
    // 可选，修改密码成功后的回调函数
    print('修改密码成功');
  },
);
```

#### 认证检查

在需要用户登录的场景下，可以使用 `AuthHelper.requireAuth` 方法检查用户是否已登录，如未登录则自动显示登录模态框：

```dart
// 检查用户是否已登录，未登录则显示登录模态框
AuthHelper.requireAuth(
  context,
  redirectRoute: '/order', // 可选，登录成功后的重定向路由
).then((isAuthenticated) {
  if (isAuthenticated) {
    // 用户已登录或登录成功，执行需要认证的操作
    print('用户已认证，可以继续操作');
  } else {
    // 用户取消登录，执行其他操作
    print('用户未认证，无法继续操作');
  }
});
```

所有认证方法都返回 `Future<bool>`，可以通过返回值判断认证是否成功。

### AppScaffold 组件使用说明

AppScaffold 是一个自定义的脚手架组件，它扩展了 Flutter 的 Scaffold 组件，增加了滚动到顶部按钮和 WhatsApp 联系按钮等功能。

#### 基本用法

```dart
import 'package:chilat2_mall_app/components/app_scaffold.dart';

// 在 build 方法中使用
@override
Widget build(BuildContext context) {
  return AppScaffold(
    appBar: AppBar(
      title: Text('页面标题'),
      centerTitle: true,
    ),
    body: ListView.builder(
      controller: _scrollController, // 如果有自定义的 ScrollController
      itemCount: items.length,
      itemBuilder: (context, index) => ListTile(title: Text('Item $index')),
    ),
    showScrollToTopButton: true, // 启用滚动到顶部按钮
    scrollController: _scrollController, // 传入自定义的 ScrollController
  );
}
```

#### 参数说明

- `body`：必需参数，页面的主体内容。
- `appBar`：可选参数，页面的应用栏。
- `bottomNavigationBar`：可选参数，底部导航栏。
- `showWhatsAppButton`：是否显示 WhatsApp 联系按钮，默认为 true。
- `whatsAppPhoneNumber`：WhatsApp 联系电话号码。
- `showScrollToTopButton`：是否显示滚动到顶部按钮，默认为 false。
- `scrollController`：滚动控制器，如果不提供则自动创建。
- `scrollToTopThreshold`：显示滚动到顶部按钮的滚动阈值，默认为 300。
- `scrollToTopButtonBottom`：滚动到顶部按钮距离底部的距离，默认为 140。
- `scrollToTopButtonRight`：滚动到顶部按钮距离右侧的距离，默认为 12。
- `scrollToTopButtonSize`：滚动到顶部按钮的大小。
- `backgroundColor`：页面背景色。

### CommonWebViewPage 组件使用说明

CommonWebViewPage 是一个封装了 WebView 功能的页面组件，用于在应用内显示网页内容。

#### 基本用法

```dart
import 'package:chilat2_mall_app/components/common_webview_page.dart';

// 导航到 WebView 页面
Navigator.of(context).push(
  MaterialPageRoute(
    builder: (context) => CommonWebViewPage(
      url: 'https://example.com',
      title: '网页标题',
    ),
  ),
);
```

#### 参数说明

- `url`：必需参数，要加载的网页 URL。
- `title`：可选参数，页面标题，显示在 AppBar 中。
- `showAppBar`：是否显示应用栏，默认为 true。
- `showLoading`：是否显示加载指示器，默认为 true。
