import 'package:shared_preferences/shared_preferences.dart';

// Cookie管理器
class CookieManager {
  static const String _cookieKey = 'custom_cookies';
  static const String _tokenKey = 'auth_token';
  static const String _siteKey = 'site_id';

  static Future<void> saveToken(String token) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, token);
  }

  static Future<String?> getToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  static Future<void> clearToken() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
  }

  static Future<void> saveSiteID(String siteId) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_siteKey, siteId);
  }

  static Future<String?> getSite() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_siteKey);
  }

  static Future<void> saveCookies(Map<String, String> cookies) async {
    final prefs = await SharedPreferences.getInstance();
    final cookieString =
        cookies.entries.map((e) => '${e.key}=${e.value}').join('; ');
    await prefs.setString(_cookieKey, cookieString);
  }

  static Future<String?> getCookies() async {
    final prefs = await SharedPreferences.getInstance();
    final cookies = prefs.getString(_cookieKey) ?? '';
    final token = await getToken();
    final site = await getSite();

    if (token != null && site != null) {
      return 'token=$token; _lnsid=$site; $cookies';
    }
    return cookies;
  }

  static Future<void> clearAll() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_cookieKey);
    await prefs.remove(_tokenKey);
  }
}
